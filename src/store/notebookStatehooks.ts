import {useDispatch, useSelector} from 'react-redux';
import {IAppState, IAppDispatch} from './index';
import {
  setActiveCompute,
  setComputeId,
  updateComputeStatus,
  updateSessionId,
  updateKernelId,
  clearActiveCompute,
  setServerless,
  resetNotebookState
} from './notebookSlice';
import {GetComputeSessionStatusResult} from '@api/WorkArea';
import {ComputeResourceItem} from '@api/Compute';

// 使用 TypeScript 类型增强的 hooks
export const useAppDispatch = () => useDispatch<IAppDispatch>();
export const useAppSelector = <T>(selector: (state: IAppState) => T): T =>
  useSelector<IAppState, T>(selector);

// Notebook 相关的自定义 hook
export const useNotebook = () => {
  const dispatch = useAppDispatch();
  const activeCompute = useAppSelector((state) => state.notebookSlice.activeCompute);
  const computeId = useAppSelector((state) => state.notebookSlice.computeId);
  const connectStatus = useAppSelector((state) => state.notebookSlice.connectStatus);
  const sessionId = useAppSelector((state) => state.notebookSlice.sessionId);
  const kernelId = useAppSelector((state) => state.notebookSlice.kernelId);

  // 设置激活的计算资源
  const setNotebookActiveCompute = (compute: ComputeResourceItem | null) => {
    dispatch(setActiveCompute(compute));
  };

  // 更新计算资源的连接状态
  const updateNotebookComputeStatus = (status?: GetComputeSessionStatusResult['status']) => {
    dispatch(updateComputeStatus(status));
  };

  // 更新计算资源的会话 ID
  const updateNotebookSessionId = (sessionId: string) => {
    dispatch(updateSessionId(sessionId));
  };

  // 更新计算资源的内核 ID
  const updateNotebookKernelId = (kernelId: string) => {
    dispatch(updateKernelId(kernelId));
  };

  const updateNotebookComputeId = (computeId: string) => {
    dispatch(setComputeId(computeId));
  };

  // 清除计算资源
  const clearNotebookActiveCompute = () => {
    dispatch(clearActiveCompute());
  };

  // 获取 serverless 状态
  const serverless = useAppSelector((state) => state.notebookSlice.serverless);

  // 设置 serverless 模式
  const setNotebookServerless = (isServerless: boolean) => {
    dispatch(setServerless(isServerless));
  };

  const resetNotebookStates = () => {
    dispatch(resetNotebookState());
  };

  return {
    activeCompute,
    computeId,
    connectStatus,
    sessionId,
    kernelId,
    setNotebookActiveCompute,
    updateNotebookComputeStatus,
    updateNotebookSessionId,
    updateNotebookKernelId,
    clearNotebookActiveCompute,
    updateNotebookComputeId,
    serverless,
    setNotebookServerless,
    resetNotebookStates
  };
};
