import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {ComputeResourceItem} from '@api/Compute';
import {GetComputeSessionStatusResult} from '@api/WorkArea';

// 定义初始状态
interface NotebookState {
  // 激活的计算资源
  activeCompute: ComputeResourceItem | null;
  // 是否为无服务器模式
  serverless: boolean;
  // 计算资源ID
  computeId?: string;
  // 连接状态
  connectStatus?: GetComputeSessionStatusResult['status'] | '';
  // 会话ID
  sessionId?: string;
  // 内核ID
  kernelId?: string;
}

const initialState: NotebookState = {
  activeCompute: null,
  serverless: true,
  computeId: undefined,
  connectStatus: undefined,
  sessionId: undefined,
  kernelId: undefined
};

// 创建 slice
const notebookSlice = createSlice({
  name: 'notebook',
  initialState,
  reducers: {
    // 设置激活的计算资源
    setActiveCompute: (state, action: PayloadAction<ComputeResourceItem | null>) => {
      state.activeCompute = action.payload;
    },
    setComputeId: (state, action: PayloadAction<string>) => {
      state.computeId = action.payload;
    },
    // 更新连接状态
    updateComputeStatus: (state, action: PayloadAction<GetComputeSessionStatusResult['status']>) => {
      state.connectStatus = action.payload;
    },
    // 更新会话 ID
    updateSessionId: (state, action: PayloadAction<string>) => {
      state.sessionId = action.payload;
    },
    // 更新内核 ID
    updateKernelId: (state, action: PayloadAction<string>) => {
      state.kernelId = action.payload;
    },
    // 清除计算资源及相关状态
    clearActiveCompute: (state) => {
      state.activeCompute = null;
    },
    // 设置服务器模式
    setServerless: (state, action: PayloadAction<boolean>) => {
      state.serverless = action.payload;
    },
    resetNotebookState: (state) => {
      state.activeCompute = initialState.activeCompute;
      state.computeId = initialState.computeId;
      state.connectStatus = initialState.connectStatus;
      state.sessionId = initialState.sessionId;
      state.kernelId = initialState.kernelId;
    }
  }
});

// 导出 actions
export const {
  setActiveCompute,
  setComputeId,
  updateComputeStatus,
  updateSessionId,
  updateKernelId,
  clearActiveCompute,
  setServerless,
  resetNotebookState
} = notebookSlice.actions;

// 导出 reducer
export default notebookSlice.reducer;
