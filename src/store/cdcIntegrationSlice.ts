import {
  CdcJobType,
  CdcSinkConfig,
  CdcSourceConfig,
  CdcTableConfig,
  DirtyDataStrategyEnum,
  DmlDeleteStrategy,
  DmlInsertStrategy,
  DmlUpdateStrategy,
  IncrementPositionType,
  SinkConfigTarget,
  SinkNameRule,
  SinkTableType,
  SinkType,
  SourceChangeStrategy,
  SourceTypeEnum
} from '@api/integration/cdc-type';
import {CommonConfig, FieldType, JobDetailBase, JobType} from '@api/integration/type';
import {CreateStep} from '@pages/DataIntegration/CdcCollect/CdcCollectCreate/constants';
import {createSelector, createSlice} from '@reduxjs/toolkit';

export const initialFieldConfig = {
  name: '',
  type: FieldType.String,
  defaultValue: '',
  dbType: FieldType.String,
  nullable: false,
  description: '',
  precision: 0,
  scale: 0,
  isPrimaryKey: false,
  function: undefined,
  functionParameter: '',
  comment: '',
  isEdit: true
};

export const initialBaseConfig = {
  name: '',
  compute: {name: '', computeId: ''},
  type: JobType.CDC,
  description: '',
  cdcType: CdcJobType.BaseIncrement
};

export const initialTableConfig = {
  createTableStatement: '',
  dmlConfig: {
    delete: DmlDeleteStrategy.Delete,
    insert: DmlInsertStrategy.Insert,
    update: DmlUpdateStrategy.Update,
    logicalDeleteField: ''
  },
  isAutoCreated: 1,
  isTableCustomConfig: false,
  mapping: [],
  sinkFields: [],
  sinkPartitions: [],
  sinkTableComment: '',
  sinkTableName: '',
  sourceSchema: '',
  sourceTable: ''
};

export const initialSourceConfig = {
  enableRateLimit: true,
  incrementPosition: {
    position: '',
    type: IncrementPositionType.Timestamp
  },
  parallelism: 1,
  rateLimit: {
    flow: 0,
    records: 0
  },
  sourceChange: {
    onAddColumn: SourceChangeStrategy.Skip,
    onChangeColumn: SourceChangeStrategy.Skip,
    onChangeColumnComment: SourceChangeStrategy.Skip,
    onChangeTableComment: SourceChangeStrategy.Skip,
    onDeleteColumn: SourceChangeStrategy.Pause,
    onDeleteSource: SourceChangeStrategy.Pause,
    onRenameColumn: SourceChangeStrategy.Skip,
    onRenameTable: SourceChangeStrategy.Skip,
    onTruncateTable: SourceChangeStrategy.Skip
  },
  sourceConnectionId: '',
  sourceDatabase: '',
  sourceType: SourceTypeEnum.MySQL
};

export const initialSinkConfig = {
  dirtyDataStrategy: {
    dirtyDataVolume: '',
    enableDirtyDataWrite: false,
    maxDirtyRatio: '',
    strategy: DirtyDataStrategyEnum.Ignore,
    maxDirtyRow: 0
  },
  dmlConfig: {
    delete: DmlDeleteStrategy.Delete,
    insert: DmlInsertStrategy.Insert,
    update: DmlUpdateStrategy.Update,
    logicalDeleteField: ''
  },
  isAutoCreated: 1,
  sinkNameRule: SinkNameRule.Same,
  prefix: '',
  suffix: '',
  sinkType: SinkType.Iceberg,
  sinkTableType: SinkTableType.Managed,
  sinkName: '',
  sinkPath: '',
  target: SinkConfigTarget.Catalog
};

export interface CdcIntegrationState {
  baseConfig: JobDetailBase;
  sourceConfig: CdcSourceConfig;
  sinkConfig: CdcSinkConfig;
  commonConfig: CommonConfig;
  tableConfigs: CdcTableConfig[];
  step?: CreateStep;
  isReadOnly?: boolean;
  isJobStarted?: boolean;
}

const initialState: CdcIntegrationState = {
  baseConfig: initialBaseConfig,
  sourceConfig: initialSourceConfig,
  sinkConfig: initialSinkConfig,
  tableConfigs: [],
  commonConfig: {
    addedSinkFields: []
  },
  step: CreateStep.SourceSinkConfig,
  isReadOnly: false,
  isJobStarted: false
};

const cdcIntegrationSlice = createSlice({
  name: 'cdcIntegration',
  initialState,
  reducers: {
    updateStep: (state, action) => {
      state.step = action.payload;
    },
    updateReadOnly: (state, action) => {
      state.isReadOnly = action.payload;
    },
    updateBaseConfig: (state, action) => {
      state.baseConfig = action.payload;
    },
    updateCommonConfig: (state, action) => {
      state.commonConfig = action.payload;
    },
    updateSinkConfig: (state, action) => {
      state.sinkConfig = action.payload;
    },
    updateSourceConfig: (state, action) => {
      state.sourceConfig = action.payload;
    },
    updateTableConfigs: (state, action) => {
      state.tableConfigs = action.payload;
    },
    // 更新全部配置
    updateAllConfig: (state, action) => {
      return {...state, ...action.payload};
    },
    reset: () => {
      return initialState;
    }
  }
});

export const {
  updateBaseConfig,
  updateCommonConfig,
  updateSinkConfig,
  updateSourceConfig,
  updateTableConfigs,
  updateStep
} = cdcIntegrationSlice.actions;

export default cdcIntegrationSlice.reducer;
