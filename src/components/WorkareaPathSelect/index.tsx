import {WorkspaceContext} from '@pages/index';
import {Button, Input, Dropdown, Loading} from 'acud';
import React, {useContext, useState, useEffect} from 'react';

import {
  getFolderPath,
  getWorkspaceFileList,
  GetWorkspaceFileListResult,
  getWorkspaceFolderList,
  GetWorkspaceFolderListResult,
  getWorkspaceFileResult
} from '@api/WorkArea';
import IconSvg from '@components/IconSvg';
import BreadCrumb from '@pages/WorkArea/components/BreadCrumb';
import styles from './index.module.less';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

/**
 * 文件节点类型 文件、文件夹、NOTEBOOK
 */
export enum FileNodeTypeEnum {
  FILE = 'FILE',
  TRASH = 'TRASH',
  FOLDER = 'FOLDER',
  NOTEBOOK = 'NOTEBOOK'
}

export enum FileTypeEnum {
  ALL = 'ALL',
  HOME = 'HOME',
  USERS = 'USERS',
  USER = 'USER',
  TRASH = 'TRASH',
  NORMAL = 'NORMAL'
}

interface FilePathSelectWorkareaProps {
  value?: string;
  onChange?: (value: string) => void;
  mode?: 'folder' | 'file';
  canSelectFileExtensions?: string[];
  getContainer?: () => HTMLElement;
}

/**
 * 工作区目录路径选择器
 */
const FilePathSelectWorkarea: React.FC<FilePathSelectWorkareaProps> = ({
  value,
  onChange,
  mode = 'folder',
  canSelectFileExtensions = [],
  getContainer = () => document.body
}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [path, setPath] = useState<GetWorkspaceFolderListResult[]>([]);
  const [list, setList] = useState<GetWorkspaceFileListResult[]>([]);
  const [displayValue, setDisplayValue] = useState<string>('');
  const [filterText, setFilterText] = useState<string>('');
  const [originalList, setOriginalList] = useState<GetWorkspaceFileListResult[]>([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  async function changeFolder(item: GetWorkspaceFolderListResult) {
    setLoading(true);
    try {
      const pathRes = await getFolderPath({
        id: item.id,
        workspaceId
      });
      setPath(pathRes.result);
      const listRes = await getWorkspaceFileList({
        workspaceId,
        parentId: item?.id
      });

      let list = listRes.result
        .filter((item) => item.type !== 'TRASH')
        .filter((item) => item.nodeType === FileNodeTypeEnum.FOLDER || canSelectFile(item.name));

      if (mode === 'folder') {
        list = list.filter((item) => item.nodeType === FileNodeTypeEnum.FOLDER);
      }

      setOriginalList(list);
      setList(list);
      setFilterText(''); // 重置筛选文本
    } finally {
      setLoading(false);
    }
  }
  useEffect(() => {
    async function init() {
      const res = await getWorkspaceFolderList({
        workspaceId,
        parentId: ''
      });
      const home = res.result.find((item) => item.type === 'HOME');
      changeFolder(home);
    }
    init();
  }, []);

  // 图标映射
  const iconMap = {
    [FileNodeTypeEnum.FOLDER]: <IconSvg size={16} type="workarea-folder" color="#84868C" />,
    [FileNodeTypeEnum.FILE]: <IconSvg size={16} type="workarea-file" color="#151B26" />,
    [FileNodeTypeEnum.TRASH]: <IconSvg size={16} type="workarea-trash" color="#84868C" />,
    [FileNodeTypeEnum.NOTEBOOK]: <IconSvg size={16} type="nb-notebook" color="#2468F2" fill="none" />
  };

  function canSelectFile(fileName: string) {
    if (!canSelectFileExtensions?.length) {
      return true;
    }
    return canSelectFileExtensions.some((extension) => fileName.endsWith(extension));
  }

  const handleSelect = async (item: GetWorkspaceFileListResult) => {
    if (mode === 'folder') {
      if (item.nodeType === FileNodeTypeEnum.FOLDER) {
        onChange?.(item.id);
        setDisplayValue(item.path);
      }
    }
    if (mode === 'file') {
      if (item.nodeType !== FileNodeTypeEnum.FOLDER) {
        if (!canSelectFile(item.name)) {
          return;
        }
        onChange?.(item.id);
        setDisplayValue(item.path);
        setVisible(false);
      }
    }
    // 如果是文件夹，点击文件夹，打开文件夹
    if (item.nodeType === FileNodeTypeEnum.FOLDER) {
      changeFolder(item);
    }
  };

  const onBreadcrumbClick = (item: GetWorkspaceFolderListResult) => {
    if (mode === 'folder') {
      onChange?.(item.id);
      setDisplayValue(item.path);
    }
    changeFolder(item);
  };

  // 处理筛选文本变化
  const handleFilterChange = (value: string) => {
    setFilterText(value);
    if (!value.trim()) {
      setList(originalList);
    } else {
      const filteredList = originalList.filter((item) =>
        item.name.toLowerCase().includes(value.toLowerCase())
      );
      setList(filteredList);
    }
  };

  const menu = (
    <div className={cx('select-menu')} onClick={(e) => e.stopPropagation()}>
      <Loading loading={loading}>
        {/* 筛选输入框 */}
        <div className={cx('filter-section')} onClick={(e) => e.stopPropagation()}>
          <Input
            placeholder="搜索文件或文件夹..."
            value={filterText}
            onChange={(e) => handleFilterChange(e.target.value)}
            allowClear
          />
        </div>
        <div className={cx('bread-crumb')} onClick={(e) => e.stopPropagation()}>
          <BreadCrumb
            path={path}
            sliceEndNum={path.length}
            maxLength={3}
            onItemClick={onBreadcrumbClick}
            getPopupContainer={getContainer}
          />
        </div>

        <div className={cx('select-list')} onClick={(e) => e.stopPropagation()}>
          {list.length === 0 && filterText ? (
            <div className={cx('no-results')}>未找到匹配的结果</div>
          ) : (
            list.map((item) => (
              <div
                className={cx('list-item', {active: item.id === value})}
                key={item.id}
                onClick={() => handleSelect(item)}
              >
                {iconMap[item.nodeType]}
                <span className={cx('list-item-name')} title={item.name}>
                  {item.name}
                </span>
              </div>
            ))
          )}
        </div>
      </Loading>
    </div>
  );

  const onVisibleChange = async (visible: boolean) => {
    setVisible(visible);
    if (visible && displayValue) {
      const res = await getWorkspaceFileResult({
        path: displayValue,
        workspaceId
      });
      console.log(res);
      if (res.result.nodeType === FileNodeTypeEnum.FOLDER) {
        changeFolder(res.result);
      } else {
        changeFolder({id: res.result.parentId} as GetWorkspaceFolderListResult);
      }
    }
  };

  return (
    <div>
      <Dropdown
        overlay={menu}
        trigger={['click']}
        visible={visible}
        onVisibleChange={onVisibleChange}
        getPopupContainer={getContainer}
      >
        <div className={cx('flex gap-[8px]')} title={displayValue}>
          <div className={cx('acud-input', 'select-input')} title={displayValue}>
            <div className={cx('overflow-hidden')}>{displayValue}</div>
          </div>
          <Button onClick={() => setVisible(!visible)}>{visible ? '隐藏' : '浏览'}</Button>
        </div>
      </Dropdown>
    </div>
  );
};

export default FilePathSelectWorkarea;
