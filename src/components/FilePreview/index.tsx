import React, {forwardRef, useImperativeHandle, useState} from 'react';
import MultipleFilePreview from './MultipleFilePreview';
import SingleFilePreview from './SingleFilePreview';
import './index.less';

export enum IPreviewType {
  /** 多图预览 */
  MULTIPLE,
  /** 单文件预览 */
  SINGLE
}

export enum ISingleFileType {
  /** 图片 */
  IMAGE,
  /** 文本 */
  TEXT,
  /** 其他 */
  OTHER,
  /** 失败 */
  FAILED
}

export enum IErrorCode {
  EILLEGAL_ARGUMENT = 'IllegalArgument'
}

export interface ISingleFilePreviewProps {
  type: IPreviewType.SINGLE;
  file: {
    name: string;
    size: number;
    path: string;
    downloadInfo?: {
      func: (params: any) => void;
      params: any;
    };
    previewInfo?: {
      func: (...params: any[]) => any;
      params: Array<any>;
    };
  };
}

interface IFileMultiple {
  // 缩略图base64
  thumbnailSrc: string;
  // 图片base64
  src: string;
  // 文件名
  name: string;
  // 图片信息
  info?: Array<{label: string; value: string}>;
}

export interface IMultipleFilePreviewProps {
  type: IPreviewType.MULTIPLE;
  files: IFileMultiple[];
  getContainer?: () => HTMLElement;
}

export type IFilePreviewProps = IMultipleFilePreviewProps | ISingleFilePreviewProps;

export interface IFilePreviewRef {
  open: () => void;
  close: () => void;
}

const FilePreview = (props: IFilePreviewProps, ref: React.ForwardedRef<IFilePreviewRef>) => {
  const [open, setOpen] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
    },
    close: () => {
      setOpen(false);
    }
  }));

  if (props.type === IPreviewType.MULTIPLE) {
    return <MultipleFilePreview {...props} open={open} setOpen={setOpen} />;
  }
  return <SingleFilePreview {...props} open={open} setOpen={setOpen} />;
};

export default forwardRef<IFilePreviewRef, IFilePreviewProps>(FilePreview);
