/** 多图预览
 * <AUTHOR>
 * 支持base64及软链
 * @param type 预览类型 IPreviewType.MULTIPLE
 * @param files 文件列表
 * @param open 是否打开
 * @param setOpen 设置是否打开
 */
import React, {memo} from 'react';
import {IMultipleFilePreviewProps} from '.';
import {Modal} from 'acud';
import {Close1, Left, Right} from '@baidu/xicon-react-bigdata';
import {FileIcon, defaultStyles} from 'react-file-icon';

const klass = 'multiple-file-preview';

enum INavChangeType {
  LEFT,
  RIGHT
}

const MultipleFilePreview: React.FC<
  IMultipleFilePreviewProps & {open: boolean; setOpen: (open: boolean) => void}
> = (props) => {
  // 当前选中图片索引
  const [selectedIndex, setSelectedIndex] = React.useState(0);

  // 是否单张图片
  const isSingleFile = props.files.length === 1;

  // 获取文件扩展名
  const extension = props.files[selectedIndex].name.split('.').pop();

  const handleClose = () => {
    props.setOpen(false);
    // 重置选中索引
    setSelectedIndex(0);
  };

  // 切换图片
  const handleNavChange = (type: INavChangeType) => {
    if (type === INavChangeType.LEFT) {
      setSelectedIndex((selectedIndex - 1 + props.files.length) % props.files.length);
    } else if (type === INavChangeType.RIGHT) {
      setSelectedIndex((selectedIndex + 1) % props.files.length);
    }
  };

  return (
    <Modal
      className={klass}
      visible={props.open}
      title={null}
      footer={null}
      closable={false}
      bodyStyle={{padding: 0}}
      width={1440}
      getContainer={props.getContainer}
    >
      <div className={`${klass}-picture`}>
        {/* 左侧图片 */}
        <div className={`picture-content`}>
          {/* 图片标题 使用react-file-icon + 名称 */}
          <div className="picture-content-title">
            <div className="title-content">
              <div className="icon">
                <FileIcon extension={extension} {...defaultStyles[extension]} size={24} />
              </div>
              {props.files[selectedIndex].name}
            </div>
            <div className={`close`} onClick={handleClose}>
              <Close1 size={24} />
            </div>
          </div>
          <img
            className={`picture-content-img ${isSingleFile ? 'single' : ''}`}
            src={props.files[selectedIndex].src}
          />
          {/* 左右切换 只有多张时显示 */}
          {!isSingleFile && (
            <>
              <div
                className={`picture-content-nav left`}
                onClick={() => handleNavChange(INavChangeType.LEFT)}
              >
                <Left size={28} />
              </div>
              <div
                className={`picture-content-nav right`}
                onClick={() => handleNavChange(INavChangeType.RIGHT)}
              >
                <Right size={28} />
              </div>
            </>
          )}
          <div className={`picture-content-index`}>
            {selectedIndex + 1}/{props.files.length}
          </div>
        </div>
        {!isSingleFile && (
          // TODO: 加入虚拟列表防止图片过多造成性能问题
          <div className={`picture-content-full`}>
            <div className={`picture-content-full-wrapper`}>
              {props.files.map((item, index) => (
                <div
                  className={
                    index === selectedIndex
                      ? `is-active picture-content-full-wrapper-item`
                      : `picture-content-full-wrapper-item`
                  }
                  key={index}
                  onClick={() => setSelectedIndex(index)}
                >
                  <img src={item.src} />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      {/* 右侧图片信息 */}
      <div className={`info-content`}>
        <div className={`info-content-title`}>图片信息</div>
        <div className={`info-content-info`}>
          {props.files[selectedIndex].info?.map((item, index) => (
            <div className="info-item" key={index}>
              <span className="info-item-label">{item.label}</span>
              <span className="info-item-value">{item.value}</span>
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
};

export default memo(MultipleFilePreview);
