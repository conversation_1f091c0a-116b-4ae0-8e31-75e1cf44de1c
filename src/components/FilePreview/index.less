.multiple-file-preview {
  .acud-modal-content {
    border-radius: 0;
    background-color: #151b26;
    color: #fff;
    max-height: 100vh;

    .acud-modal-body {
      padding: 0;
      margin: 0;
      display: flex;
      width: 100%;

      .multiple-file-preview-picture {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .picture-content {
          flex: 1;
          color: #fff;
          display: flex;
          flex-direction: column;
          position: relative;

          &-title {
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 500;
            font-size: 12px;
            color: #ffffff;
            margin: 0 24px;

            .close {
              background: rgba(255, 255, 255, 0.1);
              border-radius: 6px;
              cursor: pointer;
              width: 32px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .title-content {
              display: flex;
              align-items: center;

              .icon {
                width: 24px;
                margin-right: 16px;
                display: flex;
                justify-content: center;
              }
            }
          }

          &-img {
            height: 500px;
            object-fit: contain;

            &.single {
              height: 600px;
            }
          }

          &-nav {
            position: absolute;
            width: 56px;
            height: 56px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.3);
            border-radius: 140px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &.left {
              left: 69px;
            }

            &.right {
              right: 69px;
            }
          }

          &-index {
            position: absolute;
            height: 30px;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0;
            font-size: 14px;
            line-height: 22px;
          }
        }

        .picture-content-full {
          &-wrapper {
            display: flex;
            align-items: center;
            overflow-x: auto;
            margin: 32px;
            position: relative;

            &-item {
              margin-right: 16px;
              border-radius: 6px;
              overflow: hidden;
              width: 100px;
              height: 100px;
              flex-basis: 100px;
              flex-shrink: 0;

              &.is-active {
                background-color: #fff;
                height: 110px;
                width: 110px;
                flex-basis: 110px;
                border: 2px solid #2468f2;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              img {
                width: 100px;
                height: 100px;
                object-fit: cover;
              }
            }
          }
        }
      }

      .info-content {
        width: 374px;
        background-color: #000;
        color: #fff;

        &-title {
          padding: 16px 24px;
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
        }

        &-info {
          padding: 0 24px;
          .info-item {
            margin-bottom: 16px;
            font-weight: 400;
            font-size: 12px;
            line-height: 20px;

            &-label {
              color: rgba(255, 255, 255, 0.7);
              margin-right: 16px;
            }
          }
        }
      }
    }
  }
}

.single-file-preview {
  .acud-modal-body {
    margin: 24px;
  }

  #single-file-preview-panel-canvas {
    width: 950px;
    height: calc(80vh - 100px);
    object-fit: contain;
  }

  &-title {
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    font-size: 12px;
    width: 100%;
    line-height: unset;
    margin-bottom: 16px;

    &-options {
      display: flex;
      align-items: center;

      svg {
        fill: currentColor;
      }

      &-item {
        width: 28px;
        height: 28px;
        margin-left: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 6px;

        div {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &:hover {
          background-color: #edf0f7;
        }
      }
    }

    &-content {
      display: flex;
      align-items: center;
      max-width: 800px;

      .icon {
        min-width: 24px;
        height: 28px;
        margin-right: 16px;
        display: flex;
        justify-content: center;
      }

      .title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  &-panel {
    border: 1px solid #e1e6f0;
    border-radius: 8px;
    height: calc(80vh - 100px);
    overflow-y: auto;
    padding: 16px 8px;

    &-error {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      height: 100%;

      div {
        font-weight: 500;
        font-size: 14px;
        color: #151b26;
      }

      p {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #151b26;
      }
    }
  }

  &-alert {
    margin-bottom: 16px;
  }

  &.is-image {
    .acud-modal-content {
      border-radius: 0;
      background-color: #151b26;
      color: #fff;
    }

    .single-file-preview-title {
      color: #fff;

      &-options {
        &-item {
          &:hover {
            background-color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }

    .single-file-preview-panel {
      border: unset;
      border-radius: unset;
      width: 950px;
      height: calc(80vh - 100px);
      padding: 0;
      overflow: hidden;

      &-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .acud-loading-loading-context {
      background-color: #151b26;
    }
  }
}
