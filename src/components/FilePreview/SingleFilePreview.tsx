import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ISingleFilePreviewProps, ISingleFileType, IErrorCode} from '.';
import {FileIcon, defaultStyles} from 'react-file-icon';
import {Close1, Download1} from '@baidu/xicon-react-bigdata';
import {Alert, Button, Loading, Modal, Tooltip} from 'acud';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {Clipboard} from '@baidu/bce-react-toolkit';
import JsonView from 'react18-json-view';
import 'react18-json-view/src/style.css';
import {checkImgType, handlePreview} from '@utils/handleDownLoad';
import Yaml from 'js-yaml';
import IconSvg from '@components/IconSvg';

const klass = 'single-file-preview';

// 1mb
export const FILE_SIZE_LIMIT = 1 * 1024 * 1024;

const SingleFilePreview: React.FC<
  ISingleFilePreviewProps & {open: boolean; setOpen: (open: boolean) => void}
> = (props) => {
  const {name = '', size = 0, path = '', downloadInfo, previewInfo} = props.file || {};

  // 获取文件扩展名
  const extension = name.split('.').pop();

  // 是否超过1mb
  const isExceed = size > FILE_SIZE_LIMIT;

  const [type, setType] = useState(ISingleFileType.OTHER);
  const [src, setSrc] = useState('');
  const [loading, setLoading] = useState(false);

  const getFileSrc = useCallback(async () => {
    if (!previewInfo) {
      return;
    }

    const isImage = checkImgType(name);
    // 初始化
    setType(isImage ? ISingleFileType.IMAGE : ISingleFileType.TEXT);

    let source;
    setLoading(true);
    try {
      const res = await previewInfo.func(
        ...previewInfo.params,
        !isImage && size > FILE_SIZE_LIMIT ? FILE_SIZE_LIMIT : 0
      );
      source = await handlePreview(res, name);
    } catch (error: any) {
      source = {
        src: '',
        type: error?.code === IErrorCode.EILLEGAL_ARGUMENT ? ISingleFileType.OTHER : ISingleFileType.FAILED
      };
    } finally {
      setLoading(false);
    }
    setSrc(source.src);
    setType(source.type);
  }, [props.file]);

  useEffect(() => {
    getFileSrc();
  }, [props.file, getFileSrc]);

  // 是否是图片
  const isImage = type === ISingleFileType.IMAGE;

  // 预览面板
  const panel = useMemo(() => {
    // 是否是json文件 或 yaml文件
    const isJson = name.endsWith('.json');
    const isYaml = name.endsWith('.yaml') || name.endsWith('.yml');
    try {
      if (isJson) {
        const json = JSON.parse(src);
        return (
          <>
            <JsonView src={json} />
          </>
        );
      } else if (isYaml) {
        const yaml = Yaml.load(src);
        return (
          <>
            <JsonView src={yaml} />
          </>
        );
      }
    } catch (error) {
      return <pre className={`${klass}-panel-text`}>{src}</pre>;
    }

    // tiff
    if (type === ISingleFileType.IMAGE && (name.endsWith('.tif') || name.endsWith('.tiff'))) {
      return null;
    }

    switch (type) {
      case ISingleFileType.IMAGE:
        return <img className={`${klass}-panel-image`} src={src} alt="" />;
      case ISingleFileType.TEXT:
        return <pre className={`${klass}-panel-text`}>{src}</pre>;
      case ISingleFileType.FAILED:
        return (
          <div className={`${klass}-panel-error`}>
            <IconSvg className="icon" type="file-preview-error" size={28} />
            <div>文件加载失败</div>
            <p>
              当前文件无法加载，请
              <Button type="actiontext" onClick={getFileSrc}>
                重新加载
              </Button>
            </p>
          </div>
        );
      default:
        return (
          <div className={`${klass}-panel-error`}>
            <IconSvg type="file-preview-error" size={28} />
            <div>当前格式暂不支持预览或文件已损坏</div>
            <p>
              平台仅支持以下格式的文件预览：txt、csv、json、yaml/yml、log、md、xml、png、jpg/jpeg、bmp、gif、svg、webp、tiff/tif
            </p>
          </div>
        );
    }
  }, [src, type, name, getFileSrc]);

  const handleClose = () => {
    props.setOpen(false);
    setLoading(false);
    setSrc('');
    if (type === ISingleFileType.IMAGE) {
      document.getElementById('single-file-preview-panel-canvas')?.remove();
    }
  };

  const handleDownload = () => {
    if (downloadInfo) {
      downloadInfo.func(downloadInfo.params);
    }
  };

  return (
    <Modal
      className={`${klass} ${isImage ? 'is-image' : ''}`}
      visible={props.open}
      title={null}
      footer={null}
      closable={false}
      bodyStyle={{padding: 0}}
      width={1000}
    >
      <div className={`${klass}-title`}>
        <div className={`${klass}-title-content`}>
          <div className="icon">
            <FileIcon extension={extension} {...defaultStyles[extension]} size={24} />
          </div>
          <TextEllipsis tooltip={name}>{name}</TextEllipsis>
        </div>
        <div className={`${klass}-title-options`}>
          <Tooltip title="复制路径">
            <div className={`${klass}-title-options-item`}>
              <Clipboard text={path} successMessage="复制成功"></Clipboard>
            </div>
          </Tooltip>
          <Tooltip title="下载">
            <div className={`${klass}-title-options-item`} onClick={handleDownload}>
              <Download1 size={16} />
            </div>
          </Tooltip>
          <div className={`${klass}-title-options-item`} onClick={handleClose}>
            <Close1 size={16} />
          </div>
        </div>
      </div>
      {isExceed && type === ISingleFileType.TEXT && (
        <Alert
          className={`${klass}-alert`}
          message="文件过大，仅展示前1MB的内容，完整内容请下载后查看"
          type="warning"
        />
      )}
      <Loading loading={loading} indicatorClassName={isImage ? 'loading-is-image' : ''}>
        <div className={`${klass}-panel`}>{panel}</div>
      </Loading>
    </Modal>
  );
};

export default SingleFilePreview;
