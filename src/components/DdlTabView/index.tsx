/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-07-24 10:25:59
 * @LastEditTime: 2025-07-24 16:58:30
 */

import React from 'react';
import SqlEditPage from '@components/SqlEditPage';
import IconSvg from '@components/IconSvg';
import {Button} from 'acud';
import './index.less';
import {Clipboard} from '@baidu/bce-react-toolkit';

interface IDdlTabViewProps {
  title?: string | React.ReactNode;
  ddlSql?: string;
}

const DdlTabView: React.FC<IDdlTabViewProps> = ({ddlSql = '', title}) => {
  return (
    <div className='ddl-panel-tab-view-container'>
      <div className='ddl-panel-header'>
        <span className='title-desc'>{title}</span>
        <Clipboard
          text={ddlSql || ''}
          successMessage="复制成功"
          className="clipboard-inline-btn"
        >
          <Button
            icon={<IconSvg type="copy" />}
            size="small"
            type="actiontext"
          />
        </Clipboard>
      </div>
      <div className='ddl-panel-sql-content'>
        <SqlEditPage value={ddlSql} isReadOnly></SqlEditPage>
      </div>
    </div>
  );
};

export default DdlTabView;
