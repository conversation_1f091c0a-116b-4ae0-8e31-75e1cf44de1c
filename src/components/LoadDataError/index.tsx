/* 数据加载失败组件
 * @Author: hongjin<PERSON><EMAIL>
 * @Date: 2025-08-06 14:32:24
 * @LastEditTime: 2025-08-06 14:41:49
 */
import React from 'react';
import {Button, Empty} from 'acud';

import './index.less';

// 这里情况比较特殊，为了兜底网络错误等数据加载出错的问题。使用 cdn 链接会出现 断网时裂图的情况。
const loadDataErrBack = "data:image/png;base64,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";
interface LoadDataErrorProps {
  desTitle?: string;
  desInfo?: string;
  buttonText?: string;
  buttonHandle?: () => void;
}

const LoadDataError: React.FC<LoadDataErrorProps> = ({
  desTitle = '加载失败',
  desInfo = '获取数据目录失败',
  buttonText = '立即刷新',
  buttonHandle = () => {},
}) => {
  return <Empty
    image={<img src={loadDataErrBack}/>}
    imageStyle={{height: 74}}
    description={
      <>
        <div className='desc-title'>{desTitle}</div>
        <div className='desc-info'>{desInfo}</div>
      </>
    }
    children={<Button type="enhance" onClick={buttonHandle}>{buttonText}</Button>}
  />
};

export default LoadDataError;
