/**
 * 详情页 - 详情分区容器
 * 主要内容：
 * 1. 标题 （共n条）
 * 2. 表格展示n条数据的内容
 * <AUTHOR>
 */
import React from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
const cx = classNames.bind(styles);
interface DetailProps {
  title: string;
  details: Array<{
    label: string;
    value: string | React.ReactNode;
  }>;
}
const DetailWrapper: React.FC<DetailProps> = ({title, details}) => {
  return (
    <div className={cx('detail-section')}>
      <div className={cx('section-title')}>
        {title}
        <span className={cx('section-count')}>共{details.length}条</span>
      </div>
      <div className={cx('section-content')}>
        {details.map((detail) => (
          <div className={cx('item-row')} key={detail.label}>
            <div className={cx('item-label')}>{detail.label}</div>
            <div className={cx('item-value')}>{detail.value}</div>
          </div>
        ))}
      </div>
    </div>
  );
};
export default DetailWrapper;
