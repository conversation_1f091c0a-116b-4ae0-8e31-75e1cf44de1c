.resizable-panels {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;

  .panel {
    display: flex;
    flex-direction: column;
    min-height: 0;
    min-width: 0;
    overflow: hidden;

    &.left-panel {
      flex-shrink: 0;
    }

    &.right-panel {
      flex-shrink: 0;
    }
  }

  .divider {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: col-resize;
    flex-shrink: 0;
    background-color: transparent;

    &:hover {
      .divider-line {
        background-color: #1890ff;
        width: 2px;
      }
    }

    &.dragging {
      .divider-line {
        background-color: #1890ff;
        width: 2px;
      }
    }

    .divider-line {
      width: 1px;
      height: 100%;
      background-color: transparent;
      transition: all 0.2s ease;
    }
  }
}

// 全局样式，防止拖拽时文本被选中
:global {
  .resizable-panels-dragging {
    user-select: none;

    * {
      user-select: none;
    }
  }
}
