# ResizablePanels 可调节宽度面板组件

一个通用的可拖拽调节宽度的双面板组件，支持灵活的配置和约束条件。

## 功能特性

1. **灵活的初始配置**：支持设定任意一侧为固定宽度，另一侧自适应
2. **完整的约束系统**：可设置左右面板的最小/最大宽度
3. **多种宽度单位**：支持像素值和百分比
4. **平滑的拖拽体验**：带有视觉反馈的拖拽分隔条
5. **完整的 TypeScript 支持**：提供详细的类型定义
6. **回调支持**：宽度变化时的回调函数

## 基本用法

```tsx
import ResizablePanels from '@components/ResizablePanels';

function MyComponent() {
  return (
    <ResizablePanels
      leftPanel={<div>左侧内容</div>}
      rightPanel={<div>右侧内容</div>}
    />
  );
}
```

## 高级配置

### 1. 固定左侧宽度，右侧自适应

```tsx
<ResizablePanels
  leftPanel={<TableComponent />}
  rightPanel={<SqlPanel />}
  initialConfig={{
    leftWidth: 600, // 固定左侧 600px
    fixedSide: 'left'
  }}
  constraints={{
    leftMinWidth: 400,
    leftMaxWidth: 800,
    rightMinWidth: '20%'
  }}
/>
```

### 2. 固定右侧宽度，左侧自适应

```tsx
<ResizablePanels
  leftPanel={<ContentArea />}
  rightPanel={<Sidebar />}
  initialConfig={{
    rightWidth: '30%', // 固定右侧 30%
    fixedSide: 'right'
  }}
  constraints={{
    rightMinWidth: 200,
    rightMaxWidth: '50%',
    leftMinWidth: '40%'
  }}
/>
```

### 3. 自定义分隔条

```tsx
<ResizablePanels
  leftPanel={<div>左侧</div>}
  rightPanel={<div>右侧</div>}
  dividerConfig={{
    width: 12,
    className: 'custom-divider',
    visible: true
  }}
  onWidthChange={(leftWidth, rightWidth) => {
    console.log('宽度变化:', leftWidth, rightWidth);
  }}
/>
```

## API 参数

### ResizablePanelsProps

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| leftPanel | ReactNode | - | 左侧面板内容 |
| rightPanel | ReactNode | - | 右侧面板内容 |
| initialConfig | InitialConfig | `{}` | 初始配置 |
| constraints | Constraints | `{}` | 约束条件 |
| dividerConfig | DividerConfig | `{}` | 分隔条配置 |
| onWidthChange | Function | - | 宽度变化回调 |
| className | string | - | 自定义容器样式类名 |
| style | CSSProperties | - | 自定义样式 |

### InitialConfig

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| leftWidth | number \| string | `'50%'` | 左侧面板初始宽度 |
| rightWidth | number \| string | - | 右侧面板初始宽度 |
| fixedSide | 'left' \| 'right' \| 'none' | `'none'` | 固定哪一侧的宽度 |

### Constraints

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| leftMinWidth | number \| string | `'20%'` | 左侧面板最小宽度 |
| leftMaxWidth | number \| string | `'80%'` | 左侧面板最大宽度 |
| rightMinWidth | number \| string | `'20%'` | 右侧面板最小宽度 |
| rightMaxWidth | number \| string | `'80%'` | 右侧面板最大宽度 |

### DividerConfig

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| width | number | `8` | 分隔条宽度（像素） |
| visible | boolean | `true` | 是否显示分隔条 |
| className | string | - | 自定义分隔条样式类名 |

## 使用场景

1. **数据表格 + 详情面板**：左侧表格，右侧详情
2. **代码编辑器 + 预览**：左侧编辑器，右侧预览
3. **文件列表 + 内容查看**：左侧文件树，右侧文件内容
4. **导航 + 主内容**：左侧导航，右侧主要内容区域

## 注意事项

1. 宽度单位支持像素值（number）和百分比（string，如 '30%'）
2. 约束条件会自动应用，确保面板宽度在合理范围内
3. 拖拽时会自动处理鼠标样式和文本选择
4. 组件会自动处理容器尺寸变化
