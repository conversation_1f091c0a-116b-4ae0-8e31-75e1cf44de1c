import React, {useState, useRef, useEffect, ReactNode} from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';

const cx = classNames.bind(styles);

export interface ResizablePanelsProps {
  /** 左侧面板内容 */
  leftPanel: ReactNode;
  /** 右侧面板内容 */
  rightPanel: ReactNode;
  /** 初始配置 */
  initialConfig?: {
    /** 左侧面板初始宽度（像素值或百分比） */
    leftWidth?: number | string;
    /** 右侧面板初始宽度（像素值或百分比） */
    rightWidth?: number | string;
    /** 固定哪一侧的宽度，另一侧自适应 */
    fixedSide?: 'left' | 'right' | 'none';
  };
  /** 约束条件 */
  constraints?: {
    /** 左侧面板最小宽度（像素值或百分比） */
    leftMinWidth?: number | string;
    /** 左侧面板最大宽度（像素值或百分比） */
    leftMaxWidth?: number | string;
    /** 右侧面板最小宽度（像素值或百分比） */
    rightMinWidth?: number | string;
    /** 右侧面板最大宽度（像素值或百分比） */
    rightMaxWidth?: number | string;
  };
  /** 分隔条配置 */
  dividerConfig?: {
    /** 分隔条宽度 */
    width?: number;
    /** 是否显示分隔条 */
    visible?: boolean;
    /** 自定义分隔条样式类名 */
    className?: string;
  };
  /** 宽度变化回调 */
  onWidthChange?: (leftWidth: number, rightWidth: number) => void;
  /** 自定义容器样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

// 工具函数：将宽度值转换为像素值
const convertToPixels = (value: number | string, containerWidth: number): number => {
  if (typeof value === 'number') {
    return value;
  }
  if (typeof value === 'string') {
    if (value.endsWith('%')) {
      const percentage = parseFloat(value) / 100;
      return containerWidth * percentage;
    }
    if (value.endsWith('px')) {
      return parseFloat(value);
    }
    // 如果是纯数字字符串，当作像素值处理
    const numValue = parseFloat(value);
    return isNaN(numValue) ? 0 : numValue;
  }
  return 0;
};

// 工具函数：将像素值转换为百分比
const convertToPercentage = (pixels: number, containerWidth: number): number => {
  return (pixels / containerWidth) * 100;
};

export default function ResizablePanels({
  leftPanel,
  rightPanel,
  initialConfig = {},
  constraints = {},
  dividerConfig = {},
  onWidthChange,
  className,
  style
}: ResizablePanelsProps) {
  const {
    leftWidth: initialLeftWidth = '50%',
    rightWidth: initialRightWidth,
    fixedSide = 'none'
  } = initialConfig;

  const {
    leftMinWidth = '20%',
    leftMaxWidth = '80%',
    rightMinWidth = '20%',
    rightMaxWidth = '80%'
  } = constraints;

  const {
    width: dividerWidth = 8,
    visible: dividerVisible = true,
    className: dividerClassName
  } = dividerConfig;

  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [leftPanelWidth, setLeftPanelWidth] = useState<number>(50); // 百分比
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [leftPanelPixels, setLeftPanelPixels] = useState<number>(0); // 左侧面板像素宽度
  const [rightPanelPixels, setRightPanelPixels] = useState<number>(0); // 右侧面板像素宽度

  // 计算初始宽度的函数
  const calculateInitialWidth = (containerWidth: number) => {
    if (containerWidth <= 0) {
      setLeftPanelPixels(0);
      setRightPanelPixels(0);
      return 50;
    }

    const availableWidth = containerWidth - (dividerVisible ? dividerWidth : 0);
    let leftPixels: number;
    let rightPixels: number;

    if (fixedSide === 'left' && initialLeftWidth) {
      leftPixels = convertToPixels(initialLeftWidth, containerWidth);
      rightPixels = availableWidth - leftPixels;
    } else if (fixedSide === 'right' && initialRightWidth) {
      rightPixels = convertToPixels(initialRightWidth, containerWidth);
      leftPixels = availableWidth - rightPixels;
    } else {
      leftPixels = convertToPixels(initialLeftWidth, containerWidth);
      rightPixels = availableWidth - leftPixels;
    }

    // 确保宽度不为负数
    leftPixels = Math.max(0, leftPixels);
    rightPixels = Math.max(0, rightPixels);

    // 设置像素宽度
    setLeftPanelPixels(leftPixels);
    setRightPanelPixels(rightPixels);

    // 计算百分比（用于拖拽逻辑）
    const leftPercentage = convertToPercentage(leftPixels, containerWidth);
    const result = Math.max(0, Math.min(100, leftPercentage));
    return result;
  };

  // 监听容器尺寸变化
  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const newWidth = entry.contentRect.width;
        setContainerWidth(newWidth);
        
        // 重新计算宽度
        const newLeftWidth = calculateInitialWidth(newWidth);
        setLeftPanelWidth(newLeftWidth);
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [initialLeftWidth, initialRightWidth, fixedSide, dividerWidth, dividerVisible]);

  // 初始化宽度
  useEffect(() => {
    if (!containerRef.current) return;

    const width = containerRef.current.offsetWidth;
    if (width > 0) {
      setContainerWidth(width);
      const initialWidth = calculateInitialWidth(width);
      setLeftPanelWidth(initialWidth);
    }
  }, []);

  // 处理拖拽开始
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!dividerVisible) return;
    e.preventDefault();
    setIsDragging(true);
  };

  // 处理拖拽移动
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const currentContainerWidth = containerRect.width;
    const mouseX = e.clientX - containerRect.left;
    
    // 考虑分隔条宽度
    const availableWidth = currentContainerWidth - (dividerVisible ? dividerWidth : 0);
    
    // 计算新的左侧面板宽度像素值
    let newLeftWidthPixels = mouseX;
    
    // 应用约束条件（像素值）
    const leftMinWidthPixels = convertToPixels(leftMinWidth, currentContainerWidth);
    const leftMaxWidthPixels = convertToPixels(leftMaxWidth, currentContainerWidth);
    const rightMinWidthPixels = convertToPixels(rightMinWidth, currentContainerWidth);
    const rightMaxWidthPixels = convertToPixels(rightMaxWidth, currentContainerWidth);

    // 确保左侧面板不超过最小/最大宽度
    newLeftWidthPixels = Math.max(leftMinWidthPixels, newLeftWidthPixels);
    newLeftWidthPixels = Math.min(leftMaxWidthPixels, newLeftWidthPixels);
    
    // 确保右侧面板不超过最小/最大宽度
    const rightWidthPixels = availableWidth - newLeftWidthPixels;
    if (rightWidthPixels < rightMinWidthPixels) {
      newLeftWidthPixels = availableWidth - rightMinWidthPixels;
    }
    if (rightWidthPixels > rightMaxWidthPixels) {
      newLeftWidthPixels = availableWidth - rightMaxWidthPixels;
    }
    
    // 确保不超出容器范围
    newLeftWidthPixels = Math.max(0, Math.min(availableWidth, newLeftWidthPixels));

    // 计算新的右侧面板宽度
    const newRightWidthPixels = availableWidth - newLeftWidthPixels;
    
    // 更新像素宽度状态
    setLeftPanelPixels(newLeftWidthPixels);
    setRightPanelPixels(newRightWidthPixels);
    
    // 转换为百分比（用于内部状态管理）
    const newLeftWidthPercentage = convertToPercentage(newLeftWidthPixels, currentContainerWidth);
    setLeftPanelWidth(newLeftWidthPercentage);

    // 触发回调
    if (onWidthChange) {
      onWidthChange(newLeftWidthPixels, newRightWidthPixels);
    }
  };

  // 处理拖拽结束
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isDragging]);

  // 根据固定模式决定使用像素值还是百分比
  const getLeftPanelStyle = () => {
    if (fixedSide === 'left' && leftPanelPixels > 0) {
      return { width: `${leftPanelPixels}px`, flexShrink: 0 };
    }
    return { width: `${leftPanelWidth}%` };
  };

  const getRightPanelStyle = () => {
    if (fixedSide === 'right' && rightPanelPixels > 0) {
      return { width: `${rightPanelPixels}px`, flexShrink: 0 };
    }
    const rightPanelWidth = 100 - leftPanelWidth;
    return { width: `${rightPanelWidth}%` };
  };

  return (
    <div ref={containerRef} className={cx('resizable-panels', className)} style={style}>
      {/* 左侧面板 */}
      <div className={cx('panel', 'left-panel')} style={getLeftPanelStyle()}>
        {leftPanel}
      </div>

      {/* 分隔条 */}
      {dividerVisible && (
        <div
          className={cx('divider', dividerClassName, {dragging: isDragging})}
          style={{width: `${dividerWidth}px`}}
          onMouseDown={handleMouseDown}
        >
          <div className={cx('divider-line')} />
        </div>
      )}

      {/* 右侧面板 */}
      <div className={cx('panel', 'right-panel')} style={getRightPanelStyle()}>
        {rightPanel}
      </div>
    </div>
  );
}
