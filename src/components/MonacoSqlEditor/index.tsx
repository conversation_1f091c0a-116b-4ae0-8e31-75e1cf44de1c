import React, {useEffect, useRef} from 'react';
import ReactMonacoEditor from 'react-monaco-editor';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {format} from 'sql-formatter';

const cx = classNames.bind(styles);

interface IMonacoSqlEditor {
  value?: string;
  onChange?: (value: string) => void;
  height?: string | number;
  readOnly?: boolean;
  language?: string;
  onExecute?: () => void;
  className?: string;
  placeholder?: string;
}

/**
 * Monaco SQL编辑器组件
 * @param value 编辑器内容
 * @param onChange 内容变化回调
 * @param height 编辑器高度
 * @param readOnly 是否只读
 * @param language 语言类型
 * @returns
 */
const MonacoSqlEditor: React.FC<IMonacoSqlEditor> = ({
  value = '',
  onChange,
  height = '100%',
  readOnly = false,
  language = 'sql',
  className,
  placeholder,
  onExecute
}) => {
  const editorRef = useRef(null);
  const monacoRef = useRef(null);

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;

    // 应用主题
    monaco.editor.setTheme('sqlTheme');

    monaco.languages.registerDocumentFormattingEditProvider('sql', {
      provideDocumentFormattingEdits(model, options, token) {
        const formatted = format(model.getValue(), {
          language: 'sql'
        });

        return [
          {
            range: model.getFullModelRange(),
            text: formatted
          }
        ];
      }
    });

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      const currentValue = editor.getValue();
      if (onChange) {
        onChange(currentValue);
      }
    });

    // 添加快捷键支持
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      if (onExecute) {
        onExecute();
      }
    });

    // 添加格式化快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
      editor.getAction('editor.action.formatDocument').run();
    });
  };

  const handleEditorChange = (newValue: string) => {
    if (onChange) {
      onChange(newValue);
    }
  };

  /**
   * 组件卸载时清理资源
   */
  useEffect(() => {
    return () => {
      if (editorRef.current) {
        editorRef.current.dispose();
        editorRef.current = null;
      }
    };
  }, []);

  return (
    <div className={cx('sql-editor-container', className)} style={{height}}>
      <ReactMonacoEditor
        height="100%"
        language={language}
        value={value}
        onChange={handleEditorChange}
        editorDidMount={handleEditorDidMount}
        options={{
          placeholder,
          readOnly,
          scrollBeyondLastLine: false,
          automaticLayout: true,
          wordWrap: 'on',
          // lineNumbers: 'off',
          minimap: {
            enabled: false
          },
          fontSize: 13,
          lineHeight: 20,
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          tabSize: 2,
          insertSpaces: true,
          renderLineHighlight: 'line',
          selectOnLineNumbers: true,
          roundedSelection: false,
          cursorStyle: 'line',
          formatOnPaste: true,
          formatOnType: true,
          suggestOnTriggerCharacters: true,
          acceptSuggestionOnEnter: 'on',
          snippetSuggestions: 'inline',
          wordBasedSuggestions: 'off',
          parameterHints: {
            enabled: true
          },
          quickSuggestions: {
            other: true,
            comments: false,
            strings: false
          }
        }}
      />
    </div>
  );
};

export default MonacoSqlEditor;
