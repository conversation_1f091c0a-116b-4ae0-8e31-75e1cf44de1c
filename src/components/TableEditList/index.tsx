import {Button, Form, Table} from 'acud';
import {FormInstance} from 'acud/lib/form';
import React from 'react';
import './index.less';

export interface IColumn {
  title: string;
  dataIndex: string;
  width?: number;
  valuePropName?: string;
  rules?: any[];
  renderComponent?: React.ReactNode;
  render?: (text: string, record: any, index: number) => React.ReactNode;
}
const TableEditList: React.FC<{
  isReadOnly?: boolean;
  name: string;
  form?: FormInstance;
  columnList: IColumn[];
  defaultValue?: any;
  addButtonText?: string;
}> = ({name, isReadOnly = true, form, columnList, defaultValue = {}, addButtonText = '添加字段'}) => {
  return (
    <div className="table-edit-list">
      <Form.List name={name}>
        {(fields, {add, remove}) => {
          const columns = [
            ...columnList.map((item) => {
              return {
                ...item,
                key: item.dataIndex,
                // width: item.width ? item.width : 100,
                render: item?.render
                  ? item.render
                  : (_text, record, index) => (
                      <Form.Item
                        name={[index, item.dataIndex]}
                        rules={item.rules}
                        fieldKey={[index, item.dataIndex]}
                        valuePropName={item.valuePropName ? item.valuePropName : 'value'}
                      >
                        {item.renderComponent}
                      </Form.Item>
                    )
              };
            }),
            {
              title: '操作',
              width: 70,
              dataIndex: 'operation',
              render: (_, record, index) => (
                <Button className="delete-button" type="actiontext" onClick={() => remove(index)}>
                  移除
                </Button>
              )
            }
          ];

          return (
            <>
              <Table dataSource={fields} columns={columns} pagination={false} rowKey="id" />
              {!isReadOnly && (
                <Button className="add-button" onClick={() => add({...defaultValue})} type="default">
                  {addButtonText}
                </Button>
              )}
            </>
          );
        }}
      </Form.List>
    </div>
  );
};

export default TableEditList;
