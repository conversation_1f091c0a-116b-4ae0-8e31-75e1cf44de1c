import {IAlertStrategy} from '@api/job';
import {jobInstanceListUser} from '@api/jobInstance';
import IconSvg from '@components/IconSvg';
import TableEditList from '@components/TableEditList';
import {WorkspaceContext} from '@pages/index';
import {
  TaskAlertSceneChineseMap,
  TaskAlertSceneEnum,
  TaskAlertTypeChineseMap,
  TaskAlertTypeEnum
} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {Button, Checkbox, Form, Input, InputNumber, Modal, Select, Space, Table} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {isArray} from 'lodash';
import React, {useContext, useEffect, useMemo} from 'react';
import {useSelector} from 'react-redux';

const AlertStrategyList: React.FC<{
  value?: IAlertStrategy[];
  onChange?: (value: IAlertStrategy[]) => void;
}> = ({value, onChange}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const {workspaceId} = useContext(WorkspaceContext);
  const [form] = Form.useForm();

  const [visible, setVisible] = React.useState(false);
  const [userList, setUserList] = React.useState([]);

  const initUserList = () => {
    jobInstanceListUser(workspaceId).then((res) => {
      setUserList(res.result.runUserList);
    });
  };
  useEffect(() => {
    initUserList();
  }, []);

  const prefixSelector = (index: number) => (
    <Form.Item name={[index, 'alertType']} initialValue={TaskAlertTypeEnum.SMS}>
      <Select
        style={{
          width: 80
        }}
      >
        {Object.entries(TaskAlertTypeChineseMap).map(([key, value]) => (
          <Select.Option key={key} value={key}>
            {value}
          </Select.Option>
        ))}
      </Select>
    </Form.Item>
  );

  const columnList = useMemo(() => {
    return [
      {
        title: '用户',
        dataIndex: 'alertUser',
        width: 165,
        render: (text: string, record: any, index: number) => {
          return (
            <Form.Item name={[index, 'alertUser']} rules={[{required: true, message: '请选择用户'}]}>
              <Select
                style={{width: '160px'}}
                placeholder="请输入"
                options={userList?.map((item: any) => ({label: item.runUsername, value: item.runUsername}))}
              />
            </Form.Item>
          );
        }
      },
      {
        title: '方式',
        dataIndex: 'alertAddress',
        width: 280,
        render: (text: string, record: any, index: number) => {
          return (
            <>
              <Form.Item name={[index, 'alertAddress']}>
                <Input
                  addonBefore={prefixSelector(index)}
                  style={{
                    width: '100%'
                  }}
                />
              </Form.Item>
            </>
          );
        }
      },
      {
        title: '告警策略',
        dataIndex: 'alertScenes',
        width: 512,
        render: (text: string, record: any, index: number) => {
          return (
            <>
              <Space>
                <Form.Item name={[index, 'alertScenes']}>
                  <Checkbox.Group
                    options={Object.entries(TaskAlertSceneChineseMap).map(([key, value]) => ({
                      label: value,
                      value: key
                    }))}
                  />
                </Form.Item>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues['alertStrategyList']?.[index]?.alertScenes !==
                    curValues['alertStrategyList']?.[index]?.alertScenes
                  }
                >
                  {({getFieldsValue}) => {
                    const alertScenes = getFieldsValue()?.['alertStrategyList']?.[index]?.alertScenes;
                    if (isArray(alertScenes) && alertScenes.includes(TaskAlertSceneEnum.TIMEOUT)) {
                      return (
                        <Form.Item name={[index, 'timeout']}>
                          <InputNumber
                            style={{width: '90px'}}
                            precision={0}
                            min={1}
                            step={1}
                            formatter={(value) => value + '分钟'}
                            placeholder=""
                          />
                        </Form.Item>
                      );
                    }
                    return null;
                  }}
                </Form.Item>
              </Space>
            </>
          );
        }
      }
    ];
  }, [userList]);

  const column = [
    {
      title: '用户',
      dataIndex: 'alertUser',
      width: 200
    },
    {
      title: '方式',
      dataIndex: 'alertType',
      width: 200
    },
    {
      title: '告警策略',
      dataIndex: 'alertScenes',
      width: 200,
      render: (text: string[], record: any) => {
        return text?.map((item: string) => TaskAlertSceneChineseMap[item]).join('、');
      }
    }
  ];
  const defaultValue = {name: ''};

  // 保存
  const onSaveForm = useMemoizedFn(async () => {
    try {
      await form.validateFields();
    } catch {
      return;
    }
    // formData 值为： {name:xxx, comment:xxx}
    const formData = form.getFieldsValue();
    setVisible(false);
    onChange?.(formData.alertStrategyList);
  });

  // 保存按钮
  const footer = useMemo(
    () => (
      <Space>
        <Button type="primary" onClick={onSaveForm}>
          保存
        </Button>
      </Space>
    ),
    [onSaveForm]
  );

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        alertStrategyList: value
      });
    }
  }, [visible, value, form]);

  return (
    <>
      <div className={'form-title'}>
        <IconSvg size={16} type="workflow-detail" />
        <span className={'form-title-text'}>
          通知告警 <IconSvg onClick={() => setVisible(true)} type="edit" size={16} />
        </span>
      </div>
      <Table columns={column} dataSource={value} pagination={false} />

      <Modal
        title="告警策略"
        width={1000}
        onCancel={() => setVisible(false)}
        visible={visible}
        footer={footer}
      >
        <Form form={form} name="obj">
          <TableEditList
            isReadOnly={false}
            name="alertStrategyList"
            form={form}
            columnList={columnList}
            defaultValue={defaultValue}
          ></TableEditList>
        </Form>
      </Modal>
    </>
  );
};

export default AlertStrategyList;
