import React, {useContext, useEffect, useState, useCallback, useMemo} from 'react';
import {InputNumber, toast} from 'acud';
import IconSvg from '@components/IconSvg';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {computeScaleUp, getComputeResourceDetail} from '@api/Compute';
import useUrlState from '@ahooksjs/use-url-state';
import {WorkspaceContext} from '@pages/index';
import {useRequest} from 'ahooks';
import OrderDetail from '@pages/Compute/components/OrderDetail';
import {useRegion} from '@hooks/useRegion';
import useEnv from '@hooks/useEnv';
import {PAY_TYPE} from '../config';
import {getVpcList, getSubnetList, type Vpc, type Subnet} from '@api/Compute';
const cx = classNames.bind(styles);

const ComputeConfig: React.FC = () => {
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState();
  const [nodeCount, setNodeCount] = useState(0);
  const [detail, setDetail] = useState<any>(null);
  const {currentRegion} = useRegion();
  const {isPrivate} = useEnv();

  const goComputeList = () => {
    navigate(`${urls.compute}?workspaceId=${workspaceId}&tab=${urlState.tab}`);
  };

  const {run: getDetail, loading} = useRequest(
    () => {
      return getComputeResourceDetail({
        workspaceId,
        computeId: urlState.computeId
      });
    },
    {
      manual: true,
      onSuccess: (res) => {
        if (!res.success) {
          return;
        }
        setNodeCount(res.result.nodeCount);
        setDetail(res.result);
      }
    }
  );

  useEffect(() => {
    getDetail();
  }, []);

  const [vpcList, setVpcList] = React.useState<Vpc[]>([]);
  const [vpc, setVpc] = React.useState<Vpc | null>(null);

  const {run: getVpcListRun} = useRequest(getVpcList, {
    manual: true,
    onSuccess: (res) => {
      if (!res.success) {
        return;
      }
      setVpcList(res.result);
    }
  });

  // 渲染vpc名称
  const renderVpcName = useMemo(() => {
    if (!detail?.vpcId) {
      return;
    }
    const vpc = vpcList.find((item) => item.shortId === detail.vpcId);
    if (vpc) {
      setVpc(vpc);
    }
    return vpc ? `${vpc.name}（${vpc.cidr}）` : detail.vpcId;
  }, [vpcList, detail]);

  const [subnetList, setSubnetList] = React.useState<Subnet[]>([]);
  const {run: getSubnetListRun} = useRequest(getSubnetList, {
    manual: true,
    onSuccess: (res) => {
      setSubnetList(res.page.result);
    }
  });

  // 渲染可用区-子网
  const renderSubnetName = useMemo(() => {
    if (!detail?.subnetId) {
      return;
    }
    const subnet = subnetList.find((item) => item.shortId === detail.subnetId);
    const zoneName = detail.availableZone.replace('zone', '可用区');
    return subnet
      ? `${currentRegion?.label}-${zoneName} ${subnet.name}（${subnet.cidr}）`
      : `${currentRegion?.label}-${zoneName} ${detail.subnetId}`;
  }, [detail, subnetList, currentRegion?.label]);

  useEffect(() => {
    getVpcListRun();
  }, []);

  useEffect(() => {
    if (!vpc) {
      return;
    }
    getSubnetListRun({vpcId: vpc.vpcId});
  }, [vpc]);

  const computeInfo = [
    [
      {label: '实例名称', value: detail?.name},
      {label: '实例Id', value: detail?.computeId}
    ],
    [
      {label: '付费方式', value: PAY_TYPE.getTextFromValue(detail?.chargeType)},
      {label: '实例类型', value: detail?.engine}
    ],
    [
      {label: '网络VPC', value: renderVpcName || '-'},
      {label: '可用区及子网', value: renderSubnetName || '-'}
    ]
  ];

  const nodeConfig = {
    before: [
      {label: '节点规格', value: detail?.runtime},
      {label: '节点规格', value: detail?.clusterType},
      {label: '节点数量', value: detail?.nodeCount}
    ],
    after: [
      {label: '节点规格', value: detail?.runtime},
      {label: '节点规格', value: detail?.clusterType},
      {label: '节点数量', value: nodeCount}
    ]
  };

  const detailItems = [
    {label: '付费方式', value: PAY_TYPE.getTextFromValue(detail?.chargeType)},
    {label: '地域', value: currentRegion?.label},
    {label: '节点规格', value: detail?.clusterType},
    {label: '节点数量', value: nodeCount}
  ];

  const handleSubmit = async () => {
    if (Number(nodeCount) === Number(detail?.nodeCount)) {
      toast.error({
        message: '节点数量未发生变化',
        duration: 3
      });
      throw new Error('节点数量未发生变化');
    }
    const res = await computeScaleUp({
      workspaceId,
      computeId: detail?.computeId,
      totalCount: nodeCount
    });
    if (res.success) {
      if (isPrivate) {
        toast.success({
          message: '配置变更成功',
          duration: 3
        });
        goComputeList();
      }
      return res;
    } else {
      throw new Error(res.message.global);
    }
  };

  const subServiceType = {
    dataAnalysis: 'general compute',
    dataIntegration: 'general compute',
    dataProcess: 'general compute',
    resourcePool: 'pool'
  }[urlState.tab];

  const configParams = useMemo(() => {
    if (!detail) {
      return null;
    }
    return {
      workspaceId,
      tab: urlState.tab,
      chargingType: detail?.chargeType,
      region: currentRegion?.id,
      clusterType: detail?.clusterType,
      nodeCnt: nodeCount,
      subServiceType,
      instanceId: detail?.computeId
    };
  }, [workspaceId, urlState.tab, currentRegion?.id, nodeCount, subServiceType, detail]);

  return (
    <div className={cx('compute-wrapper')}>
      <div className={cx('compute-content')}>
        <div className={cx('compute-title')}>
          <IconSvg
            type="right"
            size={18}
            color="#5C5F66"
            className={cx('compute-title-icon')}
            onClick={() => {
              goComputeList();
            }}
          />
          配置变更
        </div>
        <div className={cx('compute-detail-wrapper')}>
          <div className={cx('legend-title')}>实例信息</div>
          {computeInfo.map((item, index) => {
            return (
              <div key={index} className={cx('compute-info-row')}>
                {item.map((info, infoIndex) => {
                  return (
                    <div key={infoIndex} className={cx('compute-info-col')}>
                      <div className={cx('compute-info-col-label')}>{info.label}</div>
                      <div className={cx('compute-info-col-value')}>{info.value}</div>
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
        <div className={cx('compute-config-wrapper')}>
          <div className={cx('legend-title')}>节点配置</div>
          <div className={cx('compute-config-table')}>
            <div className={cx('config-before')}>
              <div className={cx('config-title')}>变更前</div>
              <div className={cx('config-content')}>
                {nodeConfig.before.map((item, index) => {
                  return (
                    <div key={index} className={cx('config-item')}>
                      <div className={cx('config-item-label')}>{item.label}</div>
                      <div className={cx('config-item-value')}>{item.value}</div>
                    </div>
                  );
                })}
              </div>
            </div>
            <div className={cx('config-after')}>
              <div className={cx('config-title')}>变更后</div>
              <div className={cx('config-content')}>
                {nodeConfig.after.map((item, index) => {
                  if (item.label === '节点数量') {
                    return (
                      <div key={index} className={cx('config-item')}>
                        <div className={cx('config-item-label')}>{item.label}</div>
                        <div className={cx('config-item-value')} style={{position: 'relative', top: '10px'}}>
                          <InputNumber
                            symmetryMode
                            min={detail?.nodeCount}
                            max={100}
                            value={nodeCount}
                            onChange={(value) => {
                              setNodeCount(value);
                            }}
                          />
                          <div className={cx('config-item-tip')}>
                            特别提示：为保障服务高可用，建议在生产环境中，数据节点数量不小于3
                          </div>
                        </div>
                      </div>
                    );
                  }
                  return (
                    <div key={index} className={cx('config-item')}>
                      <div className={cx('config-item-label')}>{item.label}</div>
                      <div className={cx('config-item-value')}>{item.value}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
        <OrderDetail
          items={detailItems}
          showPrice={true}
          configParams={configParams}
          onConfigSubmit={handleSubmit}
        />
      </div>
    </div>
  );
};

export default ComputeConfig;
