@import '~@styles/common.less';
@import '../common.less';

.compute-title {
  margin-bottom: 40px !important;
}

.compute-detail-wrapper {
  padding-left: 16px;
  margin-bottom: 40px;
}

.compute-config-wrapper {
  padding-left: 16px;
}

.compute-info-row {
  display: flex;
  margin-bottom: 16px;
  padding-left: 12px;
  .compute-info-col {
    // flex: auto;
    display: flex;
    width: 418px;
  }

  .compute-info-col-label {
    font-size: 12px;
    color: #151b26;
    font-weight: bold;
    min-width: 96px;
  }

  .compute-info-col-value {
    font-size: 12px;
    color: #070c14;
  }
}

.compute-config-table {
  display: inline-flex;
  border: 1px solid #edeef5;
  border-radius: 8px;
  overflow: hidden;
  margin-left: 12px;
  .config-before {
    width: 400px;
  }
  .config-after {
    width: 600px;
  }

  .config-title {
    height: 40px;
    line-height: 40px;
    font-size: 12px;
    color: #151b26;
    font-weight: bold;
    padding-left: 20px;
    background-color: #f8fafc;
  }

  .config-content {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0 20px;
    .config-item {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      align-items: center;
      .config-item-label {
        font-size: 12px;
        color: #151b26;
        font-weight: bold;
      }
      .config-item-value {
        font-size: 12px;
        color: #151b26;
      }
      .config-item-tip {
        font-size: 12px;
        color: #84868c;
      }
    }
  }
}
