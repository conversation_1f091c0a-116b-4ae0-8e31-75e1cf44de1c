# notebook开发指南
databuilder中的notebook是基于db-jupyter-react组件开发的。db-jupyter-react组件位于packages/db-jupyter-react目录下，使用npm workspace管理，因此在db中调试时不需要link，而是通过在package.json中配置workspaces来实现。

## 调试步骤：
1. 在package.json中配置workspaces
```json
"workspaces": [
  "packages/*"
]
```

2. 重新安装依赖
```bash
npm install
```

3. 打包db-jupyter-react
进入packages/db-jupyter-react目录，执行打包命令
```bash
npm run build
```

4. 重新启动db
```bash
npm run dev
```
此时db中的notebook组件就已经更新为最新的db-jupyter-react组件了。

5. 调试完成后，发布db-jupyter-react组件到npm

6. 发布完成后，删除db中的workspaces配置，并更新package.json中的db-jupyter-react依赖版本



## 本地启动jupyter server
notebook组件依赖于jupyter server，因此需要本地启动jupyter server。

1. 安装miniforge
安装miniforge是为了使用conda（conda 是一个开源的包管理和环境管理工具）来安装jupyter server。
安装指南：https://github.com/conda-forge/miniforge

2. 新建conda环境
进入packages/db-jupyter-react目录，执行新建conda环境命令

配置pip镜像源
```bash
mkdir -p ~/.pip
vim ~/.pip/pip.conf
```
写入如下内容（以清华镜像为例）
```ini
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
```

创建conda环境
```bash
conda env create -f environment.yml
```

3. 激活conda环境
```bash
conda activate jupyter
```

4. 启动jupyter server
使用项目中的jupyter_server_config.py配置文件启动jupyter server
```bash
jupyter server --config=jupyter_server_config.py
```
