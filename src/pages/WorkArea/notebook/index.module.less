.notebook-wrapper {
  padding: 0 !important;
}

.notebook-content {
  height: calc(100% - 96px);
}

:global(.jp-Cell[data-windowed-list-index='0']) {
  margin-top: 36px;
}

:global(.jp-Notebook .jp-WindowedPanel-viewport) {
  padding: 0 16px 12px !important;
}

// 解决 Notebook 中 Cell 输入提示符因浏览器未按 utf-8 编码导致 • 符号显示乱码问题
:global(.jp-Notebook .jp-Cell.jp-mod-dirty .jp-InputPrompt::before) {
  color: var(--jp-warn-color1);
  content: '\2022' !important;
}
