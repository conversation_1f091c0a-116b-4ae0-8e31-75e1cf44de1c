import React, {useState} from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import MetaDataTree from './components/MetaDataTree';
import HotkeysHelp from './components/HotkeysHelp';
import {Tooltip} from 'acud';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import MetaIcon from '@assets/originSvg/notebook/meta.svg';
import {TooltipType} from '@components/AuthComponents/constants';

const cx = classNames.bind(styles);

export default function LeftSidebar() {
  const authList = useWorkspaceAuth([Privilege.CatalogMenu]);
  const canViewCatalog = authList[Privilege.CatalogMenu];
  const [activeKey, setActiveKey] = useState('');

  // 快捷键帮助弹窗状态
  const [hotkeysHelpVisible, setHotkeysHelpVisible] = useState(false);

  const sidebarItems = [
    {
      key: 'meta-data',
      label: '元数据',
      disabled: !canViewCatalog,
      icon: MetaIcon
    }
  ];

  function onIconClick(key: string) {
    setActiveKey((pre) => (pre === key ? '' : key));
  }

  return (
    <div className={cx('left-sidebar')}>
      <div className={cx('sidebar-icons-wrap')}>
        <div className={cx('sidebar-icons')}>
          {sidebarItems.map((item) => {
            return (
              <AuthComponents
                key={item.key}
                isAuth={!item.disabled}
                tooltipType={TooltipType.Function}
                placement="topLeft"
              >
                <Tooltip key={item.key} title={item.label} placement="right">
                  <div
                    className={cx('sidebar-icon', item.key, {
                      active: activeKey === item.key,
                      disabled: item.disabled
                    })}
                    onClick={() => {
                      if (!item.disabled) {
                        onIconClick(item.key);
                      }
                    }}
                  >
                    <item.icon />
                  </div>
                </Tooltip>
              </AuthComponents>
            );
          })}
        </div>
        <Tooltip title="快捷键" placement="right">
          <div
            className={cx('sidebar-icon', 'sidebar-keymap')}
            onClick={() => setHotkeysHelpVisible(true)}
          ></div>
        </Tooltip>
      </div>
      <div className={cx('sidebar-content', {hide: !activeKey})}>
        {activeKey === 'meta-data' ? <MetaDataTree onClose={() => setActiveKey('')} /> : null}
      </div>
      <HotkeysHelp visible={hotkeysHelpVisible} onClose={() => setHotkeysHelpVisible(false)} />
    </div>
  );
}
