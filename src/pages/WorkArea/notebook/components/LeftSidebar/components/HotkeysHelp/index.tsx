import React, {useState, useEffect} from 'react';
import {Modal} from 'acud';
import {HotkeyConfig} from '@hooks/useHotkeys';
import {hotkeyMap} from '@pages/WorkArea/notebook/hotkeys';
import classNames from 'classnames/bind';
import styles from './index.module.less';

const cx = classNames.bind(styles);

interface HotkeysHelpProps {
  visible: boolean;
  onClose: () => void;
}

const HotkeysHelp: React.FC<HotkeysHelpProps> = ({visible, onClose}) => {
  const [hotkeys, setHotkeys] = useState<HotkeyConfig[]>([]);

  // 获取当前激活的所有快捷键
  useEffect(() => {
    if (visible) {
      const allHotkeys = hotkeyMap;
      const activeHotkeys: HotkeyConfig[] = [];
      allHotkeys.forEach((value, key) => {
        activeHotkeys.push(value);
      });

      setHotkeys(activeHotkeys);
    }
  }, [visible]);

  // 格式化快捷键显示
  const formatHotkey = (hotkey: HotkeyConfig): string => {
    const parts: string[] = [];

    if (hotkey.metaKey) parts.push('⌘');
    if (hotkey.ctrlKey) parts.push('Ctrl');
    if (hotkey.altKey) parts.push('Alt');
    if (hotkey.shiftKey) parts.push('Shift');

    parts.push(hotkey.key);

    return parts.join(' + ');
  };

  return (
    <Modal title="快捷键" visible={visible} onCancel={onClose} footer={null} width={328}>
      {hotkeys.map((hotkey) => (
        <div className={cx('hotkey-item')} key={hotkey.key}>
          <span className={cx('hotkey-description')}>{hotkey.description}</span>
          <span className={cx('hotkey-key')}>
            <span className={cx('hotkey-key-text')}>{formatHotkey(hotkey)}</span>
          </span>
        </div>
      ))}
    </Modal>
  );
};

export default HotkeysHelp;
