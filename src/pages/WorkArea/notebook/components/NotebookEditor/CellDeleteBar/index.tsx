import classNames from 'classnames/bind';
import styles from './index.module.less';
import {ICellSidebarProps, NotebookCommandIds} from '@baidu/db-jupyter-react/lib/components/notebook';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import DeleteIcon from '@assets/originSvg/notebook/delete.svg';

const cx = classNames.bind(styles);

export default function CellAddBar(props: ICellSidebarProps) {
  const {commands, model, extraPayload} = props;
  const {canView, canExecute, canModify, canManage} = extraPayload.privilege;

  const disabled = !(canModify || canManage);
  function onDelete() {
    return () => {
      if (disabled) return;
      commands.execute(NotebookCommandIds.deleteCells).catch((reason) => {
        console.error('Failed to delete cells.', reason);
      });
    };
  }

  return (
    <AuthComponents isAuth={canModify || canManage} placement="left">
      <DeleteIcon className={cx('cell-delete-bar', {disabled})} onClick={onDelete()} />
    </AuthComponents>
  );
}
