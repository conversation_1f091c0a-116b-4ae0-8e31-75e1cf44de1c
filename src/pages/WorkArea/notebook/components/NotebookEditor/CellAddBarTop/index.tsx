import classNames from 'classnames/bind';
import styles from './index.module.less';
import {ICellSidebarProps, NotebookCommandIds} from '@baidu/db-jupyter-react/lib/components/notebook';
import {cellMetaKey} from '@pages/WorkArea/notebook/config';
import AuthComponents from '@components/AuthComponents/AuthComponents';

const cx = classNames.bind(styles);

export default function CellAddBar(props: ICellSidebarProps) {
  const {commands, extraPayload} = props;
  const {canView, canExecute, canModify, canManage} = extraPayload.privilege;
  const disabled = !(canModify || canManage);

  function onInsertAbove(type) {
    return () => {
      if (disabled) return;
      const cellType = type === 'Markdown' ? 'markdown' : 'code';
      commands
        .execute(NotebookCommandIds.insertAbovePro, {cellType, metadata: {[cellMetaKey]: {language: type}}})
        .catch((reason) => {
          console.error('Failed to insert above.', reason);
        });
    };
  }

  return (
    <div className={cx('cell-add-bar-top', {disabled})}>
      <div className={cx('cell-add-bar-content')}>
        <AuthComponents isAuth={canModify || canManage}>
          <div className={cx('item')} onClick={onInsertAbove('Python')}>
            +Python
          </div>
        </AuthComponents>
        <AuthComponents isAuth={canModify || canManage}>
          <div className={cx('item')} onClick={onInsertAbove('SQL')}>
            +SQL
          </div>
        </AuthComponents>
        <AuthComponents isAuth={canModify || canManage}>
          <div className={cx('item')} onClick={onInsertAbove('Markdown')}>
            +Markdown
          </div>
        </AuthComponents>
      </div>
    </div>
  );
}
