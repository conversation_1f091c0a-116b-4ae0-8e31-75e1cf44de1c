import classNames from 'classnames/bind';
import styles from './index.module.less';
import {ICellSidebarProps, NotebookCommandIds} from '@baidu/db-jupyter-react/lib/components/notebook';
import {cellMetaKey} from '@pages/WorkArea/notebook/config';
import AuthComponents from '@components/AuthComponents/AuthComponents';

const cx = classNames.bind(styles);

export default function CellAddBar(props: ICellSidebarProps) {
  const {commands, extraPayload} = props;
  const {canView, canExecute, canModify, canManage} = extraPayload.privilege;
  const disabled = !(canModify || canManage);

  function onInsertBelow(type) {
    return () => {
      if (disabled) return;
      const cellType = type === 'Markdown' ? 'markdown' : 'code';
      commands
        .execute(NotebookCommandIds.insertBelowPro, {cellType, metadata: {[cellMetaKey]: {language: type}}})
        .catch((reason) => {
          console.error('Failed to insert below.', reason);
        });
    };
  }

  return (
    <div className={cx('cell-add-bar', {disabled})}>
      <div className={cx('cell-add-bar-content')}>
        <AuthComponents isAuth={canModify || canManage}>
          <div className={cx('item')} onClick={onInsertBelow('Python')}>
            +Python
          </div>
        </AuthComponents>
        <AuthComponents isAuth={canModify || canManage}>
          <div className={cx('item')} onClick={onInsertBelow('SQL')}>
            +SQL
          </div>
        </AuthComponents>
        <AuthComponents isAuth={canModify || canManage}>
          <div className={cx('item')} onClick={onInsertBelow('Markdown')}>
            +Markdown
          </div>
        </AuthComponents>
      </div>
    </div>
  );
}
