import {useState, useEffect, useContext} from 'react';
import {
  Notebook,
  CellSidebarExtension,
  useNotebookStore
} from '@baidu/db-jupyter-react/lib/components/notebook';
import {<PERSON><PERSON><PERSON>} from '@baidu/db-jupyter-react/lib/jupyter';
import classNames from 'classnames/bind';
import CellTitleBar from './CellTitleBar';
import CellAddBar from './CellAddBar';
import CellAddBarStyles from './CellAddBar/index.module.less';
import {INotebookContent} from '@jupyterlab/nbformat';
import {useNotebook} from '@store/notebookStatehooks';
import {ServiceManager, ServerConnection} from './service';
import CellDeleteBar from './CellDeleteBar';
import CellDeleteBarStyles from './CellDeleteBar/index.module.less';
import CellAddBarTop from './CellAddBarTop';
import CellAddBarTopStyles from './CellAddBarTop/index.module.less';
import {notebookMetaKey} from '../../config';
import {WorkspaceContext} from '@pages/index';
import flags from '@/flags';
import {NotebookPrivilege} from '../../../utils';

const isPrivate = flags.DatabuilderPrivateSwitch;

const cx = classNames.bind(CellAddBarStyles);
const cy = classNames.bind(CellDeleteBarStyles);
const cz = classNames.bind(CellAddBarTopStyles);

interface NotebookEditorProps {
  id: string;
  nbformat: INotebookContent;
  privilege: NotebookPrivilege;
}

export default function NotebookEditor({id, privilege, nbformat}: NotebookEditorProps) {
  const {canView, canExecute} = privilege;
  const {sessionId, connectStatus, updateNotebookSessionId, updateNotebookKernelId, updateNotebookComputeId} =
    useNotebook();
  const notebookStore = useNotebookStore();
  const {workspaceId} = useContext(WorkspaceContext);

  const [serviceManager, setServiceManager] = useState<ServiceManager>();

  useEffect(() => {
    if (!workspaceId) {
      return;
    }
    const pathPrifix = isPrivate ? '/databuilder' : '';
    const domain = window.location.origin;
    const baseUrl = `${domain}${pathPrifix}/api/databuilder/v1/workspaces/${workspaceId}`;
    const wsUrl = `${domain.replace(/^https?/, (match) => (match === 'https' ? 'wss' : 'ws'))}${pathPrifix}/api/databuilder/ws/v1/workspaces/${workspaceId}`;
    const serverSettings = ServerConnection.makeSettings({
      baseUrl,
      wsUrl,
      init: {
        mode: 'cors',
        credentials: 'include',
        cache: 'no-store'
      }
    });
    const serviceManager = new ServiceManager({serverSettings});
    setServiceManager(serviceManager);
  }, []);

  // useEffect(() => {
  //   async function reconnectSession() {
  //     const {metadata} = nbformat;
  //     const dbNotebookMetaData = metadata[notebookMetaKey] || {};
  //     console.log('dbNotebookMetaData', dbNotebookMetaData);
  //     const {computeId, sessionId, kernelId} = dbNotebookMetaData as any;
  //     if (computeId && sessionId && kernelId) {
  //       const sessions = serviceManager!.sessions;
  //       await sessions.ready;
  //       await sessions.refreshRunning();
  //       const sessionRunnings = Array.from(sessions.running());
  //       const session = sessionRunnings.find((s) => s.id === sessionId);
  //       if (session) {
  //         updateNotebookSessionId(sessionId);
  //         updateNotebookKernelId(kernelId);
  //         updateNotebookComputeId(computeId);
  //       }
  //     }
  //   }
  //   if (serviceManager) {
  //     reconnectSession();
  //   }
  // }, [nbformat, serviceManager]);

  useEffect(() => {
    if (!serviceManager) {
      return;
    }
    async function initKernel() {
      if (connectStatus !== 'CONNECTED') {
        const notebookAdapter = notebookStore.selectNotebookAdapter(id);
        notebookAdapter?.disposeSession();
      } else {
        const {sessions} = serviceManager!;
        await sessions.ready;
        await sessions.refreshRunning();
        const sessionRunnings = Array.from(sessions.running());
        const model = sessionRunnings.find((s) => s.id === sessionId);
        // const model = sessionRunnings[0];
        if (!model) {
          return;
        }
        const session = sessions.connectTo({
          model: {
            ...model,
            path: model.path,
            name: model.name
          },
          kernelConnectionOptions: {
            handleComms: true
          }
        });
        const notebookAdapter = notebookStore.selectNotebookAdapter(id);
        (notebookAdapter?.context?.sessionContext as any)._handleNewSession(session);
      }
    }
    if (sessionId) {
      initKernel();
    }
  }, [sessionId, connectStatus, serviceManager]);

  return (
    <>
      {serviceManager && (
        <Jupyter serviceManager={serviceManager as any}>
          <Notebook
            readonly={canView}
            cellSidebarMargin={20}
            nbformat={nbformat}
            id={id}
            height="calc(100vh - 180px)" // (Height - Toolbar Height).
            extensions={[
              new CellSidebarExtension({
                factory: CellTitleBar,
                extraPayload: {notebookId: id, workspaceId, privilege}
              }),
              new CellSidebarExtension({
                factory: CellAddBar,
                sidebarClassName: cx('cell-add-bar-wrapper'),
                extraPayload: {privilege}
              }),
              new CellSidebarExtension({
                factory: CellDeleteBar,
                sidebarClassName: cy('cell-delete-bar-wrapper'),
                extraPayload: {privilege}
              }),
              new CellSidebarExtension({
                factory: CellAddBarTop,
                sidebarClassName: cz('cell-add-bar-top-wrapper'),
                shouldShow: (model, index) => index === 0,
                extraPayload: {privilege}
              })
            ]}
          />
        </Jupyter>
      )}
    </>
  );
}
