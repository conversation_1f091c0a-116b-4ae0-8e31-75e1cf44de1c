.notebook-name {
  display: inline-flex;
  align-items: center;
  width: 300px;
  position: relative;
}

.input-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.name-display {
  font-size: 22px;
  line-height: 32px;
  font-weight: 500;
  color: #151b26;
  cursor: pointer;
  padding: 0 8px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  &.readonly {
    cursor: text;
    &:hover {
      background-color: transparent;
    }
  }
}

.name-input {
  box-sizing: border-box;
  height: 32px;
  font-size: 16px;
  font-weight: 500;
  color: #151b26;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 8px;
  width: 100%;
  outline: none;

  &:focus {
    border-color: #2468f2;
  }

  &.error {
    border-color: #f33e3e;

    &:focus {
      border-color: #f33e3e;
    }
  }
}

.error-message {
  font-weight: 400;
  color: #f33e3e;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 4px;
  white-space: nowrap;
}
