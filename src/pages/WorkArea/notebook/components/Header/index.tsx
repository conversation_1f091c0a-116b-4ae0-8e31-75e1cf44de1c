import {useState, useContext, useEffect, useRef} from 'react';
import {Button, DialogBox, Tooltip} from 'acud';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {useNotebookAction} from '../../hook';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import useUrlState from '@ahooksjs/use-url-state';
import {WorkspaceContext} from '@pages/index';
import ComputeSelector from './ComputeSelector';
import NotebookName from './NotebookName';
import {useNotebookStore} from '@baidu/db-jupyter-react/lib/components/notebook';
import {useNotebook} from '@store/notebookStatehooks';
import {NotebookPrivilege} from '../../../utils';
import AuthComponents from '@components/AuthComponents/AuthComponents';
const cx = classNames.bind(styles);

export default function Header({
  notebookId,
  notebookName,
  onNameChange,
  notebookPrivilege
}: {
  notebookId: string;
  notebookName: string;
  onNameChange?: (newName: string) => void;
  notebookPrivilege: NotebookPrivilege;
}) {
  const navigate = useNavigate();
  const {
    runAll: onRun,
    updateNotebookName,
    updateSessionFileName
  } = useNotebookAction(notebookId, notebookPrivilege);
  const [urlState] = useUrlState();
  const {workspaceId} = useContext(WorkspaceContext);
  const notebookStore = useNotebookStore();
  const notebook = notebookStore.selectNotebook(notebookId);

  const {canModify, canExecute, canView, canManage} = notebookPrivilege;

  // 第一次渲染时，将 notebook 的 dirty 置为 false
  const firstRender = useRef(true);
  useEffect(() => {
    if (!firstRender.current) {
      return;
    }
    if (notebook?.adapter?.notebookPanel.model) {
      setTimeout(() => {
        notebook.adapter.notebookPanel.model.dirty = false;
        firstRender.current = false;
      }, 100);
    }
  }, [notebook?.adapter]);

  const goWorkArea = () => {
    navigate(`${urls.workArea}?workspaceId=${workspaceId}&folderId=${urlState.folderId}`);
  };

  // 处理名称变更
  const handleNameChange = async (newName: string) => {
    if (newName !== notebookName) {
      try {
        const {success, result} = await updateNotebookName(newName);
        if (success) {
          const finalName = result.name;
          console.log('更新笔记本名称成功:', finalName);
          // 更新 session 名称
          updateSessionFileName(finalName);
          onNameChange?.(finalName);
        }
      } catch (error) {
        console.error('更新笔记本名称失败:', error);
      }
    }
  };

  const disableRunning = notebook?.connectStatus !== 'CONNECTED';
  return (
    <div className={cx('header')}>
      <div className={cx('title')}>
        <NotebookName readOnly={!canManage} name={notebookName} onNameChange={handleNameChange} />
      </div>
      <div className={cx('actions')}>
        <div className={cx('action-item')}>
          <AuthComponents isAuth={!canView} placement="bottom">
            <ComputeSelector />
          </AuthComponents>
        </div>
        <div className={cx('action-item')}>
          <AuthComponents isAuth={!canView} placement="bottom">
            <Tooltip placement="bottom" title={disableRunning ? '请先连接实例' : null}>
              <Button type="primary" onClick={onRun} disabled={disableRunning}>
                <span className={cx('run-icon')}></span>
                <span>全部运行</span>
              </Button>
            </Tooltip>
          </AuthComponents>
        </div>
        <div className={cx('action-item')}>
          <Button
            onClick={() => {
              goWorkArea();
            }}
          >
            退出
          </Button>
        </div>
      </div>
    </div>
  );
}
