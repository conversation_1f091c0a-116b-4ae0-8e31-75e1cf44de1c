import {SinkNameRule} from '@api/integration/cdc-type';
import urls from '@utils/urls';
import {JsPlumbIdEnum} from './constants';
import {BatchTableColumns, Partitioning} from '@api/integration/batch';
import {RULE} from '@utils/regs';

// 获取元数据的volume链接
export const getMetaUrlTable = (volumePath, workspaceId) => {
  const pathArr = volumePath?.replace()?.split('.');
  const catalog = pathArr[0];
  const schema = pathArr[1];
  const node = pathArr[2];
  return `${urls.metaData}?workspaceId=${workspaceId}&catalog=${catalog}&schema=${schema}&node=${node}&type=table`;
};

// 根据前后缀获取目的端表名
export const getTableName = (tableName, sinkNameRule, {prefix, suffix}) => {
  const prefixStr = [SinkNameRule.AddPrefix, SinkNameRule.AddPrefixAndSuffix].includes(sinkNameRule)
    ? prefix
    : '';
  const suffixStr = [SinkNameRule.AddSuffix, SinkNameRule.AddPrefixAndSuffix].includes(sinkNameRule)
    ? suffix
    : '';
  return `${prefixStr}${tableName}${suffixStr}`;
};

/** 转换不合法的字段类型 */
export const transInvalidType = (data) => {
  const {type, precision, scale} = data;
  if (type === 'VARCHAR') {
    return precision > 0 && precision <= 65522 ? data : {...data, precision: 65522};
  }
  if (type === 'CHAR') {
    return precision > 0 && precision <= 255 ? data : {...data, precision: 255};
  }
  if (type === 'DECIMAL') {
    return precision > 0 && precision <= 38 && precision > scale && scale >= 0
      ? data
      : {...data, precision: 38, scale: 18};
  }
  if (type === 'DATETIME') {
    return precision > 0 && precision <= 6 ? data : {...data, precision: 6};
  }
  return data;
};

// 目的端-自动建表字段
export const targetColumnsDealId = (columns) => {
  return columns.map((item) => {
    const data = {
      id: `${JsPlumbIdEnum.TARGET}${item.name}-${String(new Date().getTime())}`,
      defaultValue: '',
      nullable: false,
      description: '',
      precision: 0,
      scale: 0,
      isPrimaryKey: false,
      function: '',
      functionParameter: 0,
      comment: '',
      ...item
    };
    return transInvalidType(data);
  });
};

// 目的端-已有表字段
export const transChoiceTableColumns = (obj) => {
  return targetColumnsDealId(
    obj.columns?.map((item) => ({
      type: item.typeName,
      dbType: item.typeName,
      precision: item.typePrecision ?? 0,
      scale: item.typeScale ?? 0,
      ...item
    }))
  );
};

// 检查目标表字段名称是否重复
export const checkTargetTableName = (tableData: Partial<BatchTableColumns>[], index?: number) => {
  const text = tableData[index]?.name;
  if (!text) {
    return '请输入字段名称';
  }
  if (
    tableData.filter((item) => {
      return item.name === text;
    }).length > 1
  ) {
    return '字段名称不能重复';
  }
  if (!RULE.specialName50.test(text)) {
    return RULE.specialNameStartEn64Text;
  }
  return '';
};
// 检查分区表字段名称是否重复
export const checkPartitioningTableName = (tableData: Partitioning[], index?: number) => {
  const record = tableData[index];
  const text = tableData[index].name;
  let warningText = '';
  if (!text) {
    warningText = '字段名称不能为空';
  }
  if (tableData.some((item, i) => item.name === text && i !== index && item.function === record.function)) {
    warningText = '字段名称+转换函数 不能重复';
  }
  return warningText;
};
// 检查目标表字段
export const checkTargetTable = (tableData: BatchTableColumns[]): boolean => {
  for (let i = 0; i < tableData.length; i++) {
    if (checkTargetTableName(tableData, i)) {
      return false;
    }
  }
  return true;
};
// 检查目标表字段主键是否在最上面
export const checkTargetTablePrimaryKeyOrder = (tableData) => {
  const primaryKeyIndices = tableData
    .map((item, index) => (item.isPrimaryKey ? index : -1))
    .filter((index) => index !== -1);
  for (let i = 0; i < primaryKeyIndices.length; i++) {
    if (primaryKeyIndices[i] !== i) {
      return '主键字段必须放置在表的最前面，请调整字段顺序';
    }
  }

  return '';
};

// 获取tree e.checked 全部已选择子节点
export const getAllNodeChildren = (node, arr) => {
  if (node?.children?.length) {
    node.children.forEach((item) => getAllNodeChildren(item, arr));
  }
  arr.push(node.key);
  return arr;
};
