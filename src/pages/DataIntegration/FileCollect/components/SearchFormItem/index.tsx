import IconSvg from '@components/IconSvg';
import {Input, Select} from 'acud';
import React from 'react';
import styles from './index.module.less';

const Option = Select.Option;
interface SearchFormItemProps {
  onChange: (obj: {keyword: string; value: string}) => void;
  searchObj: {
    keyword: string;
    value: string;
  };
  searchFn: () => void;
}

const SearchFormItem: React.FC<SearchFormItemProps> = ({onChange, searchObj, searchFn}) => {
  const handleChange = (value: string) => {
    onChange({value: '', keyword: value});
  };

  return (
    <div>
      <Input
        allowClear
        className={styles['search-input']}
        value={searchObj.value}
        onChange={(e) => {
          onChange({...searchObj, value: e.target.value});
        }}
        onPressEnter={searchFn}
        addonBefore={
          <Select defaultValue="namePattern" style={{width: 115}} onChange={handleChange}>
            <Option value="namePattern">任务名称</Option>
            <Option value="sourceConnectionIdFilter">源端数据源</Option>
            <Option value="sinkPathPattern">目标端路径</Option>
          </Select>
        }
        suffix={<IconSvg onClick={searchFn} fill="none" type="search" size={16} />}
        placeholder="请输入"
      />
    </div>
  );
};

export default SearchFormItem;
