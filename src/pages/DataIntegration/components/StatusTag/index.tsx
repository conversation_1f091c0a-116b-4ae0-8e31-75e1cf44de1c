/**
 * 集成 - 列表状态tag组件
 * <AUTHOR>
 */
import React from 'react';
import styles from './index.module.less';
import IconSvg from '@components/IconSvg';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);
// 样式类型枚举
export enum StatusTagType {
  RUNNING = 'running',
  SUCCESS = 'success',
  FAILED = 'failed',
  SUSPENDING = 'suspending',
  STOPPED = 'stopped'
}
interface Props {
  // 标签文字
  text: string;
  // 标签样式
  className: StatusTagType;
  // 其他样式
  style?: object;
  // 标签点击事件
  onClick?: () => void;
}
const StatusTag: React.FC<Props> = ({text, className, style, onClick}) => {
  const hasClick = onClick && typeof onClick === 'function';
  return (
    <div className={cx(styles['status-tag'], className)} style={style}>
      <span className={cx(styles['tag-text'])}>{text || '未知状态'}</span>
      {hasClick && <IconSvg type="precheck" fill="none" size={10} onClick={onClick} />}
    </div>
  );
};
export default StatusTag;
