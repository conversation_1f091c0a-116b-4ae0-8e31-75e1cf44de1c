.status-tag {
  padding: 0 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  flex-wrap: nowrap;
  min-width: 55px;
  border: 0.5px solid;
  gap: 2px;
  .tag-text {
    font-size: 12px;
    line-height: 18px;
  }
  :global(.acudicon) {
    cursor: pointer;
  }
  &.running {
    color: #333aff;
    background-color: #f6f6ff;
    border-color: #c7c9ff;
    :global(.acudicon) {
      &:hover {
        color: #2127d9;
      }
    }
  }
  &.success {
    color: #00b97c;
    background-color: #e6faf1;
    border-color: #b6f0da;
    :global(.acudicon) {
      &:hover {
        color: #009468;
      }
    }
  }
  &.failed {
    color: #fa423c;
    background-color: #ffeceb;
    border-color: #ffc3bd;
    :global(.acudicon) {
      &:hover {
        color: #d42828;
      }
    }
  }
  &.suspending {
    color: #ff7e0d;
    background-color: #fff4e6;
    border-color: #ffd09e;
    :global(.acudicon) {
      &:hover {
        color: #d95e00;
      }
    }
  }
  &.stopped {
    color: #495366;
    background-color: #f7f9fc;
    border-color: #e4e8f0;
    :global(.acudicon) {
      &:hover {
        color: #000000;
      }
    }
  }
}
