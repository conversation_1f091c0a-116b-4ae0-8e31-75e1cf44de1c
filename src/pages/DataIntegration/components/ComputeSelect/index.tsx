import {Engine, getComputeResourceList, TASK_INSTANCE_STATUS} from '@api/Compute';
import {WorkspaceContext} from '@pages/index';
import {Select} from 'acud';
import {useMemoizedFn, useRequest} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';

const {Option} = Select;

interface ComputeSelectProps {
  value?: {computeId: string; name: string};
  onChange?: (value: {computeId: string; name: string}) => void;
}

const ComputeSelect: React.FC<ComputeSelectProps> = ({value, onChange}) => {
  const {workspaceId} = useContext(WorkspaceContext);

  const [computeId, setComputeId] = useState<string>();

  const {
    data: computeList,
    loading: computeListLoading,
    run: computeListRun
  } = useRequest(
    () => getComputeResourceList({workspaceId, engine: Engine.Seatunnel, pageNo: 1, pageSize: 10000}),
    {
      manual: true
    }
  );

  useEffect(() => {
    computeListRun();
  }, []);

  useEffect(() => {
    setComputeId(value?.computeId);
  }, [value]);

  const onSelect = useMemoizedFn((value: string) => {
    const compute = computeList?.result.computes?.find((item: any) => item.computeId === value);
    setComputeId(value);
    onChange({computeId: value, name: compute?.name});
  });

  return (
    <Select
      placeholder="请选择计算实例"
      value={computeId}
      loading={computeListLoading}
      onSelect={onSelect}
      style={{width: 504}}
    >
      {computeList?.result.computes?.map((item: any) => (
        <Option
          key={item.computeId}
          value={item.computeId}
          disabled={item.status !== TASK_INSTANCE_STATUS.RUNNING}
        >
          {item.name}
        </Option>
      ))}
    </Select>
  );
};

export default ComputeSelect;
