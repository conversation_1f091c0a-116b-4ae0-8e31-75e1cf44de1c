import React from 'react';
import ReactMonacoEditor from 'react-monaco-editor';

/**
 * SqlEdit
 */
const SqlEdit: React.FC<{
  value?: string;
  onChange?: (value: string) => void;
  isReadOnly?: boolean;
  height?: number;
}> = ({value, onChange, isReadOnly = false, height = 300}) => {
  // 编辑器挂载完成 配置主题和语言规则
  const handleEditorDidMount = (editor, monaco) => {
    // 创建自定义主题
    monaco.editor.defineTheme('sqlTheme', {
      base: 'vs',
      inherit: true,
      rules: [
        {token: 'string.key.json', foreground: 'D97116'}, // JSON 键名
        {token: 'string.value.json', foreground: '1B9908'}, // JSON 字符串值
        {token: 'number.json', foreground: '2468F2'}, // JSON 数字
        {token: 'keyword.json', foreground: '8F5CFF'} // JSON 关键字（true, false, null）
      ],
      colors: {
        'editor.background': '#FBFBFC', // 编辑器背景色
        'minimap.background': '#F2F2F4' // 小地图背景色
      }
    });

    // 应用主题
    monaco.editor.setTheme('logTheme');
  };

  return (
    <div
      style={{
        height,
        width: '100%'
      }}
    >
      <ReactMonacoEditor
        theme="sqlTheme"
        height="100%"
        language="sql"
        value={value}
        editorDidMount={handleEditorDidMount}
        options={{
          readOnly: isReadOnly,
          padding: {top: 8}, // ✅ 设置编辑器上边距为 8px
          scrollBeyondLastLine: false, // 关闭滚动超出最后一行
          automaticLayout: true,
          wordWrap: 'off',
          minimap: {
            enabled: true
          }
        }}
        onChange={(value) => {
          onChange(value);
        }}
      />
    </div>
  );
};

export default SqlEdit;
