import {FieldType} from '@api/integration/type';
import {InputNumber, Space} from 'acud';
import React, {useMemo} from 'react';

interface FieldPrecisionConfigProps {
  type: FieldType;
  isReadOnly?: boolean;
  precision: number;
  scale?: number;
  onChange: (fieldName: string, value: number) => void;
}
// 精度设置
const FieldPrecisionConfig: React.FC<FieldPrecisionConfigProps> = ({
  type,
  isReadOnly,
  precision,
  scale,
  onChange
}) => {
  const renderReadOnly = useMemo(() => {
    const hasPrecision = [FieldType.Decimal, FieldType.Varchar, FieldType.Char, FieldType.Datetime];
    const hasScale = [FieldType.Decimal];
    return (
      <Space>
        {hasPrecision.includes(type) ? `(${precision}${hasScale.includes(type) ? `, ${scale}` : ''})` : '-'}
      </Space>
    );
  }, [precision, scale, type]);
  const renderByType = useMemo(() => {
    const config = {
      [FieldType.Decimal]: (
        <Space>
          <InputNumber
            style={{width: 60}}
            size="small"
            value={precision}
            min={1}
            max={38}
            onChange={(e) => onChange('precision', e)}
          />
          <InputNumber
            style={{width: 60}}
            size="small"
            value={scale}
            min={0}
            max={38}
            onChange={(e) => onChange('scale', e)}
          />
        </Space>
      ),
      [FieldType.Varchar]: (
        <InputNumber
          style={{width: 60}}
          size="small"
          value={precision}
          min={1}
          max={255}
          onChange={(e) => onChange('precision', e)}
        />
      ),
      [FieldType.Char]: (
        <InputNumber
          style={{width: 60}}
          size="small"
          value={precision}
          min={1}
          max={65522}
          onChange={(e) => onChange('precision', e)}
        />
      ),
      [FieldType.Datetime]: (
        <InputNumber
          style={{width: 60}}
          size="small"
          value={precision}
          min={1}
          max={6}
          onChange={(e) => onChange('precision', e)}
        />
      )
    };
    return Object.keys(config).includes(type) ? config[type] : '-';
  }, [onChange, precision, scale, type]);

  return <Space>{isReadOnly ? renderReadOnly : renderByType}</Space>;
};

export default FieldPrecisionConfig;
