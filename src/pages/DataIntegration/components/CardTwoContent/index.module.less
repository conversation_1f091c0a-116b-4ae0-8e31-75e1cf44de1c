.card-two-content {
  display: flex;
  gap: 12px;
  position: relative;
  padding-right: 24px;

  // flex 倒序
  // flex-direction: row-reverse;
}
.content-divider {
  flex: 0 0 32px;
  display: flex;
  padding-top: 4px;
  // align-items: center;
  justify-content: center;
}
.card-wrapper {
  flex: 0 0 calc(50% - 16px);
  overflow: auto;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  .card-header {
    height: 32px;
    background-color: #f5f5f5;
    display: flex;
    /* 节点配置备份 */

    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    padding: 6px 16px;
    color: #151b26;

    .header-left {
      flex: 1;
    }
    .header-right {
      flex: 1;
    }
  }

  .card-content {
    padding: 20px 20px 0 20px;
  }
}
