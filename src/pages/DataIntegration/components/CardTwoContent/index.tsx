import IconSvg from '@components/IconSvg';
import React from 'react';
import styles from './index.module.less';

/**
 * 卡片内容组件
 * @param param0
 * @returns
 */
const CardTwoContent: React.FC<{
  left: React.ReactNode;
  right: React.ReactNode;
  showDivider?: boolean;
  paddingTop?: number;
  title?: string[];
}> = ({left, right, showDivider = true, title = [], paddingTop = 20}) => {
  return (
    <div className={styles['card-two-content']}>
      <div className={styles['card-wrapper']}>
        <div className={styles['card-header']}>
          <div className={styles['header-left']}>{title[0]}</div>
        </div>
        <div className={styles['card-content']} style={{paddingTop}}>
          <div className={styles['content-left']}>{left}</div>
        </div>
      </div>

      <div className={styles['content-divider']}>
        {showDivider && <IconSvg type="bold-right" size={24} />}
      </div>

      <div className={styles['card-wrapper']}>
        <div className={styles['card-header']}>
          <div className={styles['header-right']}>{title[1]}</div>
        </div>
        <div className={styles['card-content']} style={{paddingTop}}>
          <div className={styles['content-right']}>{right}</div>
        </div>
      </div>
    </div>
  );
};

export default CardTwoContent;
