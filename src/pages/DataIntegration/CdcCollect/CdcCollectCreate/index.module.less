.container {
  padding: 20px 32px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
  margin: 10px;
  border-radius: 6px;
  height: calc(100% - 60px);
  overflow-y: auto;
  position: relative;

  .title {
    font-weight: 500;
    font-size: 20px;
    color: #151b26;
    margin-bottom: 16px;
    height: 28px;
  }

  .step-container {
    width: 842px;
    align-self: center;
  }

  .content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    margin-bottom: 70px;
  }
}
