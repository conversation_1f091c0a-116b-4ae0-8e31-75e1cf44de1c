/**
 * 实时集成-创建
 */

import React, {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {CreateStep, CreateStepConfig} from './constants';
import {Button, Form, Loading, Space, Steps} from 'acud';

import styles from './index.module.less';
import CdcSourceSinkConfig from './components/CdcSourceSinkConfig';
import CdcBatchConfig from './components/CdcBatchConfig';
import CdcRunConfig from './components/CdcRunConfig';
import CdcWritingConfig from './components/CdcWritingConfig';
import IconSvg from '@components/IconSvg';
import {IntegrationTab} from '@pages/DataIntegration/constants';
import urls from '@utils/urls';
import {useNavigate} from 'react-router-dom';
import {useDispatch, useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import {useRequest} from 'ahooks';
import {getJobDetails} from '@api/integration';
import {JobType} from '@api/integration/type';
import {WorkspaceContext} from '@pages/index';
import useUrlState from '@ahooksjs/use-url-state';
import {targetColumnsDealId} from '@pages/DataIntegration/utils';
import {SourceTypeEnum} from '@api/integration/cdc-type';
import {getCreateStepConfig} from './utils';

const {Step} = Steps;

export const getFormByConfig = (config) => {
  return config.map((item) => {
    return item.isShow ? (
      <Form.Item
        key={item.name}
        name={item.name}
        label={item.label}
        rules={item?.rules || []}
        extra={item?.extra}
        noStyle={item?.noStyle}
        preserve={false}
      >
        {item.component}
      </Form.Item>
    ) : null;
  });
};

const CdcCollectCreate: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState({jobId: ''});
  const {step, baseConfig, sinkConfig} = useSelector((state: IAppState) => state.cdcIntegrationSlice);

  const stepConfig = useMemo(() => getCreateStepConfig(sinkConfig.isAutoCreated), [sinkConfig.isAutoCreated]);

  // 获取详情数据
  const {loading, run: getDetail} = useRequest(
    () => getJobDetails<JobType.CDC>(workspaceId, urlState.jobId),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.success) {
          const {sourceConfig, sinkConfig, mappingConfig, commonConfig, isJobStarted, ...baseConfig} =
            res.result;
          dispatch({
            type: 'cdcIntegration/updateAllConfig',
            payload: {
              isJobStarted,
              baseConfig,
              sourceConfig,
              sinkConfig: {
                ...sinkConfig,
                isAutoCreated: Number(sinkConfig.isAutoCreated)
              },
              tableConfigs: mappingConfig.tableConfigs.map((item) => ({
                ...item,
                sinkFields: targetColumnsDealId(item.sinkFields)
              })),
              commonConfig
            }
          });
        }
      },
      refreshDeps: [workspaceId, urlState.jobId]
    }
  );

  useEffect(() => {
    dispatch({
      type: 'cdcIntegration/reset'
    });
    if (urlState.jobId) {
      getDetail();
    }
  }, [dispatch, getDetail, urlState.jobId]);

  const onChangeStep = useCallback(
    (current: number) => {
      dispatch({
        type: 'cdcIntegration/updateStep',
        payload: current
      });
    },
    [dispatch]
  );

  const stepContent = useMemo(
    () => ({
      [CreateStep.SourceSinkConfig]: <CdcSourceSinkConfig />,
      [CreateStep.RunConfig]: <CdcRunConfig />,
      [CreateStep.BatchConfig]: <CdcBatchConfig />,
      [CreateStep.WritingConfig]: <CdcWritingConfig />
    }),
    []
  );

  const backToList = useCallback(() => {
    navigate(`${urls.integration}?tab=${IntegrationTab.Realtime}`);
  }, [navigate]);

  return (
    <div className={styles['container']}>
      <Space className={styles['title']} onClick={backToList}>
        <IconSvg type="left" size={16} color="#151B26" />
        {urlState.jobId ? `编辑 ${baseConfig.name}` : '创建库表实时采集'}
      </Space>
      <Steps current={step} size="small" onChange={onChangeStep} className={styles['step-container']}>
        {Object.keys(stepConfig).map((item) => (
          <Step key={item} title={CreateStepConfig[item]} />
        ))}
      </Steps>
      {/* <Loading loading={loading}> */}
      <div className={styles['content']}>{stepContent[step]}</div>
      {/* </Loading> */}
    </div>
  );
};

export default CdcCollectCreate;
