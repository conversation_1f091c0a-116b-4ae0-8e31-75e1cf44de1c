import {InputNumber, Select, Tooltip} from 'acud';
import React, {useCallback, useEffect, useMemo} from 'react';
import IntegrationEditTable from '../IntegrationEditTable';
import {CdcTableConfig, PartitionFunction, SinkField} from '@api/integration/cdc-type';
import {ColumnsType} from 'acud/lib/table';
import {PartitionFunctionOptions} from '../../constants';
import {isNumber} from 'lodash';
import {useDispatch, useSelector} from 'react-redux';
import {produce} from 'immer';
import {IAppState} from '@store/index';
import {initialFieldConfig} from '@store/cdcIntegrationSlice';
import {checkPartitioningTableName} from '@pages/DataIntegration/utils';
import IconSvg from '@components/IconSvg';
import styles from './index.module.less';

interface CdcSinkPartitionTableProps {
  isReadOnly: boolean;
  tableConfig: CdcTableConfig;
  tableIndex: number;
}

/**
 * 分区表
 */
const CdcSinkPartitionTable: React.FC<CdcSinkPartitionTableProps> = ({
  tableConfig,
  isReadOnly,
  tableIndex
}) => {
  const dispatch = useDispatch();
  const {tableConfigs} = useSelector((state: IAppState) => state.cdcIntegrationSlice);
  // 字段名称选项
  const fieldsNameOptions = useMemo(() => {
    return tableConfig.sinkFields
      .filter((item) => item)
      .map((item) => ({value: item.name, label: item.name}));
  }, [tableConfig.sinkFields]);
  // 字段变化
  const handleChange = useCallback(
    (index: number, key: string, value) => {
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          draft[tableIndex].sinkPartitions[index][key] = value;
        })
      });
    },
    [dispatch, tableConfigs, tableIndex]
  );

  // 拖拽
  const onRowChange = useCallback(
    (value) => {
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          draft[tableIndex].sinkPartitions = value;
        })
      });
    },
    [dispatch, tableConfigs, tableIndex]
  );

  /**
   * 添加字段
   */
  const onAdd = useCallback(() => {
    dispatch({
      type: 'cdcIntegration/updateTableConfigs',
      payload: produce(tableConfigs, (draft) => {
        draft[tableIndex].sinkPartitions.push({...initialFieldConfig, function: PartitionFunction.Identity});
      })
    });
  }, [dispatch, tableConfigs, tableIndex]);

  // 删除
  const onDelete = useCallback(
    (index) => {
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          draft[tableIndex].sinkPartitions.splice(index, 1);
        })
      });
    },
    [dispatch, tableConfigs, tableIndex]
  );
  /** 分区配置 */
  const columns: ColumnsType<SinkField> = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      key: 'index',
      render: (text, record, index) => index + 1
    },
    {
      title: '字段名称',
      width: 160,
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: SinkField, index: number) => {
        if (isReadOnly) {
          return <span>{text}</span>;
        }
        const warningText = checkPartitioningTableName(tableConfig.sinkPartitions, index);
        return (
          <div className="flex items-center gap-2">
            <Select
              size="small"
              value={text}
              onSelect={(e) => handleChange(index, 'name', e)}
              options={fieldsNameOptions}
              style={{width: 150}}
            />
            {warningText && (
              <Tooltip title={warningText}>
                <IconSvg type="warning" color="red" />
              </Tooltip>
            )}
          </div>
        );
      }
    },
    {
      title: '转换函数',
      width: 120,
      dataIndex: 'function',
      key: 'function',
      render: (text: string, record: SinkField, index: number) => {
        if (isReadOnly) {
          return <span>{text}</span>;
        }
        return (
          <Select
            size="small"
            value={text}
            onSelect={(e) => handleChange(index, 'function', e)}
            options={PartitionFunctionOptions}
          />
        );
      }
    },
    {
      title: '参数',
      dataIndex: 'functionParameter',
      width: 120,
      key: 'functionParameter',
      render: (text: number, record: SinkField, index: number) => {
        if (isReadOnly) {
          return <span>{isNumber(text) ? text : '-'}</span>;
        }
        if (record.function === PartitionFunction.Identity) {
          return '-';
        }
        return (
          <InputNumber
            size="small"
            value={text}
            onChange={(e) => handleChange(index, 'functionParameter', e)}
          />
        );
      }
    }
  ];

  return (
    <div>
      <div className={styles['title']}>分区字段</div>
      <IntegrationEditTable
        columns={columns}
        data={tableConfig.sinkPartitions}
        isReadOnly={isReadOnly}
        onAdd={tableConfig.isAutoCreated ? onAdd : null}
        onDelete={onDelete}
        onDataChange={onRowChange}
        onDrag
      />
    </div>
  );
};

export default CdcSinkPartitionTable;
