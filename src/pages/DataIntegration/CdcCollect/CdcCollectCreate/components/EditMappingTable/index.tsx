import {SinkType, SourceMapping} from '@api/integration/cdc-type';
import React, {useCallback, useEffect, useMemo, useRef} from 'react';

import styles from './index.module.less';
import classNames from 'classnames/bind';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {Button, Space, toast} from 'acud';
import useUrlState from '@ahooksjs/use-url-state';
import SourceTable from '../SourceTable';
import {BatchTableColumns} from '@api/integration/batch';
import {useMemoizedFn} from 'ahooks';
import {IAppState} from '@store/index';
import {useDispatch, useSelector} from 'react-redux';
import CdcSinkTable from '../SinkTable';
import {MappingType, MappingTypeOptions} from '../../constants';
import CardTwoContent from '@pages/DataIntegration/components/CardTwoContent';
import {BrowserJsPlumbInstance, newInstance} from '@jsplumb/browser-ui';
import {JsPlumbIdEnum} from '@pages/DataIntegration/constants';
import {DetailTab} from '@pages/DataIntegration/CdcCollect/contants';
import {produce} from 'immer';
import {DDLStatementMonaco} from '@pages/DataIntegration/components/DDLStatementMonaco';
import CdcSinkPartitionTable from '../SinkPartition';
const cx = classNames.bind(styles);
interface EditMappingTableProps {
  // 当前表的序号
  tableIndex: number;
  sourceData: BatchTableColumns[];
  sourceTable: string;
  mapping: SourceMapping[];
  onChangeMapping: (mapping) => void;
  initialAutoCreated?: (sourceData, sourceTable, tableIndex) => void;
}

/**
 * 映射表单，主要处理连线逻辑
 */
const EditMappingTable: React.FC<EditMappingTableProps> = ({
  tableIndex,
  sourceData,
  sourceTable,
  mapping,
  onChangeMapping,
  initialAutoCreated
}) => {
  const [urlState] = useUrlState();
  const dispatch = useDispatch();
  const isFromDetail = urlState.tab === DetailTab.DetailConfig;
  const {tableConfigs, sinkConfig} = useSelector((state: IAppState) => state.cdcIntegrationSlice);
  // 当前表的配置
  const tableConfig = useMemo(() => tableConfigs[tableIndex], [tableConfigs, tableIndex]);

  const isReadOnly = useMemo(
    () => isFromDetail || tableConfig?.sinkFullName,
    [isFromDetail, tableConfig?.sinkFullName]
  );

  /** 映射数据 */
  const containerRef = useRef<HTMLDivElement>(null);
  /** jsPlumb实例 */
  const jsPlumbInstance = useRef<BrowserJsPlumbInstance>(null);

  /** 映射处理 计算连线 并设置 mapping 表单数据 */
  const dealMapResult = useMemoizedFn(() => {
    // 等待 dom 加载完成  计算连线数量
    setTimeout(() => {
      const mapping: SourceMapping[] = [];
      jsPlumbInstance.current?.connections.forEach((item) => {
        const sourceId = item.source.id;
        let targetId = '';
        const sourceName = sourceId.replace(JsPlumbIdEnum.SOURCE, '');
        for (const className of item.target.classList) {
          if (className.startsWith(JsPlumbIdEnum.TARGET) && className !== JsPlumbIdEnum.TARGET) {
            targetId = className;
            break;
          }
        }
        const sourceItem = sourceData.find((item) => item.name === sourceName);
        const targetItem = tableConfig.sinkFields.find((item) => item.id === targetId);
        // 判断避免重复连线
        const sourceIdArr = mapping.map((item) => item.sourceColumn);
        const isTargetIdExist = mapping.some((item) => item.targetId === targetId);

        const isExist = sourceIdArr.includes(sourceId.replace(JsPlumbIdEnum.SOURCE, '')) || isTargetIdExist;

        if (sourceItem && targetItem && !isExist) {
          mapping.push?.({sourceColumn: sourceItem.name, sinkColumn: targetItem.name, targetId});
        }
      });
      onChangeMapping(mapping);
    }, 100);
  });

  /** 点击映射 */
  const handleMap = useMemoizedFn((type: MappingType) => {
    jsPlumbInstance.current?.deleteEveryConnection();
    const num = Math.min(sourceData.length, tableConfig.sinkFields.length);
    const map = new Map<string, BatchTableColumns>();
    sourceData.forEach((item) => {
      map.set(item.name, item);
    });
    switch (type) {
      case MappingType.SameName:
        requestAnimationFrame(() => {
          tableConfig.sinkFields?.forEach((item) => {
            const sourceItem = map.get(item.name);
            if (sourceItem) {
              jsPlumbInstance.current?.connect({
                source: document.getElementById(JsPlumbIdEnum.SOURCE + sourceItem.name),
                target: document.querySelectorAll(`[data-row-key="${item.id}"]`)[0],
                anchors: ['Right', 'Left']
              });
            }
          });
        });
        break;
      case MappingType.SameRow:
        requestAnimationFrame(() => {
          for (let i = 0; i < num; i++) {
            const sourceEl = document.getElementById(JsPlumbIdEnum.SOURCE + sourceData[i].name);
            const targetEl = document.querySelectorAll(`[data-row-key="${tableConfig.sinkFields[i].id}"]`)[0];
            if (sourceEl && targetEl) {
              jsPlumbInstance.current?.connect({
                source: sourceEl,
                target: targetEl,
                anchors: ['Right', 'Left']
              });
            }
          }
          jsPlumbInstance.current?.repaintEverything();
        });
        break;
      case MappingType.CancelMap:
        break;
    }
    dealMapResult();
  });

  /** 判断连线是否存在 */
  const isExistLine = useMemoizedFn((sourceId: string, targetId: string) => {
    const sourceIdArr = mapping.map((item) => item.sourceColumn);
    const isTargetIdExist = mapping.some((item) => item.targetId === targetId);
    const isExist = sourceIdArr.includes(sourceId.replace(JsPlumbIdEnum.SOURCE, '')) || isTargetIdExist;

    if (isExist) {
      toast.error({
        message: '已经存在相同的连接，禁止重复连线',
        duration: 5
      });
      return false; // 阻止创建重复连接
    }
    dealMapResult();
    return true;
  });

  /** 初始化jsPlumb */
  const initJsPlumbFn = useCallback(() => {
    if (!containerRef.current) return;
    const jsPlumb = newInstance({
      container: containerRef.current!,
      endpointStyle: {
        strokeWidth: 10,
        type: 'None'
      },
      hoverPaintStyle: {stroke: '#f56c6c', strokeWidth: 2}, // 连线 hover 样式

      connectionOverlays: [
        {
          type: 'Arrow',
          options: {
            stroke: '#f56c6c',
            location: 1,
            width: 6,
            length: 6,
            direction: 1,
            foldback: 0.8
          }
        }
      ]
    });

    jsPlumbInstance.current = jsPlumb;
    // 删除连线 重新计算映射
    jsPlumbInstance.current?.bind('beforeDetach', () => {
      dealMapResult();
      return true;
    });

    jsPlumbInstance.current?.bind('beforeDrop', (info) => {
      return isExistLine(info.sourceId, info.targetId);
    });
  }, [dealMapResult, isExistLine]);

  /** 初始化源端 */
  const initSource = useMemoizedFn(() => {
    if (!jsPlumbInstance.current) return;
    sourceData.forEach((item) => {
      const sourceId = JsPlumbIdEnum.SOURCE + item.name;
      const element = document.getElementById(sourceId);
      if (element && !isReadOnly) {
        jsPlumbInstance.current.manage(element, sourceId);
      }
    });
    jsPlumbInstance.current.addSourceSelector(`.${JsPlumbIdEnum.SOURCE}`, {
      anchor: 'Right'
    });
  });

  /** 初始化目标端 包括 初始化 和添加节点 */
  const initTarget = useMemoizedFn(() => {
    if (!jsPlumbInstance.current) return;
    tableConfig.sinkFields?.forEach((item) => {
      const targetId = item.id;
      const element = document.querySelectorAll(`[data-row-key="${targetId}"]`)[0];
      if (element && !isReadOnly) {
        jsPlumbInstance.current.manage(element, targetId);
      }
    });
    jsPlumbInstance.current.addTargetSelector(`.${JsPlumbIdEnum.TARGET}`, {
      anchor: 'Left'
    });
  });

  /** 初始化连线 */
  const initLine = useMemoizedFn(() => {
    // 删除所有连线
    jsPlumbInstance.current?.deleteEveryConnection();
    // 初始化目标端需要连线已有数据
    mapping?.forEach((item) => {
      const sourceId = document.getElementById(JsPlumbIdEnum.SOURCE + item.sourceColumn);
      const targetClass = document.querySelectorAll(`[data-row-key="${item.targetId}"]`)[0];
      if (sourceId && targetClass) {
        jsPlumbInstance.current?.connect({
          source: sourceId,
          target: targetClass,
          anchors: ['Right', 'Left']
        });
      }
    });
  });

  useEffect(() => {
    // 初始化插件
    setTimeout(() => {
      initJsPlumbFn();
    }, 100);
  }, [initJsPlumbFn]);

  useEffect(() => {
    if (tableConfig?.sinkFields?.length && sourceData?.length) {
      // 初始化source target节点
      setTimeout(() => {
        initSource();
        initTarget();
      }, 200);
      setTimeout(() => {
        initLine();
      }, 400);
    }
  }, [initLine, initSource, initTarget, tableConfig?.sinkFields, sourceData]);

  // 监控屏幕大小变化
  useEffect(() => {
    const handleResize = () => {
      jsPlumbInstance.current?.repaintEverything(); // 刷新整个画布
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const deleteLine = useCallback(() => {
    jsPlumbInstance.current?.deleteEveryConnection();
  }, [jsPlumbInstance]);

  // 渲染源端表
  const renderSourceTable = useMemo(
    () => (
      <div>
        <div className={styles['content-title']}>
          <div className={styles['title']}>
            <Ellipsis tooltip={sourceTable}>{sourceTable}</Ellipsis>
          </div>
          {!isReadOnly && !tableConfig?.isAutoCreated && (
            <Space>
              {MappingTypeOptions.map((item) => (
                <Button onClick={() => handleMap(item.value)} type="actiontext" key={item.value}>
                  {item.label}
                </Button>
              ))}
            </Space>
          )}
        </div>
        <SourceTable tableData={sourceData} isReadOnly={isReadOnly} />
      </div>
    ),
    [handleMap, isReadOnly, sourceData, sourceTable, tableConfig?.isAutoCreated]
  );

  const onDDLChange = useCallback(
    (value) => {
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          draft[tableIndex].createTableStatement = value;
        })
      });
    },
    [dispatch, tableConfigs, tableIndex]
  );

  // doris-渲染ddl语句
  const renderDDL = useMemo(() => {
    const config = sinkConfig;
    const sinkType = config.sinkType;
    const [catalog, schema] = config.sinkPath.split('.');
    if (sinkType === SinkType.Doris && tableConfig.isAutoCreated && tableConfig.sinkFields?.length) {
      return (
        <DDLStatementMonaco
          table={tableConfig.sinkTableName}
          catalog={catalog}
          schema={schema}
          isReadOnly={isReadOnly}
          columns={tableConfig.sinkFields}
          dataSource={sinkType}
          onChange={onDDLChange}
          comment={config?.comment}
          value={tableConfig?.createTableStatement}
        />
      );
    }
    return null;
  }, [
    isReadOnly,
    onDDLChange,
    sinkConfig,
    tableConfig?.createTableStatement,
    tableConfig?.isAutoCreated,
    tableConfig?.sinkFields,
    tableConfig?.sinkTableName
  ]);

  // iceberg 渲染分区表
  const renderPartition = useMemo(() => {
    if (sinkConfig.sinkType === SinkType.Iceberg && tableConfig.isAutoCreated) {
      return (
        <CdcSinkPartitionTable
          tableConfig={tableConfig}
          isReadOnly={isReadOnly || isFromDetail}
          tableIndex={tableIndex}
        />
      );
    }
    return null;
  }, [isFromDetail, isReadOnly, sinkConfig.sinkType, tableConfig, tableIndex]);

  const onDeleteNode = (id) => {
    const element = document.getElementsByClassName(id)[0];
    if (element) {
      jsPlumbInstance.current?.deleteConnectionsForElement(element);
      jsPlumbInstance.current?.removeAllEndpoints(element);
      jsPlumbInstance.current?.unmanage(element);
    }
    jsPlumbInstance.current?.repaintEverything(); // 刷新整个画布
  };

  return (
    <div ref={containerRef} className={cx('map-setting', {'detail-map-setting': isFromDetail})}>
      <CardTwoContent
        left={
          <>
            {/** 源端表 */}
            {renderSourceTable}
          </>
        }
        right={
          <>
            {/** 目标端表 */}
            <CdcSinkTable
              onDeleteNode={onDeleteNode}
              tableConfig={tableConfig}
              tableIndex={tableIndex}
              sourceData={sourceData}
              initialAutoCreated={initialAutoCreated}
              deleteLine={deleteLine}
              onChangeMapping={onChangeMapping}
              mapping={mapping}
            />
            {renderDDL}
            {renderPartition}
          </>
        }
        title={['源端配置', '目标端配置']}
        showDivider
      />
    </div>
  );
};

export default EditMappingTable;
