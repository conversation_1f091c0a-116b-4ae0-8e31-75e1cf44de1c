import {Button, Table} from 'acud';
import React, {useCallback, useMemo, useRef} from 'react';
import useUrlState from '@ahooksjs/use-url-state';
import styles from './index.module.less';
import {DndProvider, useDrag, useDrop} from 'react-dnd';
import {HTML5Backend} from 'react-dnd-html5-backend';
import {produce} from 'immer';
import {DetailTab} from '@pages/DataIntegration/CdcCollect/contants';

interface Props {
  data: any[];
  columns: any[];
  isReadOnly: boolean;
  isEdit?: boolean;
  onDrag?: boolean;
  hiddenDelete?: boolean;
  readOnlyFields?: string[];
  rowClassName?: (record: any) => string;
  onAdd: () => void;
  onDataChange?: (value: any[]) => void;
  onDelete: (index: number) => void;
}
const type = 'DraggableBodyRow';

const DraggableBodyRow = ({index, moveRow, className, style, ...restProps}: any) => {
  const ref = useRef();
  const [{isOver, dropClassName}, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const {index: dragIndex} = (monitor.getItem() as {index: number}) || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward'
      };
    },
    drop: (item: {index: number}) => {
      moveRow(item.index, index);
    }
  });
  const [, drag] = useDrag({
    type,
    item: {index},
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{cursor: 'move', ...style}}
      {...restProps}
    />
  );
};

/**
 * 可编辑表格，支持拖拽
 */
const IntegrationEditTable: React.FC<Props> = ({
  data,
  columns,
  isReadOnly,
  onDrag,
  hiddenDelete = false,
  readOnlyFields = [],
  rowClassName,
  onAdd,
  onDelete,
  onDataChange
}) => {
  const [urlState] = useUrlState();
  // 判断是否来自详情页
  const isFromDetail = urlState.tab === DetailTab.DetailConfig;
  const showColumns = useMemo(
    () => [
      ...columns,
      ...(isFromDetail
        ? []
        : [
            {
              title: '操作',
              dataIndex: 'operation',
              key: 'operation',
              width: 180,
              render: (text, record, index) => {
                return (
                  <div>
                    {/** 暂时不做编辑保存 */}
                    {/* {record.isEdit ? (
              <Button type="actiontext" onClick={() => onDataChange(index, 'isEdit', false)}>
                保存
              </Button>
            ) : (
              <Button type="actiontext" onClick={() => onDataChange(index, 'isEdit', true)}>
                编辑
              </Button>
            )} */}
                    <Button
                      type="actiontext"
                      onClick={() => onDelete(index)}
                      disabled={isReadOnly || hiddenDelete || readOnlyFields.includes(record.name)}
                    >
                      删除
                    </Button>
                  </div>
                );
              }
            }
          ])
    ],
    [columns, hiddenDelete, isFromDetail, isReadOnly, onDelete, readOnlyFields]
  );

  const components = {
    body: {
      row: DraggableBodyRow
    }
  };

  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      const newTableData = produce(data, (draft) => {
        const draggedItem = draft[dragIndex];
        draft.splice(dragIndex, 1); // 删除原位置
        draft.splice(hoverIndex, 0, draggedItem); // 插入新位置
      });
      onDataChange(newTableData);
    },
    [data, onDataChange]
  );

  const onRow: any = (record, index) => ({
    index,
    moveRow
  });
  return (
    <div>
      <DndProvider backend={HTML5Backend}>
        <Table
          dataSource={data}
          columns={showColumns}
          components={isReadOnly || !data?.length || !onDrag ? {} : components}
          pagination={false}
          rowKey="id"
          scroll={{x: 800}}
          rowClassName={rowClassName}
          onRow={onRow}
        />
      </DndProvider>

      {!isReadOnly && onAdd && (
        <Button className={styles['add-button']} onClick={onAdd} type="default">
          + 添加字段
        </Button>
      )}
    </div>
  );
};

export default IntegrationEditTable;
