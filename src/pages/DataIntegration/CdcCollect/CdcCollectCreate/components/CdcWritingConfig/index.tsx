/**
 * 实时集成-创建-第四步-写入设置
 */
import _ from 'lodash';
import {getKey<PERSON>ist, getTreeKey} from '@pages/DataIntegration/CdcCollect/utils';
import {IAppState} from '@store/index';
import React, {Key, useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import styles from './index.module.less';
import IntegrationTableTree from '../IntegrationTableTree';
import {Button, Drawer, Modal, Search, Space, Tag} from 'acud';
import {CdcCreateOperation} from '../CdcCreateOperation';
import IconSvg from '@components/IconSvg';
import EditMappingTable from '../EditMappingTable';
import {CreateGroupTitle} from '@pages/DataIntegration/components/CreateGroupTitle';
import {initialFieldConfig, initialTableConfig} from '@store/cdcIntegrationSlice';
import {produce} from 'immer';
import {getAllNodeChildren, getTableName, targetColumnsDealId} from '@pages/DataIntegration/utils';
import {BatchTableColumns, getDatasourceTableColumns} from '@api/integration/batch';
import {useMemoizedFn} from 'ahooks';
import {SchemaConnectionType} from '../../constants';
import {WorkspaceContext} from '@pages/index';
import {DbTypeMap} from '../../constants';
import {SourceMapping} from '@api/integration/cdc-type';
import {FieldType} from '@api/integration/type';

const CdcWritingConfig: React.FC = () => {
  const dispatch = useDispatch();
  const [checkedKeys, setCheckedKeys] = useState<Key[]>([]);
  const state = useSelector((state: IAppState) => state.cdcIntegrationSlice);

  const {tableConfigs, sourceConfig, sinkConfig, baseConfig, commonConfig, isJobStarted} = state;
  const [editTableIndex, setEditTableIndex] = useState<number>();
  const [editTableName, setEditTableName] = useState<string>();
  const [modalVisible, setModalVisible] = useState(false);
  const {workspaceId} = useContext(WorkspaceContext);

  const [sourceData, setSourceData] = useState<BatchTableColumns[]>([]);
  const [sourceDescription, setSourceDescription] = useState<string>('');

  const {sourceConnectionId, sourceDatabase} = sourceConfig;

  const [searchValue, setSearchValue] = useState<string>('');
  // 暂存页面的mapping，这里不直接dispatch到store的原因是页面渲染和store互相依赖，会出现问题
  // 关闭弹窗的时候会将mapping设置到store
  const [mapping, setMapping] = useState<SourceMapping[]>([]);

  // 初始化 checkedKeys
  useEffect(() => {
    setCheckedKeys(
      _.map(tableConfigs, (item) => getTreeKey([sourceConnectionId, sourceDatabase, item.sourceTable]))
    );
  }, [sourceConnectionId, sourceDatabase, tableConfigs]);

  const onTreeCheck = useCallback(
    (checked, e) => {
      const newCheckedKeys = getAllNodeChildren(e.node, []).filter((item) => getKeyList(item).length === 3);
      // 删除的节点
      const deleteKeys = e.checked ? [] : newCheckedKeys;
      // 添加的节点
      const addKeys = e.checked ? newCheckedKeys.filter((item) => !checkedKeys.includes(item)) : [];
      const deleteSourceTable = deleteKeys.map((newCheckedKey) => {
        const [, , sourceTable] = getKeyList(newCheckedKey as string);
        return sourceTable;
      });
      // 添加节点
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: [
          // 删除表
          ...tableConfigs.filter((item) => !deleteSourceTable.includes(item.sourceTable)),
          // 添加表
          ...addKeys.map((item) => {
            const [, , sourceTable] = getKeyList(item as string);
            return {...initialTableConfig, sourceTable, isAutoCreated: Number(sinkConfig.isAutoCreated)};
          })
        ]
      });

      setCheckedKeys(newCheckedKeys);
    },
    [checkedKeys, dispatch, sinkConfig.isAutoCreated, tableConfigs]
  );

  // 清空
  const onClearChecked = useCallback(() => {
    setCheckedKeys([]);
    dispatch({
      type: 'cdcIntegration/updateTableConfigs',
      payload: []
    });
  }, [dispatch]);

  // 保存当前页面的数据
  const onSave = useCallback(async () => {
    return state;
  }, [state]);

  const transAutoSinkTableFields = useCallback(
    (sourceData) => {
      return targetColumnsDealId(
        sourceData.map((item) => ({
          ...item,
          comment: item.description || '',
          isPrimaryKey: item.primaryKey,
          type: DbTypeMap[sinkConfig.sinkType][item.dbType]
        }))
      );
    },
    [sinkConfig.sinkType]
  );

  // 初始化自动建表目的端
  const initAutoSinkTable = useCallback(
    (sourceData, sourceTable, tableIndex) => {
      const autoSinkFields = transAutoSinkTableFields(sourceData);
      const mapping = autoSinkFields.map((item) => ({
        sourceColumn: item.name,
        sinkColumn: item.name,
        targetId: item.id
      }));
      setMapping(mapping);
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          const {dmlConfig, sinkNameRule, prefix, suffix} = sinkConfig;
          const {sourceSchema} = sourceConfig;
          const logicalDeleteField = dmlConfig.logicalDeleteField
            ? [{...initialFieldConfig, name: dmlConfig.logicalDeleteField, type: FieldType.Boolean}]
            : [];
          draft[tableIndex] = {
            ...draft[tableIndex],
            dmlConfig,
            sourceSchema,
            sourceTable,
            isTableCustomConfig: true,
            sinkTableComment: sourceDescription,
            isAutoCreated: 1,
            sinkTableName: getTableName(sourceTable, sinkNameRule, {prefix, suffix}),
            sinkFields: [...autoSinkFields, ...commonConfig.addedSinkFields, ...logicalDeleteField],
            sinkPartitions: [],
            mapping
          };
        })
      });
    },
    [
      commonConfig.addedSinkFields,
      dispatch,
      sinkConfig,
      sourceConfig,
      sourceDescription,
      tableConfigs,
      transAutoSinkTableFields
    ]
  );

  /** 获取源端表数据 */
  const getSourceTableData = useMemoizedFn(async (sourceTable, tableIndex) => {
    const res = await getDatasourceTableColumns({
      environment: {
        workspaceId,
        computeId: baseConfig?.compute?.computeId
      },
      datasourceInfo: {
        connectionId: sourceConnectionId,
        database: sourceDatabase,
        ...(SchemaConnectionType.includes(sourceConfig?.sourceType)
          ? {schema: sourceConfig?.sourceSchema}
          : {}),
        table: sourceTable
      }
    });
    const sourceData = res?.result?.columns || [];
    setSourceData(sourceData);
    setSourceDescription(res?.result?.description);
    if (tableConfigs[tableIndex].sinkFields.length === 0 && tableConfigs[tableIndex].isAutoCreated) {
      initAutoSinkTable(sourceData, sourceTable, tableIndex);
    }
  });

  // 编辑选中表
  const onEditTable = useCallback(
    (key) => () => {
      const [, , sourceTable] = getKeyList(key);
      const index = tableConfigs.findIndex((item) => item.sourceTable === sourceTable);
      setEditTableIndex(index);
      setEditTableName(sourceTable);
      setModalVisible(true);
      // 初始化连线关系
      setMapping(
        tableConfigs[index].mapping?.map((item) => ({
          sinkColumn: item.sinkColumn,
          sourceColumn: item.sourceColumn,
          targetId: tableConfigs[index].sinkFields?.find((field) => field.name === item.sinkColumn)?.id
        })) || []
      );
      // 请求源端表数据
      getSourceTableData(sourceTable, index);
    },
    [getSourceTableData, tableConfigs]
  );

  // 删除当前的表
  const onDeleteTable = useCallback(
    (key) => () => {
      // checkedKeys 会随着 tableConfigs 变化
      setCheckedKeys((data) => data.filter((item) => item !== key));
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: tableConfigs.filter((item) => {
          const [, , sourceTable] = getKeyList(key as string);
          return item.sourceTable !== sourceTable;
        })
      });
    },
    [dispatch, tableConfigs]
  );

  const checkedTableList = useMemo(() => {
    return checkedKeys
      .filter((key) => {
        if (!searchValue) {
          return key;
        }
        const [, , sourceTable] = getKeyList(key as string);
        return sourceTable.toLowerCase().includes(searchValue.toLowerCase());
      })
      .map((key) => {
        const [, , sourceTable] = getKeyList(key as string);
        // 修改过的有映射关系
        const isEdit = tableConfigs.find((item) => item.sourceTable === sourceTable)?.isTableCustomConfig;
        return (
          <div className={styles['checked-item']} key={key}>
            <Space>
              {sourceTable}
              {isEdit ? (
                <Tag color="inactive" className={styles['edit-tag']}>
                  已修改
                </Tag>
              ) : null}
            </Space>

            <div className={styles['btn-group']}>
              {/** 编辑 */}
              <Button type="actiontext" onClick={onEditTable(key)}>
                <IconSvg type="edit" size={16} color="#84868C" />
              </Button>
              {/** 删除 */}
              <Button type="actiontext" onClick={onDeleteTable(key)} disabled={isJobStarted}>
                <IconSvg type="close" size={16} color="#84868C" />
              </Button>
            </div>
          </div>
        );
      });
  }, [checkedKeys, isJobStarted, onDeleteTable, onEditTable, searchValue, tableConfigs]);

  const onModalCancel = useCallback(() => {
    setModalVisible(false);
    // 保存数据
    dispatch({
      type: 'cdcIntegration/updateTableConfigs',
      payload: produce(tableConfigs, (draft) => {
        draft[editTableIndex].mapping = mapping;
      })
    });
  }, [dispatch, editTableIndex, mapping, tableConfigs]);

  const onSearch = useCallback((value) => {
    setSearchValue(value);
  }, []);

  // mapping 变化回调
  const onChangeMapping = useCallback((mapping) => {
    setMapping(mapping);
  }, []);

  return (
    <div className={styles['container']}>
      <CreateGroupTitle title="选表" />
      <div className={styles['content']}>
        {/** 源端表选择 */}
        <IntegrationTableTree checkedKeys={checkedKeys} onCheck={onTreeCheck} />
        {/** 已选表 */}
        <div className={styles['checked-tree']}>
          <div className={styles['checked-tree-title']}>
            已选表({checkedKeys.length})
            <Button type="actiontext" onClick={onClearChecked} disabled={isJobStarted}>
              清空
            </Button>
          </div>
          <Search placeholder="请搜索表名称" onSearch={onSearch} className={styles['search']} />
          <div className={styles['checked-table-list']}>{checkedTableList}</div>
        </div>
      </div>
      {/** 编辑表映射 */}
      <Drawer
        title={`编辑 ${editTableName}`}
        visible={modalVisible}
        onClose={onModalCancel}
        width={'90%'}
        destroyOnClose
      >
        <EditMappingTable
          tableIndex={editTableIndex}
          sourceData={sourceData}
          sourceTable={editTableName}
          initialAutoCreated={initAutoSinkTable}
          mapping={mapping}
          onChangeMapping={onChangeMapping}
        />
      </Drawer>
      <CdcCreateOperation onSave={onSave} />
    </div>
  );
};

export default CdcWritingConfig;
