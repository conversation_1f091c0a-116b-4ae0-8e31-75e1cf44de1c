/**
 * 实时集成-创建-操作
 */

import React, {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {Button, Loading, Space, toast} from 'acud';
import styles from './index.module.less';
import {CreateStep} from '../../constants';
import {useDispatch, useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import LeaveFromModal from '@components/LeaveFromModal';
import {useMemoizedFn} from 'ahooks';
import urls from '@utils/urls';
import {useNavigate} from 'react-router-dom';
import {IntegrationTab} from '@pages/DataIntegration/constants';
import useUrlState from '@ahooksjs/use-url-state';
import {createIntegrationJob, getIntegrationJobList, updateIntegrationJob} from '@api/integration';
import {JobDetail, JobType} from '@api/integration/type';
import {WorkspaceContext} from '@pages/index';
import _ from 'lodash';
import {CdcIntegrationState} from '@store/cdcIntegrationSlice';
import {getCreateStepConfig} from '../../utils';
import {SinkType} from '@api/integration/cdc-type';
import {checkTargetTablePrimaryKeyOrder} from '@pages/DataIntegration/utils';

interface CdcCreateOperationProps {
  // 点击保存回调（保存当前页面的form到store中）
  onSave: () => Promise<CdcIntegrationState>;
}

export const CdcCreateOperation: React.FC<CdcCreateOperationProps> = ({onSave}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const state = useSelector((state: IAppState) => state.cdcIntegrationSlice);
  const {step, sinkConfig} = state;
  // 离开时间
  const [leaveClickTime, setLeaveClickTime] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [urlState] = useUrlState({jobId: '', isPublished: false});
  const [loading, setLoading] = useState(false);
  const {workspaceId} = useContext(WorkspaceContext);

  const stepConfig = useMemo(() => getCreateStepConfig(sinkConfig.isAutoCreated), [sinkConfig.isAutoCreated]);

  const handlePrevStep = useCallback(() => {
    onSave();
    dispatch({
      type: 'cdcIntegration/updateStep',
      payload: stepConfig[step].beforeStep
    });
  }, [dispatch, onSave, step, stepConfig]);

  const handleNextStep = useCallback(async () => {
    try {
      await onSave();
    } catch {
      return;
    }
    dispatch({
      type: 'cdcIntegration/updateStep',
      payload: stepConfig[step].nextStep
    });
  }, [dispatch, onSave, step, stepConfig]);

  const handleSubmit = useCallback(async () => {
    setLoading(true);
    // 先校验/保存当前表
    try {
      const newConfig = await onSave();

      const {baseConfig, sinkConfig, tableConfigs, sourceConfig, commonConfig} = newConfig;

      // 1. 校验-目标表不能为0
      if (tableConfigs.length === 0) {
        toast.error({message: '请选择表', duration: 5});
        return false;
      }

      // 2. 校验-已有表的目标表不能为空
      tableConfigs.forEach((item) => {
        // 检测是否选择目标表
        if (!item.isAutoCreated && !item.sinkTableName) {
          toast.error({message: `请选择${item.sourceTable}的目标表`, duration: 5});
          throw new Error();
        }
        // 检测映射
        if (!item.isAutoCreated && !item.mapping.length) {
          toast.error({message: `请配置${item.sourceTable}表映射`, duration: 5});
          throw new Error();
        }
        // 3. 校验-doris主键是否排在最上面
        if (sinkConfig.sinkType === SinkType.Doris) {
          const text = checkTargetTablePrimaryKeyOrder(item.sinkFields);
          if (text) {
            toast.error({message: `表${item.sourceTable}${text}`, duration: 5});
            throw new Error();
          }
        }
      });

      // 4. 校验-同名校验
      const listRes = await getIntegrationJobList(workspaceId, {
        pageNo: 1,
        pageSize: 10000,
        type: JobType.CDC,
        namePattern: newConfig.baseConfig.name
      });
      const jobs = listRes.result.jobs.find(
        (item) => item.name === newConfig.baseConfig.name && item?.jobId !== urlState?.jobId
      );
      if (jobs) {
        toast.error({message: '同名集成任务已存在，请修改任务名称', duration: 5});
        return false;
      }

      const allConfig = {
        ...baseConfig,
        sinkConfig: {...sinkConfig, isAutoCreated: Boolean(sinkConfig.isAutoCreated)},
        mappingConfig: {
          tableConfigs: _.map(tableConfigs, (item) => ({
            ...item,
            isAutoCreated: Boolean(item.isAutoCreated),
            sinkFields: item.sinkFields.map((field) => _.omit(field, ['id', 'description', 'primaryKey'])),
            mapping: item.mapping.map((item) => ({
              sinkColumn: item.sinkColumn,
              sourceColumn: item.sourceColumn
            }))
          }))
        },
        sourceConfig,
        commonConfig,
        type: JobType.CDC
      } as JobDetail<JobType.CDC>;

      const res = urlState?.jobId
        ? await updateIntegrationJob<JobType.CDC>(workspaceId, urlState?.jobId, {
            ...allConfig,
            isPublished: urlState.isPublished
          })
        : await createIntegrationJob<JobType.CDC>(workspaceId, allConfig);

      if (res.success) {
        toast.success({message: '保存成功', duration: 5});
        navigate(`${urls.integration}?tab=${IntegrationTab.Realtime}`);
        return true;
      } else {
        setIsEditing(true);
        return false;
      }
    } catch {
      return false;
    } finally {
      setLoading(false);
    }
  }, [navigate, onSave, urlState.isPublished, urlState?.jobId, workspaceId]);

  // 取消
  const handleCancel = useMemoizedFn(() => {
    setLeaveClickTime(Date.now());
    navigate(`${urls.integration}?tab=${IntegrationTab.Realtime}`);
  });
  return (
    <div className={styles['create-operation']}>
      {/* <Loading loading={loading}> */}
      <Space>
        {step !== CreateStep.SourceSinkConfig && (
          <Button type="default" onClick={handlePrevStep} className={styles['button']}>
            上一步
          </Button>
        )}
        {step !== CreateStep.WritingConfig && (
          <Button type="primary" onClick={handleNextStep} className={styles['button']}>
            下一步
          </Button>
        )}
        {step === CreateStep.WritingConfig ? (
          <Button type="default" onClick={handleSubmit} className={styles['button']}>
            保存
          </Button>
        ) : null}
        <Button type="default" onClick={handleCancel} className={styles['button']}>
          取消
        </Button>
      </Space>
      {/* </Loading> */}
      <LeaveFromModal
        leaveClickTime={leaveClickTime}
        isEditing={isEditing}
        onSave={handleSubmit}
        title="当前数据暂未保存，如果现在关闭，未保存的数据内容将丢失。请确认是否要继续关闭"
      />
    </div>
  );
};
