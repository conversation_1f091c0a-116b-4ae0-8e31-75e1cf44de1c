import React from 'react';

import {BatchTableColumns} from '@api/integration/batch';
import IconSvg from '@components/IconSvg';
import Table, {ColumnType} from 'acud/lib/table';
import styles from './index.module.less';
import {Tag} from 'acud';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {JsPlumbIdEnum} from '@pages/DataIntegration/constants';

// 源表格配置
const CdcSourceTable: React.FC<{tableData: BatchTableColumns[]; isReadOnly: boolean}> = ({
  tableData,
  isReadOnly
}) => {
  const sourceColumns: ColumnType<BatchTableColumns>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      render: (text, record, idx: number) => <span>{idx + 1}</span>
    },
    {
      title: '字段名称',
      dataIndex: 'name',
      width: 100,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text}</Ellipsis>;
      }
    },
    {
      title: '字段类型',
      dataIndex: 'type',
      width: 100
    },
    {
      title: '是否非空',
      dataIndex: 'nullable',
      width: 100,
      render: (nullable: boolean) => (nullable ? '是' : '否')
    },
    {
      title: '字段描述',
      dataIndex: 'description',
      width: 100,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text === '' ? '-' : text}</Ellipsis>;
      }
    },
    {
      title: '主键',
      dataIndex: 'primaryKey',
      width: 100,
      fixed: 'right',
      render: (isPrimaryKey: boolean, data: BatchTableColumns) => (
        <div className={styles['source-point-column']}>
          {isPrimaryKey ? (
            <Tag>
              <IconSvg type="sql-partition" size={16} fill="none" />
            </Tag>
          ) : (
            '-'
          )}
          {/** 连线：源端表 */}
          <div
            id={JsPlumbIdEnum.SOURCE + data.name}
            className={
              JsPlumbIdEnum.SOURCE +
              ' ' +
              styles['source-point'] +
              ' ' +
              (isReadOnly ? styles['source-point-readonly'] : '')
            }
          >
            <IconSvg className={'source-point-icon'} type="source-point" size={8} color="#94959A" />
          </div>
        </div>
      )
    }
  ];
  return (
    <div className={styles['source-table-container']}>
      <Table columns={sourceColumns} dataSource={tableData} rowKey="name" pagination={false} />
    </div>
  );
};

export default CdcSourceTable;
