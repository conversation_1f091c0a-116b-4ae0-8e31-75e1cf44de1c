import React, {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import IntegrationEditTable from '../IntegrationEditTable';
import {useDispatch, useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import {CdcTableConfig, SourceMapping} from '@api/integration/cdc-type';
import {Button, Checkbox, Form, Input, Modal, Select, Space, Tag} from 'acud';
import {BatchTableColumns} from '@api/integration/batch';
import {FieldTypeOptions, isAutoCreatedOptions} from '../../constants';
import IconSvg from '@components/IconSvg';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {produce} from 'immer';
import {initialFieldConfig} from '@store/cdcIntegrationSlice';
import FieldPrecisionConfig from '@pages/DataIntegration/components/FieldPrecisionConfig';
import {useAsyncEffect} from 'ahooks';
import {getTableDetail, getTableList} from '@api/metaRequest';
import {WorkspaceContext} from '@pages/index';
import styles from './index.module.less';
import {JsPlumbIdEnum} from '@pages/DataIntegration/constants';
import {checkTargetTableName, transChoiceTableColumns} from '@pages/DataIntegration/utils';
import {RULE} from '@utils/regs';
import useUrlState from '@ahooksjs/use-url-state';

interface CdcSinkTableProps {
  // 当前目的端表的配置
  tableConfig: CdcTableConfig;
  tableIndex: number;
  sourceData: BatchTableColumns[];
  mapping: SourceMapping[];
  initialAutoCreated: (sourceData, sourceTable, tableIndex) => void;
  onDeleteNode: (id) => void;
  deleteLine: () => void;
  onChangeMapping: (mapping) => void;
}
/**
 * 目的端表
 */
const CdcSinkTable: React.FC<CdcSinkTableProps> = ({
  tableConfig,
  tableIndex,
  sourceData,
  mapping,
  initialAutoCreated,
  onDeleteNode,
  deleteLine,
  onChangeMapping
}) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState();
  const {tableConfigs, sinkConfig} = useSelector((state: IAppState) => state.cdcIntegrationSlice);
  const sinkType = sinkConfig.sinkType;
  const [tableOptions, setTableOptions] = useState<{label: string; value: string}[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalKey, setModalKey] = useState<string>();
  const isFromDetail = urlState.tab === 'detail-config';

  /**
   * 字段变化
   */
  const handleChange = useCallback(
    (index: number, key: string, value) => {
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          draft[tableIndex].sinkFields[index][key] = value;
        })
      });
    },
    [dispatch, tableConfigs, tableIndex]
  );

  const handleNameChange = useCallback(
    (index: number, value) => {
      const beforeValue = tableConfigs[tableIndex].sinkFields[index].name;
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          draft[tableIndex].sinkFields[index].name = value;
        })
      });
      const isMapping = mapping?.find((item) => item.sinkColumn === beforeValue);
      if (isMapping) {
        onChangeMapping(
          mapping.map((item) => {
            if (item.sinkColumn === beforeValue) {
              return {
                ...item,
                sinkColumn: value,
                targetId: tableConfigs[tableIndex].sinkFields[index].id
              };
            }
            return item;
          })
        );
      }
    },
    [dispatch, mapping, onChangeMapping, tableConfigs, tableIndex]
  );
  /**
   * 拖拽
   */
  const onRowChange = useCallback(
    (value) => {
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          draft[tableIndex].sinkFields = value;
        })
      });
    },
    [dispatch, tableConfigs, tableIndex]
  );

  // 不能编辑
  const notEdit = useMemo(
    () => isFromDetail || tableConfig?.sinkFullName || !tableConfig.isAutoCreated,
    [isFromDetail, tableConfig?.sinkFullName, tableConfig.isAutoCreated]
  );

  // 只读
  const readOnly = useMemo(
    () => isFromDetail || tableConfig?.sinkFullName,
    [isFromDetail, tableConfig?.sinkFullName]
  );

  // 当前行不能编辑（逻辑删除字段）
  const readOnlyRow = useMemo(
    () => [sinkConfig?.dmlConfig?.logicalDeleteField].filter((item) => item),
    [sinkConfig?.dmlConfig?.logicalDeleteField]
  );

  const isRowReadOnly = useCallback((rowName) => readOnlyRow.includes(rowName), [readOnlyRow]);

  /** 表格列配置 */
  const columns = useMemo(() => {
    return [
      {
        title: '序号',
        width: 50,
        dataIndex: 'index',
        render: (text: string, record, index: number) => <div>{index + 1}</div>,
        fixed: 'left'
      },
      {
        title: '字段名称',
        dataIndex: 'name',
        width: 120,
        key: 'name',
        fixed: 'left',
        render: (text: string, record, index: number) => {
          if (notEdit || isRowReadOnly(record.name)) {
            return text;
          }
          const warningText = checkTargetTableName(tableConfig.sinkFields, index);
          return (
            <Input
              size="small"
              value={text}
              onChange={(e) => handleNameChange(index, e.target.value)}
              // warningText={warningText}
              // warningPopover={!!warningText}
              tips={warningText}
            />
          );
        }
      },
      {
        title: '字段类型',
        dataIndex: 'type',
        width: 170,
        key: 'type',
        render: (text: string, record, index) =>
          notEdit || isRowReadOnly(record.name) ? (
            <div>{text}</div>
          ) : (
            <Select
              value={text}
              size="small"
              onSelect={(e) => handleChange(index, 'type', e)}
              options={FieldTypeOptions[sinkType]}
              style={{width: 150}}
            />
          )
      },
      {
        title: '精度',
        dataIndex: 'precision',
        width: 150,
        key: 'precision',
        render: (text: string, record, index) => (
          <FieldPrecisionConfig
            isReadOnly={notEdit || isRowReadOnly(record.name)}
            type={record.type}
            precision={record.precision}
            scale={record.scale}
            onChange={(fieldName, value) => handleChange(index, fieldName, value)}
          />
        )
      },
      {
        title: '主键',
        dataIndex: 'isPrimaryKey',
        key: 'isPrimaryKey',
        width: 70,
        render: (text: boolean, record, index) =>
          readOnly || isRowReadOnly(record.name) ? (
            text ? (
              <Tag>
                <IconSvg type="sql-partition" size={16} fill="none" />
              </Tag>
            ) : (
              '-'
            )
          ) : (
            <Checkbox
              checked={text}
              onChange={(e) => handleChange(index, 'isPrimaryKey', e.target.checked)}
            />
          )
      },
      {
        title: '默认值',
        dataIndex: 'defaultValue',
        key: 'defaultValue',
        width: 100,
        render: (text: string, record, index) =>
          notEdit || isRowReadOnly(record.name) ? (
            <div>{text === '' ? '-' : text}</div>
          ) : (
            <Input
              size="small"
              value={text}
              onChange={(e) => handleChange(index, 'defaultValue', e.target.value)}
            />
          )
      },
      {
        title: '字段描述',
        dataIndex: 'comment',
        key: 'comment',
        width: 100,
        render: (text: string, record, index: number) =>
          notEdit || isRowReadOnly(record.name) ? (
            <Ellipsis tooltip={text}>{text === '' ? '-' : text}</Ellipsis>
          ) : (
            <Input
              size="small"
              value={text}
              onChange={(e) => handleChange(index, 'comment', e.target.value)}
            />
          )
      }
    ];
  }, [handleChange, handleNameChange, isRowReadOnly, notEdit, readOnly, sinkType, tableConfig.sinkFields]);

  /**
   * 添加字段
   */
  const onAdd = useCallback(() => {
    dispatch({
      type: 'cdcIntegration/updateTableConfigs',
      payload: produce(tableConfigs, (draft) => {
        draft[tableIndex].sinkFields.push({
          ...initialFieldConfig,
          id: JsPlumbIdEnum.TARGET + String(new Date().getTime())
        });
      })
    });
  }, [dispatch, tableConfigs, tableIndex]);

  // 删除字段
  const onDelete = useCallback(
    (index) => {
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          draft[tableIndex].sinkFields.splice(index, 1);
        })
      });
      onDeleteNode(tableConfigs[tableIndex].sinkFields[index].id);
    },
    [dispatch, onDeleteNode, tableConfigs, tableIndex]
  );

  const onAutoCreatedChange = useCallback(
    (value) => {
      if (mapping.length) {
        onChangeMapping([]);
        deleteLine();
        // 删除之前的节点
        tableConfig.sinkFields.forEach((item) => {
          onDeleteNode(item.id);
        });
      }
      setTimeout(() => {
        // 自动建表，初始化字段
        if (value) {
          initialAutoCreated(sourceData, tableConfig.sourceTable, tableIndex);
        } else {
          // 已有表，清空
          dispatch({
            type: 'cdcIntegration/updateTableConfigs',
            payload: produce(tableConfigs, (draft) => {
              draft[tableIndex] = {
                ...draft[tableIndex],
                isAutoCreated: value,
                sinkFields: [],
                sinkPartitions: [],
                sinkTableName: '',
                sinkTableComment: ''
              };
            })
          });
        }
      }, 200);
    },
    [
      mapping?.length,
      onChangeMapping,
      deleteLine,
      tableConfig.sinkFields,
      tableConfig.sourceTable,
      onDeleteNode,
      initialAutoCreated,
      sourceData,
      tableIndex,
      dispatch,
      tableConfigs
    ]
  );

  useAsyncEffect(async () => {
    if (!tableConfig?.isAutoCreated) {
      const [catalogName, schemaName] = sinkConfig.sinkPath.split('.');
      const res = await getTableList(workspaceId, {catalogName, schemaName});
      const options = res.result.tables.map((item) => ({
        label: item,
        value: item
      }));
      setTableOptions(options);
    }
  }, [sinkConfig.sinkPath, tableConfig?.isAutoCreated, workspaceId]);

  // 已有表选择
  const onTableSelect = useCallback(
    async (value) => {
      onChangeMapping([]);
      deleteLine();
      // 删除之前的节点
      tableConfig.sinkFields.forEach((item) => {
        onDeleteNode(item.id);
      });
      const res = await getTableDetail(workspaceId, `${sinkConfig.sinkPath}.${value}`);
      const sinkFields = transChoiceTableColumns(res.result);
      const mapping = sinkFields.reduce((pre, cur) => {
        if (sourceData.find((item) => item.name === cur.name)) {
          return [
            ...pre,
            {
              sourceColumn: cur.name,
              sinkColumn: cur.name
            }
          ];
        } else {
          return pre;
        }
      }, []);
      onChangeMapping(mapping);
      dispatch({
        type: 'cdcIntegration/updateTableConfigs',
        payload: produce(tableConfigs, (draft) => {
          draft[tableIndex].sinkTableName = value;
          draft[tableIndex].sinkFields = sinkFields;
          draft[tableIndex].mapping = mapping;
          draft[tableIndex].isTableCustomConfig = true;
        })
      });
    },
    [
      onChangeMapping,
      deleteLine,
      tableConfig.sinkFields,
      workspaceId,
      sinkConfig.sinkPath,
      dispatch,
      tableConfigs,
      onDeleteNode,
      sourceData,
      tableIndex
    ]
  );

  // 打开编辑字段/描述弹窗
  const openEditModal = useCallback(
    (key) => () => {
      setModalVisible(true);
      setModalKey(key);
      form.setFieldValue(key, tableConfigs[tableIndex][key]);
    },
    [form, tableConfigs, tableIndex]
  );

  // 保存编辑 任务名称/描述
  const onUpdateConfig = useCallback(() => {
    dispatch({
      type: 'cdcIntegration/updateTableConfigs',
      payload: produce(tableConfigs, (draft) => {
        draft[tableIndex][modalKey] = form.getFieldValue(modalKey);
      })
    });
    setModalVisible(false);
  }, [dispatch, form, modalKey, tableConfigs, tableIndex]);

  // 编辑弹窗
  const renderEditModal = useMemo(() => {
    const editInput = {
      sinkTableName: {
        name: '目的表名称',
        component: <Input placeholder="请输入目的表名称" limitLength={256} forbidIfLimit width={400} />,
        rules: [
          {required: true, message: '请输入目的表名称'},
          {
            pattern: RULE.workflowName,
            message: RULE.workflowNameText
          }
        ]
      },
      sinkTableComment: {
        name: '目的表描述',
        rules: [{max: 500, message: '不超过500字符'}],
        component: (
          <Input.TextArea
            placeholder="请输入目的表描述"
            limitLength={500}
            autoSize={{minRows: 3, maxRows: 6}}
            width={504}
          />
        )
      }
    };
    if (!modalKey) {
      return null;
    }
    const {name, component, rules} = editInput[modalKey];
    return (
      <Modal
        title={`编辑 ${name}`}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={onUpdateConfig}
        getContainer={() => document.body}
      >
        <Form form={form}>
          <Form.Item label={name} name={modalKey} rules={rules}>
            {component}
          </Form.Item>
        </Form>
      </Modal>
    );
  }, [form, modalKey, modalVisible, onUpdateConfig]);

  return (
    <>
      <Space className={styles['header']}>
        <div>
          <span className={styles['title']}>建表方式：</span>
          <Select
            options={isAutoCreatedOptions}
            value={Number(tableConfig.isAutoCreated)}
            onChange={onAutoCreatedChange}
            disabled={readOnly}
            size="small"
          />
        </div>
        {tableConfig.isAutoCreated ? null : (
          <div>
            <span className={styles['title']}>已有表选择：</span>
            <Select
              options={tableOptions}
              value={tableConfig.sinkTableName}
              onChange={onTableSelect}
              style={{width: 200}}
              disabled={readOnly}
              showTitle
              size="small"
            />
          </div>
        )}
        {tableConfig.isAutoCreated ? (
          <>
            <div className={styles['title']}>
              目的表名称：{tableConfig.sinkTableName}
              {isFromDetail || (
                <Button type="actiontext" onClick={openEditModal('sinkTableName')} disabled={notEdit}>
                  <IconSvg type="edit" size={16} />
                </Button>
              )}
            </div>
            <div className={styles['description']}>
              目的表描述：{tableConfig.sinkTableComment || '-'}
              {isFromDetail || (
                <Button type="actiontext" onClick={openEditModal('sinkTableComment')} disabled={notEdit}>
                  <IconSvg type="edit" size={16} />
                </Button>
              )}
            </div>
          </>
        ) : null}
      </Space>
      <div className={styles['table-container']}>
        <IntegrationEditTable
          columns={columns}
          data={tableConfig.sinkFields}
          isReadOnly={readOnly}
          readOnlyFields={readOnlyRow}
          hiddenDelete={readOnly}
          onAdd={tableConfig.isAutoCreated ? onAdd : null}
          onDelete={onDelete}
          isEdit={Boolean(tableConfig.isAutoCreated)}
          rowClassName={(record) => `${record.id} ${JsPlumbIdEnum.TARGET}`}
          onDataChange={onRowChange}
          onDrag
        />
      </div>
      {renderEditModal}
    </>
  );
};

export default CdcSinkTable;
