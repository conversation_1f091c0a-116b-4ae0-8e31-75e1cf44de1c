import {getDatasourceTables} from '@api/integration/batch';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import {useRequest} from 'ahooks';
import React, {Key, useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import styles from './index.module.less';
import {Loading, Search, Tree} from 'acud';
import {getTreeKey} from '@pages/DataIntegration/CdcCollect/utils';
import {SchemaConnectionType} from '../../constants';

interface IntegrationTableTreeProps {
  onCheck: (checked: Key[], info: any) => void;
  checkedKeys: Key[];
}

const IntegrationTableTree: React.FC<IntegrationTableTreeProps> = ({checkedKeys, onCheck}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const {sourceConfig, baseConfig, isJobStarted} = useSelector(
    (state: IAppState) => state.cdcIntegrationSlice
  );
  const {sourceConnectionId, sourceDatabase} = sourceConfig;

  const [searchValue, setSearchValue] = useState<string>('');

  // 获取表列表
  const {
    run: tablesRun,
    loading: tablesLoading,
    data: tablesList
  } = useRequest(
    () =>
      getDatasourceTables({
        environment: {workspaceId, computeId: baseConfig.compute.computeId},
        datasourceInfo: {
          connectionId: sourceConnectionId,
          database: sourceDatabase,
          ...(SchemaConnectionType.includes(sourceConfig.sourceType)
            ? {schema: sourceConfig.sourceSchema}
            : {})
        }
      }),
    {
      manual: true
    }
  );

  useEffect(() => {
    if (sourceConnectionId && sourceDatabase) {
      tablesRun();
    }
  }, [sourceConnectionId, sourceDatabase, tablesRun]);

  // tree 搜索
  const onSearch = useCallback((value) => {
    setSearchValue(value);
  }, []);

  /**
   * 目前仅支持单库下多个表
   */
  const treeData = useMemo(() => {
    return [
      {
        title: sourceConnectionId,
        key: sourceConnectionId,
        children: [
          {
            title: sourceDatabase,
            key: getTreeKey([sourceConnectionId, sourceDatabase]),
            children: tablesList?.result?.tables
              .map((item) => ({
                title: item,
                key: getTreeKey([sourceConnectionId, sourceDatabase, item]),
                isLeaf: true
              }))
              .filter((child) => child.title.toLowerCase().includes(searchValue.toLowerCase()))
          }
        ]
      }
    ];
  }, [searchValue, sourceConnectionId, sourceDatabase, tablesList?.result?.tables]);

  // 默认展开的tree节点
  const defaultExpandedKeys = useMemo(() => {
    return [sourceConnectionId, getTreeKey([sourceConnectionId, sourceDatabase])];
  }, [sourceConnectionId, sourceDatabase]);

  return (
    <div className={styles['container']}>
      <div className={styles['title']}>可选项</div>
      <Search placeholder="请输入名称搜索" allowClear onSearch={onSearch} className={styles['search']} />
      <Loading loading={tablesLoading}>
        <Tree
          disabled={isJobStarted}
          treeData={treeData}
          checkable
          expandedKeys={defaultExpandedKeys}
          virtual
          height={300}
          onCheck={onCheck}
          checkedKeys={checkedKeys}
          className={styles['tree']}
        />
      </Loading>
    </div>
  );
};

export default IntegrationTableTree;
