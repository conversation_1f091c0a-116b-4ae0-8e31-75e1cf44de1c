/**
 * 实时集成-创建-第三步-批量设置
 */
import TextEllipsis from '@components/TextEllipsisTooltip';
import {CreateGroupTitle} from '@pages/DataIntegration/components/CreateGroupTitle';
import {IAppState} from '@store/index';
import {Input, Select} from 'acud';
import React, {useCallback, useMemo} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {FieldTypeOptions} from '../../constants';
import {produce} from 'immer';
import IntegrationEditTable from '../IntegrationEditTable';
import {initialFieldConfig} from '@store/cdcIntegrationSlice';
import {CdcCreateOperation} from '../CdcCreateOperation';
import FieldPrecisionConfig from '@pages/DataIntegration/components/FieldPrecisionConfig';
import {checkTargetTableName} from '@pages/DataIntegration/utils';
import styles from './index.module.less';

const CdcBatchConfig: React.FC = () => {
  const dispatch = useDispatch();
  const state = useSelector((state: IAppState) => state.cdcIntegrationSlice);
  // 这里后面还需要修改
  const isReadOnly = useSelector((state: IAppState) => state.cdcIntegrationSlice.isJobStarted);

  const sinkType = useSelector((state: IAppState) => state.cdcIntegrationSlice.sinkConfig.sinkType);
  const commonConfig = useSelector((state: IAppState) => state.cdcIntegrationSlice.commonConfig);

  /**
   * 批量字段变化
   */
  const handleChange = useCallback(
    (index: number, key: string, value) => {
      dispatch({
        type: 'cdcIntegration/updateCommonConfig',
        payload: {
          addedSinkFields: produce(commonConfig.addedSinkFields, (draft) => {
            draft[index][key] = value;
          })
        }
      });
    },
    [commonConfig, dispatch]
  );
  const columns = useMemo(
    () => [
      {
        title: '序号',
        width: 50,
        dataIndex: 'index',
        render: (text, record, index: number) => <div>{index + 1}</div>,
        fixed: 'left'
      },
      {
        title: '字段名称',
        dataIndex: 'name',
        width: 120,
        key: 'name',
        fixed: 'left',
        render: (text: string, record, index) => {
          if (isReadOnly) {
            return text;
          }
          const warningText = checkTargetTableName(commonConfig.addedSinkFields, index);
          return (
            <Input
              size="small"
              value={text}
              onChange={(e) => handleChange(index, 'name', e.target.value)}
              // 这里 acud 组件侧有 bug，输入后会失焦
              // warningText={warningText}
              // warningPopover={!!warningText}
              tips={warningText}
            />
          );
        }
      },
      {
        title: '字段类型',
        dataIndex: 'type',
        width: 170,
        key: 'type',
        render: (text: string, record, index) =>
          isReadOnly ? (
            <div>{text}</div>
          ) : (
            <Select
              value={text}
              size="small"
              onSelect={(e) => handleChange(index, 'type', e)}
              options={FieldTypeOptions[sinkType]}
              style={{width: 150}}
            />
          )
      },
      {
        title: '精度',
        dataIndex: 'precision',
        width: 150,
        key: 'precision',
        render: (text: string, record, index) => (
          <FieldPrecisionConfig
            isReadOnly={isReadOnly}
            type={record.type}
            precision={record.precision}
            scale={record.scale}
            onChange={(fieldName, value) => handleChange(index, fieldName, value)}
          />
        )
      },
      {
        title: '默认值',
        dataIndex: 'defaultValue',
        key: 'defaultValue',
        width: 100,
        render: (text: string, record, index) =>
          isReadOnly ? (
            <div>{text}</div>
          ) : (
            <Input
              size="small"
              value={text}
              onChange={(e) => handleChange(index, 'defaultValue', e.target.value)}
            />
          )
      },
      {
        title: '字段描述',
        dataIndex: 'comment',
        key: 'comment',
        width: 100,
        render: (text: string, record, index) =>
          isReadOnly ? (
            <TextEllipsis tooltip={text}>{text}</TextEllipsis>
          ) : (
            <Input
              size="small"
              value={text}
              onChange={(e) => handleChange(index, 'comment', e.target.value)}
            />
          )
      }
    ],
    [commonConfig.addedSinkFields, handleChange, isReadOnly, sinkType]
  );

  /**
   * 添加字段
   */
  const onAdd = useCallback(() => {
    dispatch({
      type: 'cdcIntegration/updateCommonConfig',
      payload: produce(commonConfig, (draft) => {
        draft.addedSinkFields.push(initialFieldConfig);
      })
    });
  }, [commonConfig, dispatch]);

  const onDelete = useCallback(
    (index) => {
      dispatch({
        type: 'cdcIntegration/updateCommonConfig',
        payload: produce(commonConfig, (draft) => {
          draft.addedSinkFields.splice(index, 1);
        })
      });
    },
    [commonConfig, dispatch]
  );

  const onSave = useCallback(async () => {
    return state;
  }, [state]);

  return (
    <div className={styles['cdc-batch-config']}>
      <CreateGroupTitle title="数据配置" />
      <IntegrationEditTable
        columns={columns}
        data={commonConfig.addedSinkFields}
        isReadOnly={isReadOnly}
        onAdd={onAdd}
        onDelete={onDelete}
      />
      <CdcCreateOperation onSave={onSave} />
    </div>
  );
};

export default CdcBatchConfig;
