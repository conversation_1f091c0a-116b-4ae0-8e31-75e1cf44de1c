/**
 * 实时集成-创建-第一步-来源与目标设置
 */
import {CreateGroupTitle} from '@pages/DataIntegration/components/CreateGroupTitle';
import React, {useCallback, useContext, useEffect, useMemo} from 'react';
import {Button, DatePicker, Form, Input, Link, Radio, Select, Space} from 'acud';
import {useDispatch, useSelector} from 'react-redux';
import {IAppState} from '@store/index';

import styles from './index.module.less';
import {
  IncrementPositionOptions,
  isAutoCreatedOptions,
  sinkNameRuleOptions,
  sinkTableTypeOptions,
  sinkTypeOptions,
  SourceTypeOptions
} from '../../constants';
import {useWatch} from 'acud/lib/form/Form';
import {
  CdcJobType,
  IncrementPositionType,
  SinkNameRule,
  SinkType,
  SourceTypeEnum
} from '@api/integration/cdc-type';
import locale from 'acud/es/date-picker/locale/zh_CN';
import {RULE} from '@utils/regs';
import ComputeSelect from '@pages/DataIntegration/components/ComputeSelect';
import {WorkspaceContext} from '@pages/index';
import {useRequest} from 'ahooks';
import {Privilege} from '@api/permission/type';
import {getDatasourceDatabases, getDatasourceTables} from '@api/integration/batch';
import {IConnection, queryConnectionList} from '@api/connection';
import IconSvg from '@components/IconSvg';
import urls from '@utils/urls';
import {initialTableConfig} from '@store/cdcIntegrationSlice';
import FilePathSelectFormItem, {MetaType, PathLevel} from '@components/FilePathSelectFormItem';
import {EntityType} from '@api/metaRequest';
import useUrlState from '@ahooksjs/use-url-state';
import {getMetaUrlTable} from '@pages/DataIntegration/utils';
import {disabledDate, disabledTime} from '@pages/DataIntegration/CdcCollect/utils';
import CardTwoContent from '@pages/DataIntegration/components/CardTwoContent';
import {getFormByConfig} from '../..';
import {CdcCreateOperation} from '../CdcCreateOperation';
import {SchemaConnectionType} from '../../constants';
import moment from 'moment';
import {omit} from 'lodash';

const CdcSourceSinkConfig: React.FC = () => {
  const dispatch = useDispatch();
  const [urlState] = useUrlState({jobId: '', sourceType: SourceTypeEnum.MySQL});
  const [baseForm] = Form.useForm();
  const [sourceForm] = Form.useForm();
  const [sinkForm] = Form.useForm();

  const {workspaceId} = useContext(WorkspaceContext);
  const state = useSelector((state: IAppState) => state.cdcIntegrationSlice);
  const {baseConfig, sourceConfig, sinkConfig, tableConfigs, isJobStarted} = state;

  const cdcType = useWatch('cdcType', baseForm);
  const compute = useWatch('compute', baseForm);

  const sourceConnectionId = useWatch('sourceConnectionId', sourceForm);
  const sourceType = useWatch('sourceType', sourceForm);

  const sinkType = useWatch('sinkType', sinkForm);
  const isAutoCreated = useWatch('isAutoCreated', sinkForm);
  const sinkNameRule = useWatch('sinkNameRule', sinkForm);

  // 初始化form表单
  useEffect(() => {
    baseForm.setFieldsValue({
      ...baseConfig,
      incrementPosition:
        baseConfig.cdcType === CdcJobType.Increment ? moment(sourceConfig.incrementPosition.position) : null
    });
    sourceForm.setFieldsValue(sourceConfig);
    sinkForm.setFieldsValue(sinkConfig);
  }, [baseConfig, baseForm, sinkConfig, sinkForm, sourceConfig, sourceForm]);

  /**
   * 初始化url里的源端类型
   */
  useEffect(() => {
    if (!sourceConnectionId) {
      sourceForm.setFieldValue('sourceType', urlState.sourceType);
    }
  }, [sourceConnectionId, sourceForm, urlState.sourceType]);

  // 获取数据源列表
  const {
    data: connectionList,
    loading: connectionLoading,
    run: connectionRun
  } = useRequest(
    () =>
      queryConnectionList(workspaceId, {pageNo: 1, pageSize: 100, type: sourceType}, Privilege.UseConnection),
    {
      manual: true
    }
  );

  const connectionOptions = useMemo(() => {
    if (connectionList) {
      return connectionList?.result?.connections.map((item: IConnection) => {
        return {
          label: item.name,
          value: item.name
        };
      });
    }
    return [];
  }, [connectionList]);
  // 获取数据库列表
  const {
    run: databasesRun,
    loading: databasesLoading,
    data: databasesList
  } = useRequest(
    () =>
      getDatasourceDatabases(
        {
          environment: {workspaceId: workspaceId, computeId: compute.computeId},
          datasourceInfo: {connectionId: sourceConnectionId}
        },
        Privilege.WriteTable
      ),
    {
      manual: true
    }
  );

  const databaseOptions = useMemo(() => {
    if (databasesList) {
      return databasesList?.result?.databases.map((item) => {
        return {
          label: item,
          value: item
        };
      });
    }
    return [];
  }, [databasesList]);

  const baseConfigForm = useMemo(() => {
    return [
      {
        name: 'name',
        label: '任务名称',
        rules: [
          {required: true, message: '请输入任务名称'},
          {
            pattern: RULE.workflowName,
            message: RULE.workflowNameText
          }
        ],
        isShow: true,
        component: <Input placeholder="请输入任务名称" limitLength={256} forbidIfLimit style={{width: 504}} />
      },
      {
        name: 'description',
        label: '任务描述',
        rules: [{max: 500, message: '不超过500字符'}],
        isShow: true,
        component: (
          <Input.TextArea
            placeholder="请输入任务描述"
            limitLength={500}
            autoSize={{minRows: 3, maxRows: 6}}
            width={504}
          />
        )
      },
      {
        name: 'cdcType',
        label: '同步步骤',
        rules: [{required: true, message: '请选择同步步骤'}],
        isShow: true,
        component: <Radio.Group options={IncrementPositionOptions} disabled={isJobStarted} />,
        extra: IncrementPositionOptions.find((item) => item.value === cdcType)?.text
      },
      {
        name: 'incrementPosition',
        label: '增量时间点',
        rules: [{required: true, message: '请选择增量时间点'}],
        component: (
          <DatePicker
            locale={locale}
            showTime
            disabled={isJobStarted}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
          />
        ),
        isShow: cdcType === CdcJobType.Increment
      }
    ];
  }, [cdcType, isJobStarted]);

  const computeConfigForm = useMemo(() => {
    return [
      {
        name: 'compute',
        label: '计算实例',
        rules: [{required: true, message: '请选择计算实例'}],
        isShow: true,
        component: <ComputeSelect />
      }
    ];
  }, []);

  // 清空写入设置，当数据源和数据库发生变化时
  const resetWriteSetting = useCallback(() => {
    dispatch({
      type: 'cdcIntegration/updateTableConfigs',
      payload: []
    });
  }, [dispatch]);

  const sourceConfigForm = useMemo(() => {
    return [
      {
        name: 'sourceType',
        label: '源端类型',
        rules: [{required: true, message: '请选择源端类型'}],
        isShow: true,
        component: (
          <Select
            options={SourceTypeOptions}
            keepExpand
            groupSelectorRender={(groupLabel, value) => <>{value}</>}
            placeholder="请选择数据源"
            style={{width: 400}}
            disabled={isJobStarted}
            onChange={() => {
              sourceForm.setFieldValue('sourceConnectionId', '');
              sourceForm.setFieldValue('sourceDatabase', '');
              sourceForm.setFieldValue('sourceTable', '');
            }}
          />
        )
      },
      {
        name: '',
        label: '数据源名称',
        isShow: sourceType,
        component: (
          <>
            <Form.Item noStyle name="sourceConnectionId" rules={[{required: true, message: '请选择数据源'}]}>
              <Select
                loading={connectionLoading}
                options={connectionOptions}
                disabled={isJobStarted}
                placeholder="请选择数据源"
                style={{width: 400}}
                onDropdownVisibleChange={(open) => {
                  if (open) {
                    connectionRun();
                  }
                }}
                onChange={(value: SourceTypeEnum) => {
                  resetWriteSetting();
                  const hasSchema = SchemaConnectionType.includes(value);
                  const curConnectionList = connectionList?.result?.connections.find(
                    (item: IConnection) => item.name === value
                  );
                  sourceForm.setFieldValue('sourceDatabase', hasSchema ? curConnectionList.database : '');
                }}
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    <div
                      style={{
                        borderTop: '1px solid #e8e9eb',
                        padding: '4px 12px'
                      }}
                    >
                      <Link
                        onClick={() =>
                          window.open(
                            `${window.location.pathname}#${urls.connection}?workspaceId=${workspaceId}&mode=create`,
                            '_blank'
                          )
                        }
                      >
                        <Space>
                          <IconSvg type="add" size={16} />
                          创建数据源
                        </Space>
                      </Link>
                    </div>
                  </>
                )}
              />
            </Form.Item>
            <Button className="ml-[8px]" onClick={connectionRun}>
              <IconSvg type="refresh" />
            </Button>
          </>
        )
      },
      {
        name: SchemaConnectionType.includes(sourceType) ? 'sourceSchema' : 'sourceDatabase',
        isShow: sourceConnectionId,
        label: '数据库名称',
        rules: [{required: true, message: '请选择数据库名称'}],
        component: (
          <Select
            loading={databasesLoading}
            placeholder="请选择数据库"
            options={databaseOptions}
            disabled={!compute || isJobStarted}
            style={{width: 400}}
            onDropdownVisibleChange={(open) => {
              if (open) {
                databasesRun();
              }
            }}
            onChange={() => {
              resetWriteSetting();
            }}
          />
        )
      }
    ];
  }, [
    compute,
    connectionList?.result?.connections,
    connectionLoading,
    connectionOptions,
    connectionRun,
    databaseOptions,
    databasesLoading,
    databasesRun,
    isJobStarted,
    resetWriteSetting,
    sourceConnectionId,
    sourceForm,
    sourceType,
    workspaceId
  ]);

  const sinkConfigForm = useMemo(() => {
    return [
      {
        name: 'sinkType',
        label: '目的地类型',
        isShow: true,
        rules: [{required: true, message: '请选择目的地类型'}],
        component: (
          <Select
            placeholder="请选择目的地类型"
            options={sinkTypeOptions}
            style={{width: 400}}
            onChange={resetWriteSetting}
            disabled={isJobStarted}
          />
        )
      },
      {
        name: 'isAutoCreated',
        label: '建表方式',
        isShow: true,
        rules: [{required: true, message: '请选择建表方式'}],
        component: (
          <Select
            placeholder="请选择建表方式"
            options={isAutoCreatedOptions}
            style={{width: 400}}
            disabled={isJobStarted}
          />
        )
      },
      {
        name: 'sinkTableType',
        label: '表类型',
        rules: [{required: true, message: '请选择表类型'}],
        isShow: isAutoCreated && sinkType === SinkType.Iceberg,
        component: (
          <Select
            placeholder="请选择表类型"
            options={sinkTableTypeOptions}
            style={{width: 400}}
            disabled={isJobStarted}
          />
        )
      },
      {
        name: 'sinkNameRule',
        label: '表名设置',
        rules: [{required: true, message: '请选择表名设置'}],
        isShow: isAutoCreated,
        component: (
          <Select
            placeholder="请选择表名设置"
            options={sinkNameRuleOptions}
            style={{width: 400}}
            disabled={isJobStarted}
          />
        )
      },
      {
        name: 'prefix',
        label: '前缀',
        rules: [
          {required: true, message: '请输入表名前缀'},
          {
            pattern: RULE.tableSuffixNameRule,
            message: RULE.tableSuffixNameRuleText
          }
        ],
        extra: RULE.tableSuffixNameRuleText,
        isShow: [SinkNameRule.AddPrefix, SinkNameRule.AddPrefixAndSuffix].includes(sinkNameRule),
        component: (
          <Input
            placeholder="请输入表名前缀"
            limitLength={128}
            forbidIfLimit
            style={{width: 400}}
            disabled={isJobStarted}
          />
        )
      },
      {
        name: 'suffix',
        label: '后缀',
        rules: [
          {required: true, message: '请输入表名后缀'},
          {
            pattern: RULE.tableSuffixNameRule,
            message: RULE.tableSuffixNameRuleText
          }
        ],
        isShow: [SinkNameRule.AddSuffix, SinkNameRule.AddPrefixAndSuffix].includes(sinkNameRule),
        extra: RULE.tableSuffixNameRuleText,
        component: (
          <Input
            placeholder="请输入表名后缀"
            limitLength={128}
            forbidIfLimit
            style={{width: 400}}
            disabled={isJobStarted}
          />
        )
      },
      {
        name: 'sinkPath',
        label: '',
        isShow: true,
        noStyle: true,
        component: (() => {
          // 编辑任务时， 表已经创建 并且是 自动建表
          const showTableStr = urlState.jobId && tableConfigs?.length && isAutoCreated;
          return (
            <>
              {/* <Form.Item label={label} hidden={!showTableStr}>
                <Space>
                  <Button
                    type="actiontext"
                    onClick={() => {
                      window.open(
                        `${window.location.pathname}#${getMetaUrlTable(sinkConfig.sinkFullName, workspaceId)}`,
                        '_blank'
                      );
                    }}
                  >
                    {sinkConfig.sinkFullName}
                  </Button>
                </Space>
              </Form.Item> */}

              {/* <Form.Item hidden={showTableStr} noStyle> */}
              <Form.Item label="目标数据库" required>
                <FilePathSelectFormItem
                  form={sinkForm}
                  name="sinkPath"
                  label=""
                  placeholderReplace="<catalog>.<schema>"
                  searchType={[EntityType.CATALOG, EntityType.SCHEMA]}
                  selectableLevel={PathLevel.Schema}
                  latestLevel={PathLevel.Catalog}
                  metaDirs={['table']}
                  metaType={MetaType.Table}
                  schemaPrivilege={[Privilege.CreateTable]}
                  disabled={isJobStarted}
                  onChange={resetWriteSetting}
                />
              </Form.Item>
            </>
          );
        })()
      }
    ];
  }, [
    isAutoCreated,
    resetWriteSetting,
    sinkForm,
    sinkNameRule,
    sinkType,
    tableConfigs?.length,
    urlState.jobId,
    isJobStarted
  ]);

  const onSave = useCallback(async () => {
    // 校验
    await Promise.all([baseForm.validateFields(), sourceForm.validateFields(), sinkForm.validateFields()]);

    // 保存到store中
    const sourceConfig = sourceForm.getFieldsValue();
    const sinkConfig = sinkForm.getFieldsValue();
    const baseConfig = baseForm.getFieldsValue();
    const newConfig = {
      ...state,
      baseConfig: {...state.baseConfig, ...omit(baseConfig, 'incrementPosition')},
      sourceConfig: {
        ...state.sourceConfig,
        ...sourceConfig,
        ...(baseConfig.cdcType === CdcJobType.Increment
          ? {
              incrementPosition: {
                type: IncrementPositionType.Timestamp,
                position: moment(baseConfig.incrementPosition).format('YYYY-MM-DDTHH:mm:ssZ')
              }
            }
          : {})
      },
      sinkConfig: {...state.sinkConfig, ...sinkConfig}
    };
    dispatch({
      type: 'cdcIntegration/updateAllConfig',
      payload: newConfig
    });
    return newConfig;
  }, [baseForm, dispatch, sinkForm, sourceForm, state]);

  return (
    <div>
      <Form
        form={baseForm}
        labelWidth="80px"
        labelAlign="left"
        inputMaxWidth={900}
        colon={false}
        layout="vertical"
      >
        <CreateGroupTitle title="基本配置" />
        {/** 基本配置 */}
        {getFormByConfig(baseConfigForm)}
        <CreateGroupTitle title="资源设置" />
        {/** 计算实例配置 */}
        {getFormByConfig(computeConfigForm)}
      </Form>
      <CreateGroupTitle title="数据配置" />
      <CardTwoContent
        left={
          <Form
            form={sourceForm}
            labelWidth="80px"
            labelAlign="left"
            inputMaxWidth={900}
            colon={false}
            layout="vertical"
          >
            {getFormByConfig(sourceConfigForm)}
          </Form>
        }
        right={
          <Form
            form={sinkForm}
            labelWidth="80px"
            labelAlign="left"
            inputMaxWidth={900}
            colon={false}
            layout="vertical"
          >
            {getFormByConfig(sinkConfigForm)}
          </Form>
        }
        title={['源端配置', '目标端配置']}
        showDivider
      />
      <CdcCreateOperation onSave={onSave} />
    </div>
  );
};

export default CdcSourceSinkConfig;
