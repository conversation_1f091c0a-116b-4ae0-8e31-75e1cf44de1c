/**
 * 实时集成-创建-第二步-运行信息设置
 */
import {CreateGroupTitle} from '@pages/DataIntegration/components/CreateGroupTitle';
import {Form, InputNumber, Radio, Select} from 'acud';
import React, {useCallback, useEffect, useMemo} from 'react';
import {getFormByConfig} from '../..';
import CardTwoContent from '@pages/DataIntegration/components/CardTwoContent';
import {
  DirtyDataStrategyOptions,
  DmlDeleteOptions,
  DmlInsertOptions,
  DmlUpdateOptions,
  EnableDirtyDataWriteOptions,
  SourceChangeStrategyConfig,
  SourceChangeStrategyOptions
} from '../../constants';
import {DmlDeleteStrategy, SourceChangeStrategy} from '@api/integration/cdc-type';
import FilePathSelect, {PathLevel} from '@components/FilePathSelect';
import {useWatch} from 'acud/lib/form/Form';
import {CdcCreateOperation} from '../CdcCreateOperation';
import {useDispatch, useSelector} from 'react-redux';
import {IAppState} from '@store/index';

const getSourceChangeStrategyOptions = (strategy: SourceChangeStrategy[], syncText) => {
  return SourceChangeStrategyOptions.filter((item) => strategy.includes(item.value)).map((item) => ({
    value: item.value,
    label: item.value === SourceChangeStrategy.Sync ? syncText : item.label
  }));
};

const sourceConfigForm = [
  {
    name: 'parallelism',
    label: '并发数',
    rules: [{required: true, message: '请填写并发数'}],
    component: <InputNumber min={1} disabled={true} />,
    isShow: true
  },
  ...SourceChangeStrategyConfig.map((item) => ({
    name: ['sourceChange', item.name],
    label: item.label,
    rules: [{required: true, message: `请选择${item.label}`}],
    isShow: true,
    component: (
      <Radio.Group options={getSourceChangeStrategyOptions(item.strategy, item?.syncText)} disabled={true} />
    )
  }))
];

const CdcRunConfig: React.FC = () => {
  const dispatch = useDispatch();
  const [sourceForm] = Form.useForm();
  const [sinkForm] = Form.useForm();

  const state = useSelector((state: IAppState) => state.cdcIntegrationSlice);

  const strategy = useWatch(['dirtyDataStrategy', 'strategy'], sinkForm);
  const enableDirtyDataWrite = useWatch(['dirtyDataStrategy', 'enableDirtyDataWrite'], sinkForm);

  const onDmlDeleteChange = useCallback(
    (value) => {
      sinkForm.setFieldValue(
        ['dmlConfig', 'logicalDeleteField'],
        value === DmlDeleteStrategy.LogicalDelete ? 'logical_delete_tag' : ''
      );
    },
    [sinkForm]
  );

  useEffect(() => {
    sinkForm.setFieldsValue(state.sinkConfig);
    sourceForm.setFieldsValue(state.sourceConfig);
  }, [sourceForm, sinkForm, state.sinkConfig, state.sourceConfig, onDmlDeleteChange]);

  const sinkConfigForm = useMemo(
    () => [
      {
        name: ['dmlConfig', 'insert'],
        label: '源端表插入数据',
        rules: [{required: true, message: '请选择源端表插入数据'}],
        isShow: true,
        component: <Select options={DmlInsertOptions} placeholder="请选择源端表插入数据" />
      },
      {
        name: ['dmlConfig', 'update'],
        label: '源端表更新数据',
        rules: [{required: true, message: '请选择源端表更新数据'}],
        isShow: true,
        component: <Select options={DmlUpdateOptions} placeholder="请选择源端表更新数据" />
      },
      {
        name: ['dmlConfig', 'delete'],
        label: '源端表删除数据',
        rules: [{required: true, message: '请选择源端表删除数据'}],
        isShow: true,
        component: (
          <Select
            options={DmlDeleteOptions}
            placeholder="请选择源端表删除数据"
            onChange={onDmlDeleteChange}
          />
        )
      },
      {
        name: ['dirtyDataStrategy', 'strategy'],
        label: '脏数据处理策略',
        rules: [{required: true, message: '请选择脏数据处理策略'}],
        isShow: true,
        component: <Select options={DirtyDataStrategyOptions} placeholder="请选择脏数据处理策略" disabled />
      },
      {
        name: ['dirtyDataStrategy', 'enableDirtyDataWrite'],
        label: '是否写入脏数据',
        isShow: strategy,
        rules: [{required: true, message: '请选择是否写入脏数据'}],
        component: <Radio.Group options={EnableDirtyDataWriteOptions} disabled />
      },
      {
        name: ['dirtyDataStrategy', 'dirtyDataVolume'],
        label: '脏数据存储路径',
        isShow: enableDirtyDataWrite,
        rules: [{required: true, message: '请选择脏数据存储路径'}],
        component: (
          <FilePathSelect
            form={sinkForm}
            metaDirs={['volume']}
            showPathSelect
            selectableLevel={PathLevel.Volume}
          />
        )
      }
    ],
    [enableDirtyDataWrite, onDmlDeleteChange, sinkForm, strategy]
  );

  const onSave = useCallback(async () => {
    //  await Promise.all([sourceForm.validateFields(), sinkForm.validateFields()]);
    const sourceConfig = sourceForm.getFieldsValue();
    const sinkConfig = sinkForm.getFieldsValue();
    const newConfig = {
      ...state,
      sourceConfig: {
        ...state.sourceConfig,
        ...sourceConfig
      },
      sinkConfig: {
        ...state.sinkConfig,
        ...sinkConfig,
        dmlConfig: {
          ...sinkConfig.dmlConfig,
          logicalDeleteField:
            sinkConfig.dmlConfig.delete === DmlDeleteStrategy.LogicalDelete ? 'logical_delete_tag' : ''
        }
      }
    };
    // 保存到store中
    dispatch({
      type: 'cdcIntegration/updateAllConfig',
      payload: newConfig
    });
    return newConfig;
  }, [dispatch, sinkForm, sourceForm, state]);

  return (
    <div>
      <CreateGroupTitle title="运行配置" />
      <CardTwoContent
        left={
          <Form form={sourceForm} labelWidth="130px" labelAlign="left" inputMaxWidth={900} colon={false}>
            {getFormByConfig(sourceConfigForm)}
          </Form>
        }
        right={
          <Form form={sinkForm} labelWidth="100px" labelAlign="left" inputMaxWidth={900} colon={false}>
            {getFormByConfig(sinkConfigForm)}
          </Form>
        }
        title={['源端配置', '目标端配置']}
        showDivider
      />
      <CdcCreateOperation onSave={onSave} />
    </div>
  );
};
export default CdcRunConfig;
