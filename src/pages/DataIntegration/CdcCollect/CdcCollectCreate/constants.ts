import {
  CdcJobType,
  DirtyDataStrategyEnum,
  DmlDeleteStrategy,
  DmlInsertStrategy,
  DmlUpdateStrategy,
  PartitionFunction,
  SinkNameRule,
  SinkTableType,
  SinkType,
  SourceChangeStrategy,
  SourceTypeEnum
} from '@api/integration/cdc-type';
import {FieldType} from '@api/integration/type';

export enum CreateStep {
  SourceSinkConfig = 0,
  RunConfig = 1,
  BatchConfig = 2,
  WritingConfig = 3
}

export const CreateStepConfig = {
  [CreateStep.SourceSinkConfig]: '来源与目标设置',
  [CreateStep.RunConfig]: '运行信息配置',
  [CreateStep.BatchConfig]: '批量增加字段',
  [CreateStep.WritingConfig]: '写入设置'
};

export const IncrementPositionOptions = [
  {
    value: CdcJobType.BaseIncrement,
    label: '全量+增量同步',
    text: '表示先全量同步表中数据，然后继续增量进行同步'
  },
  {
    value: CdcJobType.Increment,
    label: '增量同步',
    text: '选中增量同步时需要填写增量同步的时间点'
  }
];

export const SourceTypeOptions = [
  {
    value: SourceTypeEnum.MySQL,
    label: 'MySQL',
    icon: 'mysql'
  },
  {
    value: SourceTypeEnum.Oracle,
    label: 'Oracle',
    icon: 'oracle'
  }
  // {
  //   value: SourceTypeEnum.SQLServer,
  //   label: 'SQLServer',
  //   icon: 'sqlserver'
  // },
  // {
  //   value: SourceTypeEnum.PostgreSQL,
  //   label: 'PostgreSQL',
  //   icon: 'postgresql'
  // }
];

export const sinkTypeOptions = [
  {label: 'Iceberg', value: SinkType.Iceberg},
  {label: 'Doris', value: SinkType.Doris}
];

export const sinkNameRuleOptions = [
  {label: '无前后缀', value: SinkNameRule.Same},
  {label: '增加前缀', value: SinkNameRule.AddPrefix},
  {label: '增加后缀', value: SinkNameRule.AddSuffix},
  {label: '增加前后缀', value: SinkNameRule.AddPrefixAndSuffix}
];

export const isAutoCreatedOptions = [
  {label: '自动建表', value: 1},
  {label: '选择已有表', value: 0}
];

export const sinkTableTypeOptions = [
  {label: '内部表', value: SinkTableType.Managed}
  // {label: '外部表', value: SinkTableType.External}
];

export const SourceChangeStrategyOptions = [
  {label: '终止任务', value: SourceChangeStrategy.Pause},
  {label: '忽略', value: SourceChangeStrategy.Skip},
  {label: '同步', value: SourceChangeStrategy.Sync}
];

export const SourceChangeStrategyConfig = [
  {
    name: 'onDeleteColumn',
    label: '源端表删除字段',
    // 选项为终止任务（暂停任务），忽略，默认终止任务
    strategy: [SourceChangeStrategy.Pause, SourceChangeStrategy.Skip]
    // 默认值：Pause（终止任务）
  },
  {
    name: 'onDeleteSource',
    label: '源端表被删除',
    // 只有终止任务，默认终止任务
    strategy: [SourceChangeStrategy.Pause]
    // 默认值：Pause（终止任务）
  },
  {
    name: 'onAddColumn',
    label: '源端表新增字段',
    // 选项：终止任务、忽略、目的端自动新增字段并同步数据（Sync）
    strategy: [SourceChangeStrategy.Pause, SourceChangeStrategy.Skip, SourceChangeStrategy.Sync],
    syncText: '目的端自动新增字段并同步数据'
    // 默认值：Skip（忽略）
  },
  {
    name: 'onRenameColumn',
    label: '源端表重命名字段',
    // 选项：终止任务、忽略、默认忽略
    strategy: [SourceChangeStrategy.Pause, SourceChangeStrategy.Skip]
    // 默认值：Skip（忽略）
  },
  {
    name: 'onTruncateTable',
    label: '源端表清空数据',
    // 选项：终止任务、忽略，默认忽略
    strategy: [SourceChangeStrategy.Pause, SourceChangeStrategy.Skip]
    // 默认值：Skip（忽略）
  },
  {
    name: 'onRenameTable',
    label: '源端表名称重命名',
    // 选项：终止任务、忽略，默认忽略
    strategy: [SourceChangeStrategy.Pause, SourceChangeStrategy.Skip]
    // 默认值：Skip（忽略）
  },
  {
    name: 'onChangeColumn',
    label: '源端表修改字段类型',
    // 选项：终止任务、忽略、同步修改字段类型，默认忽略
    strategy: [SourceChangeStrategy.Pause, SourceChangeStrategy.Skip, SourceChangeStrategy.Sync],
    syncText: '同步修改字段类型'
    // 默认值：Skip（忽略）
  },
  {
    name: 'onChangeTableComment',
    label: '源端表修改表描述信息',
    // 选项：终止任务、忽略、同步修改表描述，默认忽略
    strategy: [SourceChangeStrategy.Pause, SourceChangeStrategy.Skip, SourceChangeStrategy.Sync],
    syncText: '同步修改表描述'
    // 默认值：Skip（忽略）
  },
  {
    name: 'onChangeColumnComment',
    label: '源端表修改字段描述信息',
    // 选项：终止任务、忽略、同步修改字段描述，默认忽略
    strategy: [SourceChangeStrategy.Pause, SourceChangeStrategy.Skip, SourceChangeStrategy.Sync],
    syncText: '同步修改字段描述'
    // 默认值：Skip（忽略）
  }
];

export const DmlInsertOptions = [
  {label: '正常处理', value: DmlInsertStrategy.Insert},
  {label: '忽略', value: DmlInsertStrategy.Ignore}
];

export const DmlUpdateOptions = [
  {label: '正常处理', value: DmlUpdateStrategy.Update},
  {label: '忽略', value: DmlUpdateStrategy.Ignore}
];

export const DmlDeleteOptions = [
  {label: '正常处理', value: DmlDeleteStrategy.Delete},
  {label: '忽略', value: DmlDeleteStrategy.Ignore},
  {label: '逻辑删除', value: DmlDeleteStrategy.LogicalDelete}
];

export const DirtyDataStrategyOptions = [
  {label: '不容忍', value: DirtyDataStrategyEnum.Strict},
  {label: '容忍部分', value: DirtyDataStrategyEnum.Tolerant},
  {label: '忽略', value: DirtyDataStrategyEnum.Ignore}
];

export const EnableDirtyDataWriteOptions = [
  {label: '是', value: true},
  {label: '否', value: false}
];

/** 字段类型配置 */
export const FieldTypeOptionsConfig = {
  [FieldType.String]: {label: 'STRING', value: FieldType.String},
  [FieldType.Fixed]: {label: 'FIXED', value: FieldType.Fixed},
  [FieldType.Char]: {label: 'CHAR', value: FieldType.Char},
  [FieldType.Varchar]: {label: 'VARCHAR', value: FieldType.Varchar},
  [FieldType.Tinyint]: {label: 'TINYINT', value: FieldType.Tinyint},
  [FieldType.Smallint]: {label: 'SMALLINT', value: FieldType.Smallint},
  [FieldType.Int]: {label: 'INT', value: FieldType.Int},
  [FieldType.Long]: {label: 'LONG', value: FieldType.Long},
  [FieldType.Bigint]: {label: 'BIGINT', value: FieldType.Bigint},
  [FieldType.Largeint]: {label: 'LARGEINT', value: FieldType.Largeint},
  [FieldType.Float]: {label: 'FLOAT', value: FieldType.Float},
  [FieldType.Double]: {label: 'DOUBLE', value: FieldType.Double},
  [FieldType.Decimal]: {label: 'DECIMAL', value: FieldType.Decimal},
  [FieldType.Boolean]: {label: 'BOOLEAN', value: FieldType.Boolean},
  [FieldType.Binary]: {label: 'BINARY', value: FieldType.Binary},
  [FieldType.Bytes]: {label: 'BYTES', value: FieldType.Bytes},
  [FieldType.Date]: {label: 'DATE', value: FieldType.Date},
  [FieldType.Time]: {label: 'TIME', value: FieldType.Time},
  [FieldType.Datetime]: {label: 'DATETIME', value: FieldType.Datetime},
  [FieldType.Timestamp]: {label: 'TIMESTAMP', value: FieldType.Timestamp},
};

// Doris 支持的字段类型
export const DorisFieldTypes: FieldType[] = [
  FieldType.Char,
  FieldType.Varchar,
  FieldType.String,
  FieldType.Tinyint,
  FieldType.Smallint,
  FieldType.Int,
  FieldType.Bigint,
  FieldType.Largeint,
  FieldType.Float,
  FieldType.Double,
  FieldType.Decimal,
  FieldType.Boolean,
  FieldType.Date,
  FieldType.Datetime
];

// Iceberg 支持的字段类型
export const IcebergFieldTypes: FieldType[] = [
  FieldType.String,
  FieldType.Int,
  FieldType.Long,
  FieldType.Float,
  FieldType.Double,
  FieldType.Decimal,
  FieldType.Boolean,
  FieldType.Binary,
  FieldType.Date,
  FieldType.Time,
  FieldType.Timestamp
];

export const DbTypeMap = {
  [SinkType.Iceberg]: {
    BOOLEAN: FieldType.Boolean,
    BYTE: FieldType.Int,
    SHORT: FieldType.Int,
    INT: FieldType.Int,
    LONG: FieldType.Long,
    FLOAT: FieldType.Float,
    DOUBLE: FieldType.Double,
    DATE: FieldType.Date,
    TIME: FieldType.Time,
    TIMESTAMP: FieldType.Timestamp,
    TIMESTAMP_NTZ: FieldType.Timestamp,
    CHAR: FieldType.String,
    VARCHAR: FieldType.String,
    STRING: FieldType.String,
    BINARY: FieldType.Binary,
    DECIMAL: FieldType.Decimal
  },
  [SinkType.Doris]: {
    BOOLEAN: FieldType.Boolean,
    BYTE: FieldType.Smallint,
    SHORT: FieldType.Smallint,
    INT: FieldType.Int,
    LONG: FieldType.Bigint,
    FLOAT: FieldType.Float,
    DOUBLE: FieldType.Double,
    DECIMAL: FieldType.Decimal,
    TIME: FieldType.String,
    DATE: FieldType.Date,
    TIMESTAMP: FieldType.Datetime,
    TIMESTAMP_NTZ: FieldType.Datetime,
    CHAR: FieldType.String,
    VARCHAR: FieldType.String,
    STRING: FieldType.String,
    BINARY: FieldType.String
  }
};

export const FieldTypeOptions = {
  [SinkType.Doris]: DorisFieldTypes.map((item) => FieldTypeOptionsConfig[item]),
  [SinkType.Iceberg]: IcebergFieldTypes.map((item) => FieldTypeOptionsConfig[item])
};

// 映射规则
export enum MappingType {
  SameName = 'name',
  SameRow = 'row',
  CancelMap = 'cancel'
}

export const MappingTypeOptions = [
  {label: '同名映射', value: MappingType.SameName},
  {label: '同行映射', value: MappingType.SameRow},
  {label: '取消映射', value: MappingType.CancelMap}
];

// 使用sourceSchema作为数据库名称的类型
export const SchemaConnectionType = [SourceTypeEnum.PostgreSQL, SourceTypeEnum.SQLServer];

export const PartitionFunctionOptions = [
  // {label: 'year', value: PartitionFunction.Year},
  // {label: 'month', value: PartitionFunction.Month},
  // {label: 'day', value: PartitionFunction.Day},
  // {label: 'hour', value: PartitionFunction.Hour},
  // {label: 'bucket', value: PartitionFunction.Bucket},
  // {label: 'truncate', value: PartitionFunction.Truncate},
  {label: 'identity', value: PartitionFunction.Identity}
];
