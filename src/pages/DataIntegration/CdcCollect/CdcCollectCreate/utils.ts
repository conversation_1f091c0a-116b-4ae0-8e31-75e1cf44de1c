import {CreateStep} from './constants';

// 根据建表方式获取创建步骤
export const getCreateStepConfig = (isAutoCreated) => {
  if (isAutoCreated) {
    return {
      [CreateStep.SourceSinkConfig]: {
        nextStep: CreateStep.RunConfig,
        beforeStep: null
      },
      [CreateStep.RunConfig]: {
        nextStep: CreateStep.BatchConfig,
        beforeStep: CreateStep.SourceSinkConfig
      },
      [CreateStep.BatchConfig]: {
        nextStep: CreateStep.WritingConfig,
        beforeStep: CreateStep.RunConfig
      },
      [CreateStep.WritingConfig]: {
        beforeStep: CreateStep.BatchConfig,
        nextStep: null
      }
    };
  } else {
    return {
      [CreateStep.SourceSinkConfig]: {
        nextStep: CreateStep.RunConfig,
        beforeStep: null
      },
      [CreateStep.RunConfig]: {
        nextStep: CreateStep.WritingConfig,
        beforeStep: CreateStep.SourceSinkConfig
      },
      [CreateStep.WritingConfig]: {
        beforeStep: CreateStep.RunConfig,
        nextStep: null
      }
    };
  }
};
