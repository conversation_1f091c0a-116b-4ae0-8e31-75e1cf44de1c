.cdc-detail-log {
  margin-top: 10px;
  border-radius: 8px;
  border: 1px solid rgba(232, 233, 235, 1);
  flex: 1;
  display: flex;
  flex-direction: column;
  &_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 16px;
    &_title {
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      letter-spacing: 0px;
    }
    button {
      width: 20px;
      height: 20px;
    }
  }

  &_container {
    display: flex;
    flex-direction: column;
    flex: 1;
    .content {
      flex: 1;
      overflow: hidden;
    }
  }

  .log-title {
    display: flex;
    justify-content: space-between;
  }

  &_hasPage-patination {
    border-top: 1px solid rgba(232, 233, 235, 1);
    padding: 8px 0px;
  }
}
