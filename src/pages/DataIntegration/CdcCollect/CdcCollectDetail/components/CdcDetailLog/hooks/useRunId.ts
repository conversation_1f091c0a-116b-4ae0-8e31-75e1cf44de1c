import {useRequest} from 'ahooks';
import {useContext} from 'react';
import {WorkspaceContext} from '@pages/index';
import {getCdcListExecutions} from '@api/integration';
import {CdcJobStage} from '@api/integration/cdc-type';
import useUrlState from '@ahooksjs/use-url-state';

/**
 * 获取当前任务的 runId
 * @returns runId 和相关状态
 */
export const useRunId = () => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState({jobId: ''});

  const {
    data: executionData,
    loading: runIdLoading,
    run: fetchRunId,
    error: runIdError
  } = useRequest(
    () =>
      getCdcListExecutions(workspaceId, urlState.jobId, {
        pageNo: 1,
        pageSize: 1,
        stage: CdcJobStage.Increment, // 默认获取增量阶段
        status: '' // 不过滤状态
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (!res.success) {
          console.warn('Failed to fetch runId:', res.status);
        }
      },
      onError: (error) => {
        console.error('Error fetching runId:', error);
      }
    }
  );

  const runId = executionData?.result?.runId || '';

  return {
    runId,
    runIdLoading,
    fetchRunId,
    runIdError,
    hasRunId: !!runId
  };
};
