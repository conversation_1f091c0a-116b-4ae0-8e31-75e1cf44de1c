/**
 * 实时集成-详情-运行日志
 */
import React, {useCallback, useContext, useEffect, useState} from 'react';
import {useMemoizedFn, useRequest} from 'ahooks';
import {Empty, Pagination, Space, Button, Divider} from 'acud';
import {WorkspaceContext} from '@pages/index';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import MonacoLog from '@components/MonacoLog';
import {getIntegrationExecutionLog, downloadIntegrationExecutionLog} from '@api/integration';
import useUrlState from '@ahooksjs/use-url-state';
import {OutlinedBceSearchLg, OutlinedBceDownload, OutlinedRefresh} from 'acud-icon';
import {useRunId} from './hooks/useRunId';

const cx = classNames.bind(styles);
const kclass = 'cdc-detail-log';

export interface InstanceLogProps {}

const CdcDetailLog: React.FC<InstanceLogProps> = () => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState({jobId: ''});
  // 自动刷新
  const [taskFinished, setTaskFinished] = useState(false);
  // 当前页码
  const [pageNo, setPageNo] = useState<number>(1);
  // 是否已触发兜底 Flag
  const [isEnsureTriggered, setIsEnsureTriggered] = useState(false);
  // 运行日志 runId
  const {runId, runIdLoading, fetchRunId, runIdError, hasRunId} = useRunId();

  const {
    data: logData,
    run: runQueryLog,
    loading: logLoading
  } = useRequest(getIntegrationExecutionLog, {
    manual: true,
    pollingInterval: undefined,
    pollingWhenHidden: false, // 页面不可见时暂停轮询
    onSuccess: (res) => {
      // 如果请求失败，取消刷新
      if (!res.success) {
        setTaskFinished(true);
        return;
      }
      // 如果当前页码为 0，则设置为总页码 最新的数据
      if (pageNo === 0) {
        setPageNo(res.result.totalLogPageCount);
      }
      const result = res.result;
      // 如果任务已完成，前端停止刷新
      // 但由于任务执行过快的时候，概率出现 taskFinished 为 true 但日志为空，所以前端 10s 后做一次兜底刷新
      if (result.taskFinished && !result.logContent && !isEnsureTriggered) {
        setIsEnsureTriggered(true);
        setTimeout(() => {
          queryLog();
        }, 10000);
      }
      setTaskFinished(!!result.taskFinished);
    }
  });

  // 查询日志
  const queryLog = useCallback(() => {
    if (!hasRunId) {
      console.warn('runId is required for querying log');
      return;
    }
    if (!pageNo) {
      setPageNo(0);
    }
    runQueryLog(workspaceId, urlState.jobId, runId, {
      pageSize: 100,
      pageNo
    });
  }, [runId, hasRunId, runQueryLog, urlState.jobId, workspaceId, pageNo]);

  /**
   * 刷新操作 - 重新获取 runId 并查询日志
   */
  const handleRefresh = useMemoizedFn(async () => {
    try {
      // 先获取最新的 runId
      await fetchRunId();
      // runId 获取成功后会自动触发 queryLog
    } catch (error) {
      console.error('Failed to refresh:', error);
    }
  });

  const changePageNo = useMemoizedFn((pageNo: number) => {
    setPageNo(pageNo);
  });

  // 综合 Loading 状态：runId 获取中 或 日志查询中
  const isLoading = runIdLoading || logLoading;

  // 日志可下载的条件：非加载中 && 日志内容不为空 && 有 runId
  const canDownloadLog = !isLoading && logData?.result?.logContent?.length > 0 && hasRunId;

  /**
   * 下载日志
   */
  const handleDownload = useMemoizedFn(() => {
    if (!runId) {
      console.warn('runId is required for downloading log');
      return;
    }
    // 如果日志为空，不触发下载
    if (!canDownloadLog) {
      return;
    }
    downloadIntegrationExecutionLog(workspaceId, runId);
  });

  // 初始化：先获取 runId
  useEffect(() => {
    fetchRunId();
  }, [fetchRunId]);

  // 当 runId 获取成功后，自动查询日志
  useEffect(() => {
    if (hasRunId) {
      queryLog();
    }
  }, [hasRunId, queryLog]);

  // 处理 runId 获取失败的情况
  if (runIdError) {
    return (
      <div className={styles[kclass]}>
        <div className={styles[`${kclass}_header`]}>
          <span className={styles[`${kclass}_header_title`]}>任务日志</span>
          <div className={styles[`${kclass}_header_btn-grp`]}>
            <Space>
              <Button type="text" onClick={handleRefresh}>
                <OutlinedRefresh />
              </Button>
            </Space>
          </div>
        </div>
        <Divider style={{padding: 0, margin: 0}}></Divider>
        <div className={styles[`${kclass}_container`]}>
          <Empty description="获取运行信息失败，请重试" />
        </div>
      </div>
    );
  }

  return (
    <div className={styles[kclass]}>
      <div className={styles[`${kclass}_header`]}>
        <span className={styles[`${kclass}_header_title`]}>任务日志</span>
        <div className={styles[`${kclass}_header_btn-grp`]}>
          <Space>
            {/* <Button type="text">
              <OutlinedBceSearchLg />
            </Button> */}
            <Button type="text" onClick={handleDownload} disabled={!canDownloadLog}>
              <OutlinedBceDownload />
            </Button>
            <Button type="text" onClick={queryLog}>
              <OutlinedRefresh />
            </Button>
          </Space>
        </div>
      </div>
      <Divider style={{padding: 0, margin: 0}}></Divider>
      <div className={cx('flex', 'h-[600px]')}>
        <div className={styles[`${kclass}_container`]}>
          {logData?.result?.logContent || isLoading ? (
            <>
              <div className={styles['content']}>
                <MonacoLog value={logData?.result?.logContent || ''} loading={isLoading} />
              </div>
              <div className={styles[`${kclass}_hasPage-patination`]}>
                <Pagination
                  className="flex justify-end"
                  size="small"
                  pageSize={1}
                  total={logData?.result?.totalLogPageCount || 0}
                  current={pageNo}
                  onChange={changePageNo}
                />
              </div>
            </>
          ) : (
            <Empty description="暂无日志" />
          )}
        </div>
      </div>
    </div>
  );
};

export default CdcDetailLog;
