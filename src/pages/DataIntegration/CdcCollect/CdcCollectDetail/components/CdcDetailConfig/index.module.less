.cdc-detail-config {
  :global {
    .acud-collapse {
      .acud-collapse-item {
        border: none;

        .acud-collapse-header {
          background-color: #fff;
          padding: 0 0 0 24px;
          margin-bottom: 20px;
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;

          .collapse-header-arrow {
            transform: rotate(-90deg);
            transition: transform 0.3s ease;
            position: absolute;
            left: 0;
            top: 50%;
            margin-top: -8px;
          }

          .acud-collapse-arrow {
            left: 0;
            display: none;
          }
        }

        .acud-collapse-content {
          border: none;

          .acud-collapse-content-box {
            padding: 0 24px 0;
          }
        }
      }

      .acud-collapse-item.acud-collapse-item-active {
        .collapse-header-arrow {
          transform: rotate(0deg);
          transition: transform 0.3s ease;
        }
      }
    }
  }
}
