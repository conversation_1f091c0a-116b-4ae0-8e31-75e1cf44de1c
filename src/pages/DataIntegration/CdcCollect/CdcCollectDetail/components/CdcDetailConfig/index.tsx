/**
 * 实时集成-详情-任务配置
 */
import {useMemo} from 'react';
import {Collapse} from 'acud';
import {JobDetail, JobType} from '@api/integration/type';
import {CdcJobType} from '@api/integration/cdc-type';
import {
  CDC_TASK_TYPE,
  sinkNameRuleMap,
  sinkNameMap,
  getSourceStrategyText,
  dmlStrategyMap,
  dirtyDataStrategyMap
} from '@pages/DataIntegration/CdcCollect/contants';
import CdcDetailMapping from '../CdcDetailMapping';
import DetailWrapper from '@components/DetailWrapper';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import IconSvg from '@components/IconSvg';
import {formatTime} from '@utils/moment';
const cx = classNames.bind(styles);
const klass = 'cdc-detail-config';
const {Panel} = Collapse;
const CdcDetailConfig = ({jobDetail}: {jobDetail: JobDetail<JobType.CDC>}) => {
  // 源端与目标端详情内容配置
  const sourceDetailItems = useMemo(() => {
    const cdcType = jobDetail?.cdcType;
    return [
      {
        title: '基本信息',
        details: [
          {label: '任务名称', value: jobDetail?.name || '-'},
          {label: '任务描述', value: jobDetail?.description || '-'},
          {label: '同步步骤', value: CDC_TASK_TYPE.fromValue(cdcType)?.text || '-'},
          ...(cdcType === CdcJobType.Increment
            ? [
                {
                  label: '增量时间点',
                  value: formatTime(jobDetail?.sourceConfig?.incrementPosition.position)
                }
              ]
            : [])
        ]
      },
      {
        title: '源端信息',
        details: [
          {label: '数据源类型', value: jobDetail?.sourceConfig.sourceType || '-'},
          {label: '数据源名称', value: jobDetail?.sourceConfig.sourceConnectionId || '-'},
          {label: '数据库名称', value: jobDetail?.sourceConfig.sourceDatabase || '-'}
        ]
      },
      {
        title: '目标端信息',
        details: [
          {label: '目标端类型', value: jobDetail?.sinkConfig.sinkType || '-'},
          {label: '数据表类型', value: sinkNameMap[jobDetail?.sinkConfig.sinkTableType] || '-'},
          {
            label: '表名设置',
            value: jobDetail?.sinkConfig.sinkName || sinkNameRuleMap[jobDetail?.sinkConfig.sinkNameRule]
          },
          {label: '建表方式', value: jobDetail?.sinkConfig.isAutoCreated ? '自动建表' : '选择已有表'},
          {label: '前缀', value: jobDetail?.sinkConfig.prefix || '-'},
          {label: '后缀', value: jobDetail?.sinkConfig.suffix || '-'},
          {label: '目标数据库', value: jobDetail?.sinkConfig.sinkPath || '-'}
        ]
      }
    ];
  }, [jobDetail]);
  const runDetailItems = useMemo(() => {
    const sourceChange = jobDetail?.sourceConfig.sourceChange;
    const {dmlConfig, dirtyDataStrategy} = jobDetail?.sinkConfig || {};
    return [
      {
        title: '计算实例信息',
        details: [{label: '计算实例', value: jobDetail?.compute.name || '-'}]
      },
      // {
      //   title: '源端读取信息',
      //   details: [
      //     {label: '最大并发数', value: jobDetail?.sourceConfig.parallelism || '-'},
      //     {label: '开启限速', value: jobDetail?.sourceConfig.enableRateLimit ? '是' : '否'},
      //     {label: '限速大小', value: `${jobDetail?.sourceConfig.rateLimit.flow || '-'}MB/s`},
      //     {
      //       label: '源端表删除字段',
      //       value: getSourceStrategyText(sourceChange?.onDeleteColumn, 'onDeleteColumn')
      //     },
      //     {
      //       label: '源端表被删除',
      //       value: getSourceStrategyText(sourceChange?.onDeleteSource, 'onDeleteSource')
      //     },
      //     {label: '源端表新增字段', value: getSourceStrategyText(sourceChange?.onAddColumn, 'onAddColumn')},
      //     {
      //       label: '源端表重命名字段',
      //       value: getSourceStrategyText(sourceChange?.onRenameColumn, 'onRenameColumn')
      //     },
      //     {
      //       label: '源端表清空数据',
      //       value: getSourceStrategyText(sourceChange?.onTruncateTable, 'onTruncateTable')
      //     },
      //     {
      //       label: '源端表名称重命名',
      //       value: getSourceStrategyText(sourceChange?.onRenameTable, 'onRenameTable')
      //     },
      //     {
      //       label: '源端表修改字段类型',
      //       value: getSourceStrategyText(sourceChange?.onChangeColumn, 'onChangeColumn')
      //     },
      //     {
      //       label: '源端表修改表描述信息',
      //       value: getSourceStrategyText(sourceChange?.onChangeTableComment, 'onChangeTableComment')
      //     },
      //     {
      //       label: '源端表修改字段描述信息',
      //       value: getSourceStrategyText(sourceChange?.onChangeColumnComment, 'onChangeColumnComment')
      //     }
      //   ]
      // },
      {
        title: '目标端写入信息',
        details: [
          {label: '源端表插入数据', value: dmlStrategyMap[dmlConfig?.insert] || '-'},
          {label: '源端表更新数据', value: dmlStrategyMap[dmlConfig?.update] || '-'},
          {label: '源端表删除数据', value: dmlStrategyMap[dmlConfig?.delete] || '-'}
          // {label: '脏数据处理策略', value: dirtyDataStrategyMap[dirtyDataStrategy?.strategy] || '-'},
          // {label: '是否写入脏数据', value: dirtyDataStrategy?.enableDirtyDataWrite ? '是' : '否'},
          // {label: '脏数据存储路径', value: dirtyDataStrategy?.dirtyDataVolume || '-'}
        ]
      }
    ];
  }, [jobDetail]);

  return (
    <div className={cx(styles[klass])}>
      <Collapse defaultActiveKey={['1', '2', '3']} accordion={false}>
        <Panel
          header={
            <div className={styles['collapse-header']}>
              <IconSvg type="arrow-down" size={16} className="collapse-header-arrow" />
              源端与目标端信息
            </div>
          }
          key={1}
        >
          {sourceDetailItems.map(({title, details}) => (
            <DetailWrapper key={title} title={title} details={details} />
          ))}
        </Panel>
        <Panel
          header={
            <div className={styles['collapse-header']}>
              <IconSvg type="arrow-down" size={16} className="collapse-header-arrow" />
              运行信息
            </div>
          }
          key={2}
        >
          {runDetailItems.map(({title, details}) => (
            <DetailWrapper key={title} title={title} details={details} />
          ))}
        </Panel>
        <Panel
          header={
            <div className={styles['collapse-header']}>
              <IconSvg type="arrow-down" size={16} className="collapse-header-arrow" />
              映射信息
            </div>
          }
          key="3"
        >
          <CdcDetailMapping jobDetail={jobDetail} />
        </Panel>
      </Collapse>
    </div>
  );
};
export default CdcDetailConfig;
