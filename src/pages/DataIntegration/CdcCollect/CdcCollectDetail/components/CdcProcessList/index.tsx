/**
 * 实时集成-详情-运行记录
 * 包括：
 * 1. 全量进展（表配置了全量+增量模式时展示）
 * 2. 实时同步
 * @author: <EMAIL>
 */
import React, {useContext, useState, useMemo} from 'react';
import {Radio, Table, Search, Progress} from 'acud';
import {Link} from 'react-router-dom';
import urls from '@utils/urls';
import {formatTime} from '@utils/moment';
import {useRequest} from 'ahooks';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import useUrlState from '@ahooksjs/use-url-state';
import {getCdcListExecutions} from '@api/integration';
import {JobOrderBy, JobDetail, JobType} from '@api/integration/type';
import {CdcJobStage, QueryCdcListExecutionRes, CdcJobType} from '@api/integration/cdc-type';
import {Order} from '@api/common';
import {WorkspaceContext} from '@pages/index';
import {formatEmpty} from '@utils/utils';
import {orderMap} from '@pages/DataIntegration/CdcCollect/contants';
import StatusTag from '@pages/DataIntegration/components/StatusTag';
import RefreshButton from '@components/RefreshButton';
import {CDC_RECORD_STATUS} from '../../../contants';
import styles from './index.module.less';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);
const klass = 'cdc-process-list';
const CdcProcessList = ({jobDetail}: {jobDetail: JobDetail<JobType.CDC>}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [{jobId}] = useUrlState({jobId: '', workspaceId});
  const [sourceTablePattern, setSourceTablePattern] = useState<string>('');
  const isBaseIncrement = jobDetail?.cdcType === CdcJobType.BaseIncrement;
  // Tab切换
  const [stage, setStage] = useState<CdcJobStage>(isBaseIncrement ? CdcJobStage.Base : CdcJobStage.Increment);
  const [total, setTotal] = useState(0);
  const [dataSourceMap, setDataSourceMap] = useState<QueryCdcListExecutionRes>({
    [CdcJobStage.Base]: [],
    [CdcJobStage.Increment]: []
  });
  const [statusFilter, setStatusFilter] = useState([]);
  const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({pageNo: 1, pageSize: 10});
  const [sorter, setSorter] = useState<{field: JobOrderBy | null; order: Order | null}>({
    field: null,
    order: null
  });
  // 获取运行记录（全量和增量同一个接口拉取）
  const {loading, run: getExecutions} = useRequest(
    () =>
      getCdcListExecutions(workspaceId, jobId, {
        ...pagination,
        order: orderMap[sorter.order],
        orderBy: sorter.field,
        sourceTablePattern,
        stage,
        status: statusFilter?.[0]
      }),
    {
      onSuccess: (res) => {
        if (res.success) {
          const dataSourceMap = res.result;
          setDataSourceMap(dataSourceMap);
          setTotal(res.result.totalCount);
        }
      },
      refreshDeps: [workspaceId, jobId, pagination, sorter, sourceTablePattern, stage, statusFilter]
    }
  );
  // 全量进展 - 列表配置项
  const baseColumns = [
    {
      title: '源端表名称',
      dataIndex: 'sourceTable',
      width: 120,
      ellipsis: true,
      render: (text: string) => <Ellipsis tooltip={text}>{formatEmpty(text)}</Ellipsis>
    },
    {
      title: '目标端表名称',
      dataIndex: 'sinkTablePath',
      width: 120,
      ellipsis: true,
      render: (text: string) => {
        const [catalog = '', schema = '', table = ''] = (text ?? '').split('.');
        return table ? (
          <Link
            to={`${urls.metaData}?workspaceId=${workspaceId}&catalog=${catalog}&schema=${schema}&node=${table}&type=table`}
          >
            <Ellipsis tooltip={table}>{table}</Ellipsis>
          </Link>
        ) : (
          '-'
        );
      }
    },
    {
      title: '全量进度',
      dataIndex: 'syncProgress',
      width: 140,
      render: (text: string) => {
        return <Progress percent={Number(text)} type="line" />;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      filterMultiple: false,
      filters: CDC_RECORD_STATUS.toArray(),
      filteredValue: statusFilter,
      render: (text: string) => {
        const statusObj = CDC_RECORD_STATUS.fromValue(text);
        return <StatusTag text={statusObj.text} className={statusObj.className} />;
      }
    },
    // {
    //   title: '读取行数',
    //   dataIndex: 'readCount',
    //   width: 80,
    //   render: (text: string) => formatEmpty(text)
    // },
    // {
    //   title: '写入行数',
    //   dataIndex: 'writeCount',
    //   width: 80,
    //   render: (text: string) => formatEmpty(text)
    // },
    {
      title: '全量开始时间',
      dataIndex: 'startTime',
      sorter: true,
      width: 140,
      render: (text: string) => formatTime(text)
    },
    {
      title: '全量结束时间',
      dataIndex: 'finishTime',
      sorter: true,
      width: 140,
      render: (text: string) => formatTime(text)
    }
  ];
  // 实时同步 - 列表配置项
  const incrementColumns = [
    {
      title: '源端表名称',
      dataIndex: 'sourceTable',
      width: 120,
      ellipsis: true,
      render: (text: string) => <Ellipsis tooltip={text}>{formatEmpty(text)}</Ellipsis>
    },
    {
      title: '目标端表名称',
      dataIndex: 'sinkTablePath',
      width: 120,
      ellipsis: true,
      render: (text: string) => {
        const [catalog = '', schema = '', table = ''] = (text ?? '').split('.');
        return table ? (
          <Link
            to={`${urls.metaData}?workspaceId=${workspaceId}&catalog=${catalog}&schema=${schema}&node=${table}&type=table`}
          >
            <Ellipsis tooltip={table}>{table}</Ellipsis>
          </Link>
        ) : (
          '-'
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      filterMultiple: false,
      width: 100,
      filters: CDC_RECORD_STATUS.toArray(),
      filteredValue: statusFilter,
      render: (text: string) => {
        const statusObj = CDC_RECORD_STATUS.fromValue(text);
        return <StatusTag text={statusObj.text} className={statusObj.className} />;
      }
    }
    // {
    //   title: '终止时间',
    //   dataIndex: 'stopTime',
    //   width: 150,
    //   render: (text: string) => formatTime(text)
    // },
    // {
    //   title: '终止原因',
    //   dataIndex: 'stopMessage',
    //   width: 120,
    //   ellipsis: true,
    //   render: (text: string) => formatEmpty(text)
    // },
    // {
    //   title: '读取行数',
    //   dataIndex: 'readCount',
    //   width: 80,
    //   render: (text: string) => formatEmpty(text)
    // },
    // {
    //   title: '写入行数',
    //   dataIndex: 'writeCount',
    //   width: 80,
    //   render: (text: string) => formatEmpty(text)
    // },
    // {
    //   title: '脏数据行数',
    //   dataIndex: 'dirtyCount',
    //   width: 100,
    //   render: (text: string) => formatEmpty(text)
    // },
    // {
    //   title: '读取大小',
    //   dataIndex: 'readBytes',
    //   width: 80,
    //   render: (text) => {
    //     return `${text}B`;
    //   }
    // },
    // {
    //   title: '写入大小',
    //   dataIndex: 'writeBytes',
    //   width: 80,
    //   render: (text) => {
    //     return `${text}B`;
    //   }
    // },
    // {
    //   title: '脏数据大小',
    //   dataIndex: 'dirtyBytes',
    //   width: 100,
    //   render: (text) => {
    //     return `${text}B`;
    //   }
    // }
  ];
  const radioOptions = useMemo(() => {
    return [
      ...(isBaseIncrement
        ? [
            {
              value: CdcJobStage.Base,
              label: '全量进度'
            }
          ]
        : []),
      {
        value: CdcJobStage.Increment,
        label: '实时同步'
      }
    ];
  }, [isBaseIncrement]);
  const columnsMap = {
    [CdcJobStage.Base]: baseColumns,
    [CdcJobStage.Increment]: incrementColumns
  };
  const handleTableChange = (pag, filters, sorter) => {
    setPagination({...pagination, pageNo: pag.current, pageSize: pag.pageSize});
    setSorter({field: sorter.field, order: sorter.order});
    setStatusFilter(filters.status || []);
  };
  return (
    <div className={cx(styles[klass])}>
      <div className={cx(styles[`${klass}-header`], 'mb-[16px]')}>
        <div className={cx(styles['header-left'])}>
          <Radio.Group
            options={radioOptions}
            value={stage}
            optionType="button"
            onChange={(e) => {
              setStage(e.target.value);
            }}
          ></Radio.Group>
          <Search
            className="ml-[16px]"
            placeholder="请输入表名称搜索"
            onSearch={(value) => setSourceTablePattern(value)}
            style={{width: 240}}
          />
        </div>
        <div className={cx(styles['header-right'])}>
          <RefreshButton onClick={getExecutions} />
        </div>
      </div>
      <div className={cx(styles[`${klass}-content`])}>
        <Table
          dataSource={dataSourceMap[stage]}
          columns={columnsMap[stage]}
          scroll={{x: 1150}}
          onChange={handleTableChange}
          loading={loading}
          pagination={{
            current: pagination.pageNo,
            pageSize: pagination.pageSize,
            total: total,
            pageSizeOptions: ['10', '20', '50', '100'],
            showSizeChanger: true
          }}
        ></Table>
      </div>
    </div>
  );
};
export default CdcProcessList;
