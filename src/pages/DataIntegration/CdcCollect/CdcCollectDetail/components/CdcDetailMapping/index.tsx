/**
 * 实时任务详情 - 任务配置的映射信息
 * <AUTHOR>
 */
import React, {useState, useEffect, useCallback, useMemo, useContext} from 'react';
import _ from 'lodash';
import {Tree, Search} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {JobDetail, JobType} from '@api/integration/type';
import {useDispatch} from 'react-redux';
import {SchemaConnectionType} from '@pages/DataIntegration/CdcCollect/CdcCollectCreate/constants';
import {BatchTableColumns, getDatasourceTableColumns} from '@api/integration/batch';
import EditMappingTable from '@pages/DataIntegration/CdcCollect/CdcCollectCreate/components/EditMappingTable';
import {WorkspaceContext} from '@pages/index';
import {JsPlumbIdEnum} from '@pages/DataIntegration/constants';
import styles from './index.module.less';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);
const CdcDetailMapping: React.FC<{jobDetail: JobDetail<JobType.CDC>}> = ({jobDetail}) => {
  // 1. 准备tree数据
  const [treeData, setTreeData] = useState([]);
  const [searchValue, setSearchValue] = useState<string>('');
  const [tableConfigs, setTableConfigs] = useState([]);
  const dispatch = useDispatch();
  useEffect(() => {
    const sourceConfig = jobDetail?.sourceConfig;
    const tableConfigs = jobDetail?.mappingConfig?.tableConfigs;

    const renewTableConfig = _.map(tableConfigs, (tableConfig) => {
      const sinkFields = _.map(tableConfig.sinkFields, (item) => ({
        ...item,
        id: `${JsPlumbIdEnum.TARGET}${item.name}`
      }));
      return {
        ...tableConfig,
        sinkFields,
        mapping: _.map(tableConfig.mapping, (item) => {
          const target = _.find(sinkFields, (field) => field.name === item.sinkColumn);
          return {
            ...item,
            targetId: target?.id
          };
        })
      };
    });
    setTableConfigs(renewTableConfig);
    const children = _.map(tableConfigs, (tableConfig) => ({
      title: tableConfig?.sourceTable,
      key: tableConfig?.sourceTable,
      isLeaf: true
    }));
    const treeData = [
      {
        title: sourceConfig?.sourceConnectionId,
        key: sourceConfig?.sourceConnectionId,
        children: [
          {
            title: sourceConfig?.sourceDatabase,
            key: sourceConfig?.sourceDatabase,
            children: _.filter(children, (child) =>
              child.title.toLowerCase().includes(searchValue.toLowerCase())
            )
          }
        ]
      }
    ];
    dispatch({
      type: 'cdcIntegration/updateTableConfigs',
      payload: {
        ...renewTableConfig
      }
    });
    dispatch({
      type: 'cdcIntegration/updateSinkConfig',
      payload: {
        ...jobDetail?.sinkConfig
      }
    });
    setTreeData(treeData);
  }, [jobDetail, dispatch, searchValue]);
  // 2: tree点击事件
  const [selectedKey, setSelectedKey] = useState<string>(null);
  const [sourceData, setSourceData] = useState<BatchTableColumns[]>([]);
  const {workspaceId} = useContext(WorkspaceContext);
  // 获取源端表数据
  const getSourceTableData = useMemoizedFn(async (sourceTable) => {
    const sourceConfig = jobDetail?.sourceConfig;
    const res = await getDatasourceTableColumns({
      environment: {
        workspaceId,
        computeId: jobDetail?.compute?.computeId
      },
      datasourceInfo: {
        connectionId: sourceConfig?.sourceConnectionId,
        database: sourceConfig?.sourceDatabase,
        ...(SchemaConnectionType.includes(sourceConfig?.sourceType)
          ? {schema: sourceConfig?.sourceSchema}
          : {}),
        table: sourceTable
      }
    });
    const sourceData = res?.result?.columns || [];
    setSourceData(sourceData);
  });
  const onTreeClick = useCallback(
    (event, node) => {
      if (node?.isLeaf) {
        setSelectedKey(node.key);
        getSourceTableData(node.key);
      }
    },
    [setSelectedKey, getSourceTableData]
  );
  // 3：mapping数据展示
  const mappingData = useMemo(() => {
    const tableIndex = _.findIndex(tableConfigs, (item) => item.sourceTable === selectedKey);
    return {
      tableIndex,
      sourceTable: selectedKey,
      sourceData: sourceData,
      mapping: tableIndex !== -1 ? tableConfigs[tableIndex]?.mapping : []
    };
  }, [selectedKey, sourceData, tableConfigs]);
  return (
    <div className={cx(styles['cdc-detail-mapping'])}>
      <div className={cx(styles['mapping-container'], 'mr-[8px]')}>
        <div className={cx(styles['tree-title'])}>源端数据源</div>
        <div className={cx(styles['tree-container'])}>
          <Search placeholder="请输入名称搜索" allowClear onSearch={(value) => setSearchValue(value)} />
          <Tree
            treeData={treeData}
            defaultExpandAll
            searchPlaceholder="请输入搜索内容"
            onClick={onTreeClick}
          />
        </div>
      </div>
      {selectedKey && <EditMappingTable {...mappingData} onChangeMapping={() => {}} />}
    </div>
  );
};

export default CdcDetailMapping;
