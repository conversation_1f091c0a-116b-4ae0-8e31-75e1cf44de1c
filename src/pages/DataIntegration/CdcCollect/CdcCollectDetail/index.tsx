/**
 * 实时集成-详情页
 * 包括：
 * 1. 运行记录（全量&实时）
 * 2. 运行日志
 * 3. 任务配置
 * 4. 统计信息（本期不做）
 * @author: <EMAIL>
 */

import React, {useContext, useState, useMemo} from 'react';
import useUrlState from '@ahooksjs/use-url-state';
import {useNavigate} from 'react-router-dom';
import {Loading, Breadcrumb, Tabs, Menu, Space, Dropdown, Tooltip, Button, Divider} from 'acud';
import {Link} from 'react-router-dom';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import {formatTime} from '@utils/moment';
import {useRequest} from 'ahooks';
import {getJobDetails} from '@/api/integration';
import {JobDetail, JobType} from '@api/integration/type';
import {IntegrationTab} from '@pages/DataIntegration/constants';
import StatusTag from '@pages/DataIntegration/components/StatusTag';
import TextEllipsis from '@components/TextEllipsisTooltip';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import AuthButton from '@components/AuthComponents/AuthButton';
import IconSvg from '@components/IconSvg';
import {ConnectionTypeFlatMap} from '@pages/MetaData/Connection/constants';
import {getOperationList, JobItem} from '../utils';
import {DetailTab, CDC_STATUS, OperationShowType, CdcOperation} from '../contants';
import CdcProcessList from './components/CdcProcessList';
import CdcDetailConfig from './components/CdcDetailConfig';
import CdcDetailLog from './components/CdcDetailLog';

import styles from './index.module.less';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);
const {TabPane} = Tabs;
const CdcCollectDetail: React.FC = () => {
  const navigate = useNavigate();
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  const [{jobId, tab}, setUrlState] = useUrlState({
    jobId: '',
    workspaceId: workspaceId,
    tab: DetailTab.ProcessList
  });
  const [jobDetail, setJobDetail] = useState<JobDetail<JobType.CDC>>();
  // 获取详情数据
  const {loading, run: getDetail} = useRequest(() => getJobDetails<JobType.CDC>(workspaceId, jobId), {
    onSuccess: (res) => {
      if (res.success) {
        setJobDetail(res.result as any);
      }
    },
    refreshDeps: [workspaceId, jobId]
  });
  const onTabChange = (key: string) => {
    setUrlState({tab: key});
  };
  const jumpCdc = () => {
    navigate(`${urls.integration}?tab=${IntegrationTab.Realtime}`);
  };
  // 详情标题+操作
  const renderTitle = () => {
    const statusObj = CDC_STATUS.fromValue(jobDetail?.status);
    const operations = getOperationList(navigate, {
      status: jobDetail?.status,
      privileges: jobDetail?.privileges
    });
    const jobs = [
      {jobId, name: String(jobDetail?.name), cdcType: jobDetail?.cdcType, subStatus: jobDetail?.subStatus}
    ] as JobItem[];
    const menu = (
      <Menu>
        {operations[OperationShowType.DetailDropdown].map((item, index) => (
          <AuthComponents key={index} isAuth={item.isAuth}>
            <Menu.Item
              onClick={() =>
                item.callback(
                  jobs,
                  workspaceId,
                  item.key === CdcOperation.Delete ? jumpCdc : getDetail,
                  navigate
                )
              }
              disabled={item.disabled}
            >
              {item?.label}
            </Menu.Item>
          </AuthComponents>
        ))}
      </Menu>
    );
    return (
      <>
        <div className={cx(styles['job-name'])}>
          <TextEllipsis tooltip={jobDetail?.name}>{jobDetail?.name || '-'}</TextEllipsis>
          <StatusTag text={statusObj.text} className={statusObj.className} style={{marginLeft: '12px'}} />
        </div>
        <div className={cx(styles['job-opr'])}>
          <Space>
            <Dropdown overlay={menu} trigger={['click']}>
              <Tooltip title="更多">
                <Button className="square-button">
                  <IconSvg type="more" size={16} />
                </Button>
              </Tooltip>
            </Dropdown>
            {operations[OperationShowType.DetailList].map((item, index) => (
              <AuthButton
                isAuth={item.isAuth}
                type="primary"
                key={index}
                onClick={() => item.callback(jobs, workspaceId, getDetail, navigate)}
                disabled={item.disabled}
              >
                {item.label}
              </AuthButton>
            ))}
          </Space>
        </div>
      </>
    );
  };
  // 来源图标
  const sourceIcon = useMemo(() => {
    const target = ConnectionTypeFlatMap[jobDetail?.sourceConfig?.sourceType];
    return (
      <IconSvg
        type={target?.icon}
        color={target?.color || undefined}
        size={16}
        className="bordered-circle-icon mr-[5px]"
      />
    );
  }, [jobDetail?.sourceConfig?.sourceType]);
  // 详情信息行
  const renderDetailInfo = () => {
    return (
      <>
        <div className={styles['source-name']}>
          {sourceIcon}
          {jobDetail?.sourceConfig?.sourceConnectionId || '-'}
        </div>
        <IconSvg type="right" size={16} className="mr-[8px] ml-[8px]" />
        <div className={styles['dest-name']}>{jobDetail?.sinkConfig?.sinkType || '-'}</div>
        <Divider type="vertical" />
        <div className={styles['info-text']}>
          <Tooltip title="创建人">
            <IconSvg type="user" size={16} />
          </Tooltip>
          {jobDetail?.creatorName || '-'}
        </div>
        <Divider type="vertical" />
        <div className={styles['info-text']}>
          <Tooltip title="创建时间">
            <IconSvg type="time" size={16} />
          </Tooltip>
          {formatTime(jobDetail?.createTime)}
        </div>
        <Divider type="vertical" />
        <div className={cx(styles['info-text'], styles['info-desc'])}>
          <Tooltip title="任务描述">
            <IconSvg type="description" size={16} />
          </Tooltip>
          <TextEllipsis tooltip={jobDetail?.description}>{jobDetail?.description || '-'}</TextEllipsis>
        </div>
      </>
    );
  };
  return (
    <div className={cx('db-workspace-wrapper', styles['cdc-job-detail'])}>
      <Loading loading={loading} />
      <Breadcrumb className="mb-4">
        <Breadcrumb.Item>
          <Link to={`${urls.integration}`}>库表采集</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link to={`${urls.integration}?tab=${IntegrationTab.Realtime}`}>实时同步</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>任务详情</Breadcrumb.Item>
      </Breadcrumb>
      <div className={cx(styles['cdc-job-detail-title'], 'mb-[8px]')}>{renderTitle()}</div>
      <div className={cx(styles['cdc-job-detail-info'], 'mb-[16px]')}>{renderDetailInfo()}</div>
      <div className={cx(styles['cdc-job-detail-tabs'])}>
        <Tabs activeKey={tab} onChange={onTabChange} destroyInactiveTabPane={true}>
          <TabPane tab="运行记录" key={DetailTab.ProcessList}>
            <CdcProcessList jobDetail={jobDetail} />
          </TabPane>
          <TabPane tab="运行日志" key={DetailTab.Log}>
            <CdcDetailLog />
          </TabPane>
          <TabPane tab="任务配置" key={DetailTab.DetailConfig}>
            <CdcDetailConfig jobDetail={jobDetail} />
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default CdcCollectDetail;
