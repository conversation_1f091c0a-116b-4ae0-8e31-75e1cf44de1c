import {CDC_SUBSTATUS, CdcOperation, OperationShowType, EditMode, TreeSplit} from './contants';
import {BatchOperateType} from '@api/integration/type';
import {CdcJob, CdcJobStatus, SubStatus} from '@api/integration/cdc-type';
import {authCheck} from '@pages/DataIntegration/SqlCollect/OfflineCollect/utils';
import {Privilege} from '@api/permission/type';
import * as http from '@api/integration';
import {Modal, toast} from 'acud';
import {onStart} from './CdcCollectList/components/CdcCollectRun';
import {onPrecheck} from './CdcCollectList/components/CdcCollectPrecheck';
import {onOpenCreateModal} from './CdcCollectList/components/CdcCreateModal';
import urls from '@utils/urls';
import moment from 'moment';

// 格式化时间 秒 => 天:时:分:秒
export const transSecond2Time = (seconds: number) => {
  if (!seconds) return '0秒';
  if (seconds === -1) return '-';

  const day = Math.floor(seconds / 86400);
  const hour = Math.floor((seconds % 86400) / 3600);
  const minute = Math.floor((seconds % 3600) / 60);
  const second = seconds % 60;

  return `${day ? `${day}天` : ''}${hour ? `${hour}时` : ''}${minute ? `${minute}分` : ''}${second}秒`;
};

// 执行概况文字处理
export const transStatus2Overview = ({base, increment}: SubStatus) => {
  const baseOverview = base ? CDC_SUBSTATUS.fromValue(base).text : '';
  const incrementOverview = increment ? CDC_SUBSTATUS.fromValue(increment).text : '';
  return `${baseOverview ? `全量${baseOverview}，` : ''}${increment ? `增量${incrementOverview}` : ''}`;
};

// 操作类型
export type OperationConfig = Record<
  OperationShowType,
  {label: string; callback: any; key: CdcOperation; isAuth?: boolean}[] | null
>;

export type JobItem = CdcJob & {
  operations: OperationConfig;
};

// 删除和批量删除 - 目前只支持删除单个任务
export const onDelete = (jobs: JobItem[], workspaceId: string, callback) => {
  const jobNames = jobs.map((job) => `"${job.name}"`).join('、');
  const isBatch = jobs.length > 1;
  Modal.confirm({
    title: `${isBatch ? '批量删除' : '删除任务'}`,
    content: `${jobNames}删除后，任务运行数据将被清空，无法恢复，请确认是否删除。`,
    onOk: async () => {
      try {
        const res = isBatch
          ? await http.batchOperationIntegrationJob(workspaceId, BatchOperateType.Delete, {
              jobIds: jobs.map((item) => item.jobId)
            })
          : await http.deleteIntegrationJob(workspaceId, jobs[0].jobId);
        if (res.success) {
          toast.success({message: `${jobNames}删除成功`, duration: 5});
          callback();
        }
      } catch {
        console.error('删除任务失败');
      }
    }
  });
};
// 暂停任务和批量暂停任务 - 目前只支持暂停单个任务
export const onSuspend = async (jobs: JobItem[], workspaceId: string, callback) => {
  const jobNames = jobs.map((job) => `"${job.name}"`).join('、');
  const isBatch = jobs.length > 1;
  Modal.confirm({
    title: `${isBatch ? '批量暂停' : '暂停任务'}`,
    content: `确认暂停${jobNames}任务吗？`,
    onOk: async () => {
      try {
        const res = isBatch
          ? await http.batchOperationIntegrationJob(workspaceId, BatchOperateType.Stop, {
              jobIds: jobs.map((item) => item.jobId)
            })
          : await http.suspendCdcIntegrationJob(workspaceId, jobs[0].jobId);
        if (res.success) {
          toast.success({message: `${jobNames}暂停成功`, duration: 5});
          callback();
        }
      } catch {
        console.error('暂停任务失败');
      }
    }
  });
};

// 复制
export const onCopy = async (jobs: JobItem[], workspaceId: string, callback) => {
  const {name, jobId} = jobs[0];
  try {
    const res = await http.copyIntegrationJob(workspaceId, jobId);
    if (res.success) {
      toast.success({message: `${name}复制成功`, duration: 5});
      callback();
    }
  } catch {
    console.error('复制任务失败');
  }
};

// 创建/编辑
export const onEdit = (mode: EditMode, id: string, type, navigate) => {
  if (mode === EditMode.Create) {
    onOpenCreateModal(navigate);
  } else {
    navigate(`${urls.cdcCollectCreate}?mode=${mode}&jobId=${id}`);
  }
};

// 根据操作配置列表聚合展示操作项
export const getOperationList = (navigate, obj?: {privileges: string[]; status: CdcJobStatus}) => {
  const {status, privileges} = obj || {};
  const PreCheck = {
    label: '前置检查',
    key: CdcOperation.PreCheck,
    callback: onPrecheck,
    isAuth: authCheck(privileges, Privilege.Execute),
    disabled: [
      CdcJobStatus.Ready,
      CdcJobStatus.Running,
      CdcJobStatus.PreCheck,
      CdcJobStatus.Suspending
    ].includes(status)
  };
  const Start = {
    label: '运行',
    key: CdcOperation.Start,
    disabled: ![CdcJobStatus.Checkpass, CdcJobStatus.Suspend, CdcJobStatus.Failed].includes(status),
    callback: onStart,
    isAuth: authCheck(privileges, Privilege.Execute)
  };
  const Suspend = {
    label: '暂停',
    key: CdcOperation.Suspend,
    callback: onSuspend,
    disabled: ![CdcJobStatus.Running].includes(status),
    isAuth: authCheck(privileges, Privilege.Execute)
  };
  const Delete = {
    label: '删除',
    key: CdcOperation.Delete,
    disabled: [
      CdcJobStatus.Ready,
      CdcJobStatus.Running,
      CdcJobStatus.PreCheck,
      CdcJobStatus.Suspending
    ].includes(status),
    callback: onDelete,
    isAuth: authCheck(privileges, Privilege.Manage)
  };
  const Edit = {
    label: '编辑',
    key: CdcOperation.Edit,
    isAuth: authCheck(privileges, Privilege.Manage),
    disabled: [
      CdcJobStatus.Ready,
      CdcJobStatus.Running,
      CdcJobStatus.PreCheck,
      CdcJobStatus.Suspending
    ].includes(status),
    callback: (jobs: JobItem[]) => onEdit(EditMode.Edit, jobs?.[0]?.jobId, jobs?.[0]?.sourceType, navigate)
  };
  const Copy = {
    label: '复制',
    key: CdcOperation.Copy,
    disabled: false,
    callback: onCopy,
    isAuth: authCheck(privileges, Privilege.Manage)
  };

  const initialList = {
    [OperationShowType.List]: [PreCheck, Edit, Start],
    [OperationShowType.Dropdown]: [Delete, Suspend, Copy],
    [OperationShowType.DetailDropdown]: [PreCheck, Suspend, Copy, Delete],
    [OperationShowType.DetailList]: [Start, Edit]
    // [OperationShowType.ResultDropdown]: [PreCheck, Edit, Copy, Delete]
  };

  return initialList;
};

export const getTreeKey = (keyList: string[]) => keyList.join(TreeSplit);

export const getKeyList = (key: string) => key.split(TreeSplit);

// 时间位点 - 禁用当前时间之后的日期和时间，并且禁用时分秒小于当前时间的选项
export const disabledDate = (date) => {
  return date && date.isAfter(moment(), 'day');
};
export const disabledTime = (date) => {
  if (date && date.isSame(moment(), 'day')) {
    const hour = moment().hour();
    const minute = moment().minute();
    const second = moment().second();
    return {
      disabledHours: () => Array.from({length: 24}, (_, i) => i).filter((h) => h > hour),
      disabledMinutes: (selectedHour) =>
        selectedHour === hour ? Array.from({length: 60}, (_, i) => i).filter((m) => m > minute) : [],
      disabledSeconds: (selectedHour, selectedMinute) =>
        selectedHour === hour && selectedMinute === minute
          ? Array.from({length: 60}, (_, i) => i).filter((s) => s > second)
          : []
    };
  }
  return {};
};
