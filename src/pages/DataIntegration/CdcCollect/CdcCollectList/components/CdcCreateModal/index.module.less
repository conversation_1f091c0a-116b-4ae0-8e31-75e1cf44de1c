.title {
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0;
  margin-bottom: 8px;
}

.card-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  // 节点间距94px
  gap: 12px;
  float: left;
}

.card-item {
  width: 184px;
  height: 94px;
  border-radius: 8px;
  border: 1px solid #e5e5e5;
  text-align: center;
  margin: 0 auto;
  cursor: pointer;
  padding: 12px;
  &:hover {
    border-color: #1890ff;
  }
  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  &.active {
    border-color: #1890ff;
    background-color: #e6f0ff;
  }
  .card-item-icon {
    width: 100%;
    height: 32px;
    margin-bottom: 12px;
    svg {
      border-radius: 50%;
    }
  }
  .card-item-label {
    font-size: 12px;
    line-height: 16px;
    font-weight: 500;
  }
}
