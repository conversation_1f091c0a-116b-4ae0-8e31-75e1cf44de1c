/**
 * 实时创建任务前置弹窗
 */

import IconSvg from '@components/IconSvg';
import urls from '@utils/urls';
import {Button, Modal, Space} from 'acud';
import {SourceTypeOptions} from '@pages/DataIntegration/CdcCollect/CdcCollectCreate/constants';
import {useMemoizedFn} from 'ahooks';
import ReactDOM from 'react-dom/client';
import classNames from 'classnames/bind';
import React, {useMemo, useState} from 'react';
import styles from './index.module.less';
const cx = classNames.bind(styles);

const CdcCreateModal: React.FC<{
  navigate: (path: string) => void;
  afterClose?: () => void;
}> = ({navigate, afterClose}) => {
  const [visible, setVisible] = useState(true);
  const [activeItem, setActiveItem] = useState<string | null>(null);

  const handleOk = useMemoizedFn(() => {
    navigate(urls.cdcCollectCreate + `?sourceType=${activeItem}`);
    afterClose?.();
  });
  const footer = useMemo(
    () => (
      <Space>
        <Button type="default" onClick={() => setVisible(false)}>
          取消
        </Button>
        <Button disabled={!activeItem} type="primary" onClick={() => handleOk()}>
          确定
        </Button>
      </Space>
    ),
    [activeItem, handleOk]
  );
  return (
    <Modal
      visible={visible}
      title={`创建`}
      width={512}
      footer={footer}
      onCancel={() => setVisible(false)}
      afterClose={afterClose}
    >
      <div className={styles['title']}>结构化数据</div>
      <div className={styles['card-list']}>
        {Object.values(SourceTypeOptions).map((item) => (
          <div
            onClick={() => setActiveItem(item.value)}
            key={item.value}
            className={cx('card-item', {active: activeItem === item.value})}
          >
            <div className={styles['card-item-icon']}>
              <IconSvg type={item.icon} size={32} />
            </div>
            <div className={styles['card-item-label']}>{item.label}</div>
          </div>
        ))}
      </div>
    </Modal>
  );
};

export const onOpenCreateModal = (navigate) => {
  const container = document.createElement('div');
  document.body.appendChild(container);

  const root = ReactDOM.createRoot(container);
  const afterClose = () => {
    root.unmount();
    container.remove();
  };
  root.render(<CdcCreateModal afterClose={afterClose} navigate={navigate} />);
};
