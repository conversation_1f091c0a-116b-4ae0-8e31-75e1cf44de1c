/**
 * 实时作业 - 运行弹窗
 * <AUTHOR>
 */
import moment from 'moment';
import React, {useState, useMemo, useEffect} from 'react';
import {JobItem, disabledDate, disabledTime} from '../../../utils';
import {FileJobRunType} from '@api/integration/type';
import {IncrementPositionType, CdcSubStatus, CdcJobType} from '@api/integration/cdc-type';
import {useMemoizedFn} from 'ahooks';
import {startCdcIntegrationJob, getEarliestTimePoint} from '@api/integration';
import urls from '@utils/urls';
import {Modal, Switch, Form, DatePicker, Space, Button, toast, Tooltip} from 'acud';
import {Link} from 'react-router-dom';
import ReactDOM from 'react-dom/client';

interface simpleJobItem {
  jobId: string;
}
const StartModal: React.FC<{
  jobs: JobItem[] | simpleJobItem[];
  workspaceId: string;
  callback: () => void;
  navigate: (path: string) => void;
  afterClose: () => void;
}> = ({jobs, workspaceId, callback, navigate, afterClose}) => {
  const [visible, setVisible] = useState(true);
  const disableResetPosition = useMemo(() => {
    const {cdcType, subStatus} = jobs?.[0] as JobItem;
    return cdcType === CdcJobType.BaseIncrement && subStatus?.base !== CdcSubStatus.Closed;
  }, [jobs]);
  // 最早时间位点
  const [currentTime, setCurrentTime] = useState(moment());
  const [form] = Form.useForm();
  const isReset = Form.useWatch('isReset', form);
  // 运行任务 是否查看详情
  const runJob = useMemoizedFn(async (flag: boolean) => {
    const formData = form.getFieldsValue();
    const jobId = jobs?.[0]?.jobId;
    const {success} = await startCdcIntegrationJob(workspaceId, jobId, {
      resetIncrementPosition: isReset,
      incrementPosition: {
        type: IncrementPositionType.Timestamp,
        position: formData.timePoint || currentTime
      }
    });

    if (!success) {
      toast.error('运行失败');
      return;
    }
    setVisible(false);

    if (flag) {
      navigate(`${urls.cdcCollectDetail}?jobId=${jobId}`);

      toast.success({
        message: '运行成功！',
        duration: 5
      });
    } else {
      toast.success({
        message: '运行提交成功',
        description: (
          <span>
            请前往运行记录查看结果，立即前往
            <Link
              className="global-notify-ticket-link cursor-pointer"
              to={`${urls.cdcCollectDetail}?jobId=${jobId}`}
            >
              运行记录
            </Link>
          </span>
        ),
        duration: 5
      });
      callback();
    }
  });
  // 获取最早时间位点 - 本期不做
  // const getTime = useMemoizedFn(async (workspaceId: string, jobId: string) => {
  //   const {result, success} = await getEarliestTimePoint(workspaceId, jobId);
  //   if (!success) {
  //     console.error('获取时间位点失败');
  //     return;
  //   }
  //   if (result.earliestTimestampPosition) {
  //     setCurrentTime(moment.utc(result.earliestTimestampPosition));
  //   }
  // });
  // useEffect(() => {
  //   getTime(workspaceId, jobs[0].jobId);
  // }, [getTime, workspaceId, jobs]);

  // 底部按钮配置
  const footer = (
    <Space>
      <Button type="default" onClick={() => setVisible(false)}>
        取消
      </Button>
      <Button type="enhance" onClick={() => runJob(false)}>
        运行
      </Button>
      <Button type="primary" onClick={() => runJob(true)}>
        运行并查看详情
      </Button>
    </Space>
  );
  return (
    <Modal
      visible={visible}
      title="运行"
      width={632}
      footer={footer}
      onCancel={() => setVisible(false)}
      afterClose={afterClose}
    >
      <Form
        className="mt-[4px]"
        form={form}
        labelWidth={80}
        labelAlign="left"
        initialValues={{
          isReset: false,
          timePoint: currentTime
        }}
      >
        <Form.Item label="是否重置位点：" name="isReset">
          {disableResetPosition ? (
            <Tooltip
              placement="right"
              visible={disableResetPosition}
              title="全量-增量任务仅在增量任务开始时才能重置位点"
            >
              <Switch disabled />
            </Tooltip>
          ) : (
            <Switch />
          )}
        </Form.Item>

        {isReset && (
          <>
            {/* <Form.Item label="最早时间位点：">{currentTime.format('YYYY-MM-DD HH:mm:ss')}</Form.Item> */}
            <Form.Item label="时间位点：" name="timePoint">
              <DatePicker showTime disabledDate={disabledDate} disabledTime={disabledTime} />
            </Form.Item>
          </>
        )}
      </Form>
    </Modal>
  );
};
export default StartModal;

// 运行和批量运行弹窗 - 目前仅支持单个作业
export const onStart = (jobs: JobItem[] | simpleJobItem[], workspaceId: string, callback, navigate) => {
  const container = document.createElement('div');
  document.body.appendChild(container);

  const root = ReactDOM.createRoot(container);
  const afterClose = () => {
    root.unmount();
    container.remove();
  };
  root.render(
    <StartModal
      jobs={jobs}
      workspaceId={workspaceId}
      callback={callback}
      navigate={navigate}
      afterClose={afterClose}
    />
  );
};
