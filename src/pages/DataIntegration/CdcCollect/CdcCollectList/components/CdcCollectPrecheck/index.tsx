/**
 * 实时集成-前置检查弹窗
 * @author: zhao<PERSON><PERSON><EMAIL>
 */
import {JobItem} from '@pages/DataIntegration/CdcCollect/utils';
import ReactDOM from 'react-dom/client';
import {IntegrationTab} from '@pages/DataIntegration/constants';
import PreCheckModal from '@pages/DataIntegration/components/PreCheckModal';
export const onPrecheck = (jobs: JobItem[], workspaceId: string, callback, navigate) => {
  const container = document.createElement('div');
  document.body.appendChild(container);

  const root = ReactDOM.createRoot(container);
  const afterClose = () => {
    root.unmount();
    container.remove();
  };
  const preCheckModal = (
    <PreCheckModal
      jobs={jobs}
      workspaceId={workspaceId}
      callback={callback}
      navigate={navigate}
      afterClose={afterClose}
      isPreCheck={true}
      type={IntegrationTab.Realtime}
    />
  );
  root.render(preCheckModal);
};
