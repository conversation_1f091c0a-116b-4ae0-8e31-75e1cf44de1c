/**
 * 实时集成-列表
 * @author: <EMAIL>
 */
import React, {useState, useContext, useRef} from 'react';
import {Search, Button, Table, Menu, Space, Dropdown} from 'acud';
import moment from 'moment';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {ColumnsType} from 'acud/lib/table';
import {useNavigate, Link} from 'react-router-dom';
import {getIntegrationJobList} from '@api/integration';
import {JobType, JobOrderBy} from '@api/integration/type';
import {SubStatus, CdcJobStatus} from '@api/integration/cdc-type';
import {Order} from '@api/common';
import {WorkspaceContext} from '@pages/index';
import {Privilege, ResourceType} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import AuthButton from '@components/AuthComponents/AuthButton';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import PermissionModal, {PermissionModalRef} from '@components/Workspace/PermissionModal';
import {PRIVILEGE_LIST} from '@pages/DataIntegration/SqlCollect/OfflineCollect';
import {SourceTypeMap} from '@pages/DataIntegration/SqlCollect/constants';
import PreCheckModal from '@pages/DataIntegration/components/PreCheckModal';
import {IntegrationTab} from '@pages/DataIntegration/constants';
import StatusTag from '@pages/DataIntegration/components/StatusTag';
import {useRequest} from 'ahooks';
import IconSvg from '@components/IconSvg';
import urls from '@utils/urls';
import {
  CDC_STATUS,
  CDC_TASK_TYPE,
  OperationShowType,
  orderMap,
  EditMode,
  SourceTypeEnum,
  searchOptions,
  SEARCH_TYPE
} from '../contants';
import {transSecond2Time, transStatus2Overview, getOperationList, onEdit, JobItem} from '../utils';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {formatEmpty} from '@utils/utils';
const cx = classNames.bind(styles);

const klass = 'cdc-collect-list';

const CdcCollectList: React.FC = () => {
  const permissionModalRef = useRef<PermissionModalRef>(null);
  const [dataSource, setDataSource] = useState([]);
  const navigate = useNavigate();
  // 权限列表
  const authList = useWorkspaceAuth([Privilege.StructuredIntegrationCreate, Privilege.CatalogMenu]);
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // 搜索条件
  const [searchValues, setSearchValues] = useState({
    keyword: SEARCH_TYPE.NAME,
    value: ''
  });
  // 任务状态筛选
  const [statusFilter, setStatusFilter] = useState([]);
  // 任务类型筛选
  const [taskTypeFilter, setTaskTypeFilter] = useState([]);
  // 源端类型筛选
  const [sourceTypeFilter, setSourceTypeFilter] = useState([]);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({pageNo: 1, pageSize: 10});
  const [sorter, setSorter] = useState<{field: JobOrderBy | null; order: Order | null}>({
    field: null,
    order: null
  });
  // 当前选中的任务
  const [selectedJob, setSelectedJob] = useState(null);
  // 前置检查结果弹窗状态
  const [modalVisible, setModalVisible] = useState(false);

  // 渲染操作列
  const renderOperation = (record: JobItem) => {
    const operations = getOperationList(navigate, {
      status: record.status,
      privileges: record.privileges
    });
    const menu = (
      <Menu>
        {operations[OperationShowType.Dropdown].map((item, index) => (
          <AuthComponents key={index} isAuth={item.isAuth}>
            <Menu.Item
              key={index}
              onClick={() => item.callback([record], workspaceId, getCdcList)}
              disabled={item.disabled}
            >
              {item.label}
            </Menu.Item>
          </AuthComponents>
        ))}
        <AuthMenuItem
          isAuth={record.privileges?.includes(Privilege.Manage)}
          onClick={() =>
            permissionModalRef.current?.open({
              resourceType: ResourceType.StructuredIntegration,
              resourceId: record.jobId,
              resourceName: record.name,
              privilegeList: PRIVILEGE_LIST
            })
          }
        >
          权限管理
        </AuthMenuItem>
      </Menu>
    );
    return (
      <Space className={cx('operation')}>
        {operations[OperationShowType.List].map((item, index) => (
          <AuthComponents key={index} isAuth={item.isAuth}>
            <Button
              type="actiontext"
              key={index}
              onClick={() => item.callback([record], workspaceId, getCdcList, navigate)}
              disabled={item.disabled}
            >
              {item.label}
            </Button>
          </AuthComponents>
        ))}
        <Dropdown overlay={menu}>
          <IconSvg type="more" size={16} color="#6c6d70" />
        </Dropdown>
      </Space>
    );
  };

  // 表格列配置
  const columns: ColumnsType<JobItem> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      width: 220,
      ellipsis: true,
      // 任务名称点击跳转详情页面
      render: (text: string, record: JobItem) => (
        <Link to={`${urls.cdcCollectDetail}?workspaceId=${workspaceId}&jobId=${record.jobId}`}>
          <Ellipsis tooltip={text}>{formatEmpty(text)}</Ellipsis>
        </Link>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 140,
      filterMultiple: false,
      filters: CDC_STATUS.toArray(),
      filteredValue: statusFilter,
      render: (status: CdcJobStatus, record: JobItem) => {
        const hasPrecheck = [
          CdcJobStatus.PreCheck,
          CdcJobStatus.Checkpass,
          CdcJobStatus.Checkfailed
        ].includes(status);
        const statusObj = CDC_STATUS.fromValue(status);
        return (
          <StatusTag
            text={statusObj.text}
            className={statusObj.className}
            onClick={
              hasPrecheck
                ? () => {
                    setSelectedJob(record);
                    setModalVisible(true);
                  }
                : undefined
            }
          />
        );
      }
    },
    {
      title: '源端类型',
      dataIndex: 'sourceType',
      width: 120,
      filterMultiple: false,
      filters: Object.keys(SourceTypeEnum).map((item) => ({
        text: item,
        value: item
      })),
      filteredValue: sourceTypeFilter,
      render: (text) => (
        <div className={cx('source-type')}>
          <IconSvg type={SourceTypeMap[text]?.icon} className="bordered-circle-icon mr-[8px]" size={14} />
          {text}
        </div>
      )
    },
    {
      title: '源端数据源',
      dataIndex: 'sourceConnectionId',
      width: 120,
      ellipsis: true,
      render: (text: string, record: JobItem) => (
        <Link to={`${urls.connectionDetail}?name=${record.sourceConnectionId}&workspaceId=${workspaceId}`}>
          <Ellipsis tooltip={text}>{formatEmpty(text)}</Ellipsis>
        </Link>
      )
    },
    {
      title: '源端数据库',
      dataIndex: 'sourceDatabase',
      width: 120,
      ellipsis: true,
      // 跳转到数据库详情页面
      render: (text: string) => <Ellipsis tooltip={text}>{formatEmpty(text)}</Ellipsis>
    },
    {
      title: '目标端数据库',
      dataIndex: 'sinkPath',
      width: 120,
      // 跳转到数据库详情页面
      render: (text: string) => {
        const [catalog = '', schema = ''] = (text ?? '').split('.');
        return schema ? (
          <Link to={`${urls.metaData}?workspaceId=${workspaceId}&catalog=${catalog}&schema=${schema}`}>
            <Ellipsis tooltip={schema}>{schema}</Ellipsis>
          </Link>
        ) : (
          '-'
        );
      }
    },
    {
      title: '持续运行时长',
      dataIndex: 'runtime',
      width: 120,
      render: (text: number) => transSecond2Time(text)
    }, // 转换为时分秒格式
    // {
    //   title: '数据延迟时间',
    //   dataIndex: 'latency',
    //   width: 100,
    //   // -1 表示延迟未知
    //   render: (text: number) => (text === -1 ? '未知' : `${text}s`)
    // },
    {
      title: '任务类型',
      dataIndex: 'cdcType',
      width: 120,
      render: (text: string) => CDC_TASK_TYPE.fromValue(text).text,
      filterMultiple: false,
      filters: CDC_TASK_TYPE.toArray(),
      filteredValue: taskTypeFilter
    },
    {
      title: '执行概况',
      dataIndex: 'subStatus',
      width: 220,
      render: (text: SubStatus) => transStatus2Overview(text)
    },
    {title: '创建人', dataIndex: 'creatorName', width: 120},
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
      sorter: true,
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {title: '更新人', dataIndex: 'updaterName', width: 120},
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 160,
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 220,
      fixed: 'right',
      render: (_, record: JobItem) => renderOperation(record)
    }
  ];

  // 拉取实时任务列表
  const {loading, run: getCdcList} = useRequest(
    () =>
      getIntegrationJobList(workspaceId, {
        type: JobType.CDC,
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        orderBy: sorter.field,
        order: orderMap[sorter.order],
        jobStatusFilter: statusFilter?.[0],
        cdcTypeFilter: taskTypeFilter?.[0],
        sourceType: sourceTypeFilter?.[0],
        [searchValues.keyword]: searchValues.value
      }),
    {
      onSuccess: (res) => {
        if (res.success) {
          const {jobs, totalCount} = res.result;
          setDataSource(
            jobs.map((item) => ({
              ...item,
              operations: getOperationList(navigate, {
                privileges: item?.privileges || ['MANAGE'],
                status: item.status
              })
            }))
          );
          setTotal(totalCount);
        }
      },
      refreshDeps: [pagination, sorter, searchValues, sourceTypeFilter, statusFilter, taskTypeFilter]
    }
  );

  const handleSearch = (e) => {
    const {type, value} = e;
    setSearchValues({keyword: type, value});
    setPagination((prev) => ({...prev, pageNo: 1}));
  };
  const handleRefresh = () => {
    getCdcList();
  };
  const handleCreate = () => {
    onEdit(EditMode.Create, '', '', navigate);
  };
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination({...pagination, pageNo: pagination.current, pageSize: pagination.pageSize});
    setSorter({field: sorter.field, order: sorter.order});
    setStatusFilter(filters.status || []);
    setTaskTypeFilter(filters.cdcType || []);
    setSourceTypeFilter(filters.sourceType || []);
  };
  return (
    <div className={cx(klass)}>
      <div className={cx(styles['cdc-header'])}>
        <Search
          allowClear
          multipleDefaultValue={SEARCH_TYPE.NAME}
          multipleOption={searchOptions}
          onSearch={handleSearch}
          className={cx(styles['search'])}
        />
        <div className={cx(styles['header-right'])}>
          <Button onClick={handleRefresh} className="mr-[10px]">
            <IconSvg type="refresh" size={16} color="#6c6d70" />
          </Button>
          <AuthButton
            isAuth={authList[Privilege.StructuredIntegrationCreate]}
            tooltipType={TooltipType.Function}
            type="primary"
            onClick={handleCreate}
            className="mr-[10px]"
            icon={<IconSvg type="add" size={16} />}
          >
            创建实时采集
          </AuthButton>
        </div>
      </div>
      <Table
        className={cx(styles['cdc-table'])}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        scroll={{x: 1200}}
        onChange={handleTableChange}
        pagination={{
          current: pagination.pageNo,
          pageSize: pagination.pageSize,
          total: total,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true
        }}
        rowKey="jobId"
      />
      <PermissionModal workspaceId={workspaceId} ref={permissionModalRef} />
      {modalVisible && (
        <PreCheckModal
          jobs={[selectedJob]}
          workspaceId={workspaceId}
          afterClose={() => setModalVisible(false)}
          isPreCheck={false}
          type={IntegrationTab.Realtime}
        />
      )}
    </div>
  );
};

export default CdcCollectList;
