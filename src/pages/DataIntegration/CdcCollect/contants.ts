import {update} from 'immutability-helper';
import Enum from '@helpers/enum';
import {
  CdcJobStatus,
  CdcJobType,
  CdcSubStatus,
  CdcRecordStatus,
  SinkNameRule,
  SinkTableType,
  SourceChangeStrategy,
  DmlDeleteStrategy,
  DirtyDataStrategyEnum,
  DmlUpdateStrategy,
  DmlInsertStrategy
} from '@api/integration/cdc-type';
import {StatusTagType} from '@pages/DataIntegration/components/StatusTag';
// 详情页tab
export enum DetailTab {
  // 运行记录
  ProcessList = 'process-list',
  // 日志
  Log = 'log',
  // 任务配置
  DetailConfig = 'detail-config'
}
// 实时作业操作枚举
export enum CdcOperation {
  // 前置检查
  PreCheck = 'PreCheck',
  // 运行
  Start = 'start',
  // 暂停
  Suspend = 'suspend',
  // 删除
  Delete = 'delete',
  // 编辑
  Edit = 'edit',
  // 复制
  Copy = 'copy'
}
// 实时作业状态枚举
export const CDC_STATUS = new Enum(
  {
    alias: CdcJobStatus.Draft,
    text: '草稿',
    value: CdcJobStatus.Draft,
    className: StatusTagType.STOPPED
  },
  {
    alias: CdcJobStatus.Ready,
    text: '待触发',
    value: CdcJobStatus.Ready,
    className: StatusTagType.SUSPENDING
  },
  {
    alias: CdcJobStatus.Running,
    text: '运行中',
    value: CdcJobStatus.Running,
    className: StatusTagType.RUNNING
  },
  {
    alias: CdcJobStatus.PreCheck,
    text: '前置检查中',
    value: CdcJobStatus.PreCheck,
    className: StatusTagType.RUNNING
  },
  {
    alias: CdcJobStatus.Checkpass,
    text: '前置检查通过',
    value: CdcJobStatus.Checkpass,
    className: StatusTagType.SUCCESS
  },
  {
    alias: CdcJobStatus.Checkfailed,
    text: '前置检查失败',
    value: CdcJobStatus.Checkfailed,
    className: StatusTagType.FAILED
  },
  {
    alias: CdcJobStatus.Suspending,
    text: '暂停中',
    value: CdcJobStatus.Suspending,
    className: StatusTagType.SUSPENDING
  },
  {
    alias: CdcJobStatus.Suspend,
    text: '已暂停',
    value: CdcJobStatus.Suspend,
    className: StatusTagType.STOPPED
  },
  {
    alias: CdcJobStatus.Failed,
    text: '运行失败',
    value: CdcJobStatus.Failed,
    className: StatusTagType.FAILED
  }
);
// 实时作业类型枚举
export const CDC_TASK_TYPE = new Enum(
  {
    alias: CdcJobType.BaseIncrement,
    text: '全量+增量',
    value: CdcJobType.BaseIncrement
  },
  {
    alias: CdcJobType.Increment,
    text: '增量',
    value: CdcJobType.Increment
  }
);
export const CDC_SUBSTATUS = new Enum(
  {
    alias: CdcSubStatus.Queue,
    text: '未开始',
    value: CdcSubStatus.Queue
  },
  {
    alias: CdcSubStatus.Running,
    text: '运行中',
    value: CdcSubStatus.Running
  },
  {
    alias: CdcSubStatus.Failed,
    text: '运行失败',
    value: CdcSubStatus.Failed
  },
  {
    alias: CdcSubStatus.Closed,
    text: '已完成',
    value: CdcSubStatus.Closed
  },
  {
    alias: CdcSubStatus.Suspend,
    text: '已暂停',
    value: CdcSubStatus.Suspend
  },
  {
    alias: CdcSubStatus.Stopped,
    text: '已终止',
    value: CdcSubStatus.Stopped
  }
);
/** 实时 - 任务记录状态枚举 */
export const CDC_RECORD_STATUS = new Enum(
  {
    alias: CdcRecordStatus.Running,
    text: '运行中',
    value: CdcRecordStatus.Running,
    className: StatusTagType.RUNNING
  },
  {
    alias: CdcRecordStatus.Failed,
    text: '失败',
    value: CdcRecordStatus.Failed,
    className: StatusTagType.FAILED
  },
  {
    alias: CdcRecordStatus.Closed,
    text: '成功',
    value: CdcRecordStatus.Closed,
    className: StatusTagType.SUCCESS
  },
  {
    alias: CdcRecordStatus.Stopped,
    text: '已终止',
    value: CdcRecordStatus.Stopped,
    className: StatusTagType.STOPPED
  },
  {
    alias: CdcRecordStatus.Queue,
    text: '未开始',
    value: CdcRecordStatus.Queue,
    className: StatusTagType.STOPPED
  },
  {
    alias: CdcRecordStatus.Suspend,
    text: '已暂停',
    value: CdcRecordStatus.Suspend,
    className: StatusTagType.STOPPED
  }
);

/** 实时 - 源端类型 */
export enum SourceTypeEnum {
  MySQL = 'MySQL',
  Oracle = 'Oracle'
  // SQLServer = 'SQLServer',
  // PostgreSQL = 'PostgreSQL',
  // Hana = 'Hana'
  // KingBaseES = 'KingBase ES',
  // TDEngine = 'TDEngine',
  // influxDB = 'influxDB'
}
// 操作展示形式，列表内、列表内下拉，详情内下拉
export enum OperationShowType {
  List = 'list',
  Dropdown = 'dropdown',
  DetailList = 'detailList',
  DetailDropdown = 'detailDropdown',
  ResultDropdown = 'resultDropdown'
}
export const orderMap = {
  ascend: 'asc',
  descend: 'desc'
};
export enum EditMode {
  Create = 'create',
  Edit = 'edit'
}
export enum SEARCH_TYPE {
  NAME = 'namePattern',
  SOURCE = 'sourceConnectionIdFilter',
  SOURCE_DB = 'sourceDataBaseFilter',
  DEST_DB = 'sinkPathPattern'
}
// 搜索类型
export const searchOptions = [
  {
    label: '任务名称',
    value: SEARCH_TYPE.NAME
  },
  {
    label: '源端数据源',
    value: SEARCH_TYPE.SOURCE
  },
  {
    label: '源端数据库',
    value: SEARCH_TYPE.SOURCE_DB
  },
  {
    label: '目的端库',
    value: SEARCH_TYPE.DEST_DB
  }
];
export const sinkNameRuleMap = {
  [SinkNameRule.Same]: '和源端名称保持一致',
  [SinkNameRule.AddPrefix]: '增加前缀',
  [SinkNameRule.AddSuffix]: '增加后缀',
  [SinkNameRule.AddPrefixAndSuffix]: '增加前缀和后缀'
};
export const sinkNameMap = {
  [SinkTableType.Managed]: '内部表'
  // [SinkTableType.External]: '外部表'
};
// 编辑类型
export enum CreateMode {
  Create = 'create',
  Update = 'update'
}
// 策略文本映射相关
export function getSourceStrategyText(strategy: SourceChangeStrategy, type?: string) {
  const SyncMap = {
    onAddColumn: '目的端自动新增字段并同步数据',
    onRenameColumn: '同步重命名字段类型',
    onChangeColumn: '同步修改字段类型',
    onChangeTableComment: '同步修改表描述',
    onChangeColumnComment: '同步修改字段描述'
  };
  switch (strategy) {
    case SourceChangeStrategy.Skip:
      return '忽略';
    case SourceChangeStrategy.Pause:
      return '终止任务';
    case SourceChangeStrategy.Sync:
      return SyncMap[type];
  }
}
export const dmlStrategyMap = {
  [DmlDeleteStrategy.Delete]: '正常处理',
  [DmlDeleteStrategy.Skip]: '忽略',
  [DmlDeleteStrategy.LogicalDelete]: '逻辑删除',
  [DmlDeleteStrategy.Ignore]: '忽略该操作',
  [DmlUpdateStrategy.Update]: '按 UPDATE 同步',
  [DmlInsertStrategy.Insert]: '按 INSERT 同步'
};
export const dirtyDataStrategyMap = {
  [DirtyDataStrategyEnum.Strict]: '不容忍',
  [DirtyDataStrategyEnum.Tolerant]: '容忍部分',
  [DirtyDataStrategyEnum.Ignore]: '忽略'
};

export const TreeSplit = '-->-->';
