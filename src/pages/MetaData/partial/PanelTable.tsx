/**
 * Table 面板
 * 注意：
 * 1、catalog 为 CatalogType.SYSTEM 和 CatalogType.EDAP_DATALAKE 类型下的 Table 不支持编辑/删除
 * 2、catalog 为 CatalogType.EDAP_DATALAKE 类型下只有 Table
 * <AUTHOR>
 */
import {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {Button, Search, Table} from 'acud';
import {useAsyncEffect, useRequest} from 'ahooks';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import RefreshButton from '@components/RefreshButton';
import IconSvg from '@components/IconSvg';
import DdlTabView from '@components/DdlTabView';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';
import {RULE} from '@utils/regs';
import {MetaCnNameMap, ruleMapByCatalog} from '../config';
import {CatalogType, ETableDoris} from '@api/metaRequest';
import PermissionManage from '../../../components/PermissionManage';
import {Privilege, ResourceType} from '@api/permission/type';
import {isBuildInCatalog} from '../helper';
import {edapIframeSrc} from '@components/IframePreloader';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import {useNavigate} from 'react-router-dom';
import {IcebergFieldTypes, DorisFieldTypes, DorisTableFieldTypeMap, DorisTableTypeMap} from '@api/meta/table';
import {detailDataFormate} from '../MetaTableCreate/utils';
import JsonView from '@uiw/react-json-view';
import {OutlinedEdit} from 'acud-icon';
import {TooltipConfig, TooltipType} from '@/components/AuthComponents/constants';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2',
  PERMISSION = '3',
  DDL = '4'
}

const propertiesShowFormat = (data = []) => {
  const newProps = data.map((item, index) => <div key={index}>{`${item.name}: ${item.value}`}</div>);
  return <div>{newProps || '-'}</div>;
};

const PanelTable = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, userList} = props;
  const {catalog = '', schema = '', node = '', tab = PanelEnum.OVERVIEW} = urlState;

  const navigate = useNavigate();

  // catalog 类型
  const [catalogType, setCatalogType] = useState<CatalogType>(CatalogType.SYSTEM);

  const dropdownMenu = useMemo(
    () => [
      {
        key: 'rename',
        label: `重命名${MetaCnNameMap['Table']}`,
        authName: Privilege.Manage
      },
      {key: 'remove', label: `删除${MetaCnNameMap['Table']}`, authName: Privilege.Manage}
    ],
    []
  );

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // table 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.ITableDetailRes>();

  const initialPanes = useMemo(
    () => [
      {tab: '概览', key: PanelEnum.OVERVIEW},
      {tab: '详情', key: PanelEnum.DETAIL},
      ...(isBuildInCatalog(catalog as CatalogType)
        ? []
        : [{tab: '权限管理', key: PanelEnum.PERMISSION, privilege: [Privilege.Manage]}]),
      ...(catalogType === CatalogType.DORIS || catalogType === CatalogType.DATALAKE
        ? [{tab: 'DDL', key: PanelEnum.DDL}]
        : [])
    ],
    [catalogType]
  );

  // 详情字段类型配置
  const tableInfoFieldsMap = useMemo(
    () => ({
      [CatalogType.EDAP_DATALAKE]: [
        'catalogName',
        'schemaName',
        'tableName',
        'tableType',
        'dataSourceFormat',
        'storageLocation',
        'createdAt',
        'createdBy'
      ],
      [CatalogType.DORIS]: [
        'catalogName',
        'schemaName',
        'tableName',
        'tableType',
        'dataSourceFormat',
        'tableModel',
        'properties',
        'createdAt'
      ],
      [CatalogType.DATALAKE]: [
        'catalogName',
        'schemaName',
        'tableName',
        'tableType',
        'dataSourceFormat',
        'storageLocation',
        'properties',
        'createdAt',
        'createdBy',
        'updatedAt',
        'updatedBy'
      ]
      // 其他类型可按需补充
    }),
    []
  );

  // 详情字段转化为表格形式
  const detailInfoFormat = useMemo(() => {
    return detailDataFormate(dataInfo);
  }, [dataInfo]);

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    // 获取要展示的字段 list，如找不到默认使用 DATALAKE 类型
    const fields = tableInfoFieldsMap[catalogType] || tableInfoFieldsMap[CatalogType.DATALAKE];
    return fields
      .map((key) => {
        switch (key) {
          case 'catalogName':
            return {
              label: `${MetaCnNameMap['Catalog']}名称`,
              value: info.catalogName
            };
          case 'schemaName':
            return {
              label: `${MetaCnNameMap['Schema']}名称`,
              value: info.schemaName
            };
          case 'tableName':
            return {
              label: `${MetaCnNameMap['Table']}名称`,
              value: info.name
            };
          case 'tableType':
            return {
              label: '表类型',
              value: info.tableType
            };
          case 'dataSourceFormat':
            return {
              label: '数据源格式',
              value: info.dataSourceFormat === 'DORIS' ? 'DORIS 内表' : info.dataSourceFormat
            };
          case 'storageLocation':
            return {
              label: '存储路径',
              value: info.storageLocation
            };
          case 'createdAt':
            return {
              label: '创建时间',
              value: info.createdAt
            };
          case 'createdBy':
            return {
              label: '创建人',
              value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
            };
          case 'updatedAt':
            return {
              label: '修改时间',
              value: info.updatedAt
            };
          case 'updatedBy':
            return {
              label: '最近修改人',
              value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
            };
          case 'tableModel':
            return {
              label: '表模型',
              value: DorisTableTypeMap[info.tableModel] || '-'
            };
          case 'properties':
            return {
              label: '表属性',
              value: propertiesShowFormat(detailInfoFormat.properties)
            };
          default:
            return null;
        }
      })
      .filter(Boolean);
  }, [catalogType, dataInfo, userList, MetaCnNameMap, tableInfoFieldsMap, detailInfoFormat]);

  // 获取详情
  const {loading, run: getTableDetail} = useRequest(
    async () => {
      // 获取 catalog 类型，根据类型获取详情展示形式，system 不需要请求
      if (catalog !== CatalogType.SYSTEM) {
        const catalogDetail = await http.getCatalogDetail(workspaceId, catalog);
        setCatalogType(catalogDetail?.result?.catalog?.type);
      }
      const res = await http.getTableDetail(workspaceId, fullName);
      setDataInfo(res.result);
    },
    {
      manual: true
    }
  );

  // 字段信息 表格的 Datasource
  const [filterColumnVal, setFilterColumnVal] = useState('');
  const tableData = useMemo(() => {
    const data = dataInfo?.columns || [];
    return data.filter((item) => ~item.name.indexOf(filterColumnVal));
  }, [dataInfo, filterColumnVal]);

  // 初始化
  useEffect(() => {
    catalog && schema && node && getTableDetail();
  }, [catalog, schema, node]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchTable(workspaceId, fullName, {comment: text});
      getTableDetail();
    },
    [dataInfo, setDataInfo]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, node: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, node: '', type: ''}), true);
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: node,
          title: 'Table',
          requestFun: http.deleteTable,
          successFun: removeSuccessFun,
          workspaceId: workspaceId
        });
      }
    },
    [node, workspaceId, catalog, schema, removeSuccessFun]
  );

  // 编辑
  const onEditClick = useCallback(() => {
    navigate(
      `${urls.metaCreateTable}?workspaceId=${workspaceId}&catalog=${catalog}&schema=${schema}&node=${node}`
    );
  }, [workspaceId, catalog, schema, node]);

  const renameNameRules = useMemo(() => {
    const ruleInfo = ruleMapByCatalog[catalogType];
    return [
      {
        validator: async (_, value) => {
          // 校验特殊字符和长度限制
          if (!ruleInfo?.rule.test(value)) {
            return Promise.reject(new Error(ruleInfo.text));
          }
          // 异步校验Volume名称是否重复，复用查询接口 silent模式
          const res = await http.getTableDetail(workspaceId, `${catalog}.${schema}.${value}`, true);
          if (res.success && res.result?.id) {
            return Promise.reject(new Error(`该${MetaCnNameMap['Table']}名称已存在，请重新输入`));
          }
          return Promise.resolve();
        }
      }
    ];
  }, [catalog, catalogType, schema, workspaceId]);

  // 字段信息 表格的 Columns,为拓展字段使用
  const fieldsTypeArr: any[] = useMemo(() => {
    if (catalogType === CatalogType.DORIS) {
      return [DorisFieldTypes.ARRAY, DorisFieldTypes.STRUCT, DorisFieldTypes.MAP];
    } else if (catalogType === CatalogType.DATALAKE) {
      return [IcebergFieldTypes.LIST, IcebergFieldTypes.STRUCT, IcebergFieldTypes.MAP];
    }
  }, [catalogType]);

  // 分桶数据字段
  const bucketInfoList = useMemo(() => {
    const info: any = dataInfo || {};
    const listRes = [
      {
        label: '分桶方式',
        value: info?.distribution?.type || '-'
      },
      {
        label: '分桶数量',
        value: info?.distribution?.number || '-'
      }
    ];
    if (info?.distribution?.type === 'HASH') {
      listRes.splice(1, 0, {
        label: '分桶列',
        value: (info?.distribution?.columnNames).toString() || '-'
      });
    }
    return listRes;
  }, [dataInfo]);

  const descLimitLength = useMemo(() => {
    if (catalogType === CatalogType.DATALAKE || catalogType === CatalogType.DORIS) return 300;
    return 150;
  }, [catalogType]);

  const renderTab = useMemo(() => {
    const partitionColumns = [
      {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        render: (v) => v + 1 || '-'
      },
      {
        title: '字段名称',
        dataIndex: 'columnName',
        key: 'columnName'
      },
      ...(catalogType === CatalogType.DATALAKE
        ? [
            {
              title: '转换函数',
              dataIndex: 'function',
              key: 'function',
              render: (text, record) => {
                const functionArgument = record.functionArgument ? `(${record.functionArgument})` : '';
                return `${text}${functionArgument}`;
              }
            }
          ]
        : [])
    ];

    const columns = [
      {
        title: '序号',
        dataIndex: 'position',
        key: 'position',
        width: 100,
        render: (index) => index + 1 || '-'
      },
      {
        title: '字段名称',
        dataIndex: 'name',
        ellipsis: true,
        key: 'name'
      },
      {
        title: '字段类型',
        dataIndex: 'typeName',
        key: 'typeName',
        width: 200,
        render: (v, record) => {
          switch (v) {
            case DorisTableFieldTypeMap.DECIMAL:
              return `${v}(${record?.typePrecision},${record?.typeScale})`;
            case IcebergFieldTypes.FIXED:
            case DorisTableFieldTypeMap.CHAR:
            case DorisTableFieldTypeMap.VARCHAR:
              return `${v}(${record?.typeLength})`;
            case DorisTableFieldTypeMap.DATETIME:
              return `${v}(${record?.typeScale})`;
            default:
              return v || '-';
          }
        }
      },
      ...(catalogType === CatalogType.DATALAKE
        ? [
            {
              title: '非空',
              dataIndex: 'nullable',
              key: 'nullable',
              width: 100,
              render: (text) => (text ? '是' : '否')
            }
          ]
        : []),
      ...(catalogType === CatalogType.DORIS
        ? [
            {
              title: '非空',
              dataIndex: 'nullable',
              key: 'nullable',
              width: 100,
              render: (text) => (text ? '是' : '否')
            },
            {
              title: '主键',
              dataIndex: 'keyIndex',
              key: 'keyIndex',
              width: 100,
              render: (keyIndex) => (keyIndex !== undefined ? '是' : '否')
            },
            ...(dataInfo?.tableModel === ETableDoris.AGGREGATE
              ? [
                  {
                    title: '聚合函数',
                    dataIndex: 'aggregateType',
                    key: 'aggregateType',
                    width: 200,
                    render: (text) => text || '-'
                  }
                ]
              : []),
            {
              title: '默认值',
              dataIndex: 'defaultValue',
              key: 'defaultValue',
              width: 100,
              render: (text) => text || '-'
            }
          ]
        : []),
      {
        title: '字段描述',
        dataIndex: 'comment',
        key: 'comment',
        ellipsis: true,
        render: (text) => text || '-'
      }
    ];

    const config = {
      [PanelEnum.DETAIL]: <InfoPanel infoList={infoList} title="基本信息" />,
      [PanelEnum.OVERVIEW]: (
        <div>
          <DescriptionEdit
            text={dataInfo?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={!isBuildInCatalog(catalog as CatalogType)} // 内置的 catalog 不允许编辑
            authList={dataInfo?.privileges || []}
            limitLength={descLimitLength}
          />
          <div>
            <h2 className="title-head">字段信息</h2>
            <div className="data-search">
              <Search
                className="table-overview-search"
                onChange={(e) => setFilterColumnVal(e.target.value)}
                allowClear
                placeholder="请输入字段名称查询"
              />
              <RefreshButton onClick={getTableDetail} />
            </div>
            <Table
              columns={columns}
              rowKey="position"
              expandable={{
                expandedRowRender: (record: any) => {
                  const typeJson = record.typeJson;
                  if (!typeJson) return <div>无数据</div>;
                  try {
                    const content = JSON.parse(typeJson);
                    return <JsonView displayDataTypes={false} value={content} />;
                  } catch (e) {
                    return <div>数据格式错误</div>;
                  }
                },
                rowExpandable: (record) => fieldsTypeArr.includes(record.typeName)
              }}
              dataSource={tableData}
              loading={loading}
            />
          </div>

          <div>
            <h2 className="title-head">分区信息</h2>
            <Table
              columns={partitionColumns}
              dataSource={detailInfoFormat.partitioning}
              loading={loading}
              pagination={false}
            />
          </div>

          {catalogType === CatalogType.DORIS && (
            <div style={{marginTop: 30}}>
              <InfoPanel infoList={bucketInfoList} title="分桶信息" />
            </div>
          )}
        </div>
      ),
      [PanelEnum.PERMISSION]: (
        <PermissionManage
          resourceType={ResourceType.Table}
          resourceId={fullName}
          hasInheritedFrom
          name={node}
          onSuccess={getTableDetail}
        />
      ),
      [PanelEnum.DDL]: <DdlTabView title="DDL语句" ddlSql={dataInfo?.ddl || ''} />
    };
    return config[tab];
  }, [
    catalogType,
    catalog,
    dataInfo?.tableModel,
    dataInfo?.comment,
    dataInfo?.privileges,
    fullName,
    infoList,
    loading,
    node,
    onChangeDescript,
    tab,
    tableData,
    getTableDetail,
    bucketInfoList
  ]);

  const renameLimitLength = useMemo(() => {
    if (catalogType === CatalogType.DATALAKE || catalogType === CatalogType.DORIS) return 128;
    return 64;
  }, [catalogType]);

  return (
    <div className="work-meta-table-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={<IconSvg type="meta-table" size={20} color="#fff" />}
        title={node as string}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        authList={dataInfo?.privileges || []}
        createText={`编辑${MetaCnNameMap['Table']}`}
        onCreateClick={onEditClick}
        createDisabled={catalogType === CatalogType.DORIS}
        createDisabledTooltip={catalogType === CatalogType.DORIS ? TooltipConfig[TooltipType.NotSupport] : ''}
        createIcon={<OutlinedEdit width={16} height={16} />}
        hiddenCreate={catalogType === CatalogType.EDAP_DATALAKE}
        createAuthName={Privilege.Manage}
        otherButton={
          catalogType === CatalogType.EDAP_DATALAKE ? (
            <Button
              onClick={() =>
                window.open(
                  `${edapIframeSrc}#/meta-data/manage?type=EDAP&topic=DataLake&database=${schema}&table=${node}`,
                  '_blank'
                )
              }
            >
              去管理
            </Button>
          ) : null
        }
      />
      {/* Tabs */}
      <MetaTabs
        panesList={initialPanes}
        tab={tab}
        onTabChange={onTabChange}
        authList={dataInfo?.privileges}
      />
      {renderTab}
      {/** 重命名 Table 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={node}
        title="Table"
        requestFun={http.patchTable}
        successFun={renameSuccessFun}
        nameRules={renameNameRules}
        limitLength={renameLimitLength}
      />
    </div>
  );
};
export default PanelTable;
