.ddl_modle_container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  .ddl_modle_header {
    display: flex;
    justify-content: space-between;
    height: 40px;
    border-bottom: 1px solid #E8E9EB;
    padding: 0 19px;

    .ddl_modle_header_desc {
      display: flex;
      align-items: center;

      .header_desc_title {
        color: #151B26;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        margin-right: 8px;
      }
    }

    .ddl_modle_header_control {
      display: flex;
      align-items: center;

      .header_control_item {
        padding: 2px 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        cursor: pointer;
        border: 0.5px solid #E1E6F0;
        background-color: #F8FAFC;
        border-radius: 4px;

        font-weight: 400;
        font-style: Regular;
        font-size: 12px;
        line-height: 20px;

        &:active,
        &:hover {
          border: 1px solid #2468F2;
          background-color: #E6F0FF;
          color: #2468F2;

          font-weight: 500;
          font-style: Medium;
        }
      }
    }
  }

  .ddl_modle_content {
    flex: 1;
    position: relative;
    display: flex;
    padding-top: 16px;

    .content_row_nums {
      width: 56px;
      display: flex;
      flex-direction: column;

      .row_num {
        text-align: center;
        height: 22px;
      }
    }

    .content_row_content {
      margin-left: 15px;

      .row_content {
        height: 22px;
        display: flex;
        align-items: center;
      }
    }

    .content_split_line {
      position: absolute;
      height: 100%;
      left: 56px;
      top: 0;
      border-left: 1px solid #E8E9EB;
    }
  }

  .drawer_container {
    display: flex;
    flex-direction: column;
    position: absolute;

    .draw_ddl_content {
      position: relative;
      flex: 1;
    }

    border: 1px solid #E8E9EB;
  }

  :global {
    button.acud-drawer-close {
      top: -8px
    }
  }
}