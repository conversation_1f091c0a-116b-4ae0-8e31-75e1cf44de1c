/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-07-11 17:04:52
 * @LastEditTime: 2025-08-21 14:24:48
 */
import React, {useCallback, useContext, useMemo, useState, useRef, useEffect} from 'react';
import {useNavigate} from 'react-router-dom';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import useUrlState from '@ahooksjs/use-url-state';
import {Alert, Space, Button, Form, toast, Loading} from 'acud';
import {submitDataFormate} from './utils';

import ResizableGrid, {ResizableGridRefHandle} from './ResizableGrid';
import MetaIcebergTable from './MetaTable/IcebergTable';
import MetaDorisTable from './MetaTable/DorisTable';
import DdlModule from './DdlModule';
import styles from './index.module.less';

import {
  TableSource,
  CatalogType,
  ITableParamsNew,
  createTable,
  patchTable,
  tabelSchemaToDDL,
  ddlToTableSchema,
  IDDLToSchemaParams
} from '@api/metaRequest';
import * as http from '@api/metaRequest';
import flags from '@/flags';
import {debounce} from 'lodash';
import {ResultStatus} from './components/StatusIcon';
import {detailDataFormate} from './utils';
import {TableHanleModel} from '@api/meta/table';

interface MetaTaleCreateProps {}
const kclass = 'meta_table_create_container';

const isPrivate = flags.DatabuilderPrivateSwitch;

enum sliderPostion {
  left = 1,
  right = 2
}

enum fromHanle {
  TODDL = 'toDDL',
  TOCONFIRM = 'toConfirm'
}
// 根据 node 值，获取基础信息
const baseInfo = (node: string) => {
  const tableHanleWay = node ? TableHanleModel.EDIT : TableHanleModel.CREATE;
  return {
    tableHanleWay: tableHanleWay, // 创建 或 编辑 (元信息)
    isCreate: tableHanleWay === TableHanleModel.CREATE, // 是否为创建
    title: node ? `编辑${node}` : '创建数据表',
    alertContIce: node
      ? '当前表编辑仅支持以下内容，更多高级设置如修改分区请前往工作区创建NoteBook并编写SQL'
      : '请在左侧可视化区域定义表结构，系统将自动生成右侧DDL语句以供查看。DDL语句修改不会同步回左侧，且在左侧变更后会被覆盖。最终建表以左侧可视化配置为准。',
    alertContDoris: node
      ? '当前表编辑仅支持以下内容，更多高级设置如修改分区请前往工作区创建NoteBook并编写SQL'
      : '可视化配置与DDL编辑支持双向同步，建议在可视化配置完成基础表结构定义后在DDL中补充高级语句。可视化配置变更将自动更新并覆盖DDL，最终建表以DDL语句为准。'
  };
};

const MetaTable: React.FC<MetaTaleCreateProps> = () => {
  // catalog 类型
  const [catalogType, setCatalogType] = useState<CatalogType>(CatalogType.SYSTEM);
  // 是否是数据湖类型
  const isDataLake = useMemo(() => catalogType === CatalogType.DATALAKE, [catalogType]);

  const [loading, setLoading] = useState<boolean>(false);

  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);
  const resizableGridRefHandle = useRef<ResizableGridRefHandle>(null);

  const [form] = Form.useForm();

  // 使用replace 防止破坏history
  const [{catalog, schema, node}] = useUrlState(undefined, {
    navigateMode: 'replace'
  });
  // table 全名
  const fullName = `${catalog}.${schema}.${node}`;
  // 根据 node 值，整体组件所需信息
  const baseInfoData = useMemo(() => baseInfo(node), [node]);

  const editTableName = Form.useWatch('name', form);

  // 跳转地址
  const goToPath = useCallback(
    (control = false) => {
      const url = `${urls.metaData}?workspaceId=${workspaceId}&catalog=${catalog}&schema=${schema}`;
      let editUrl = `&type=table`;
      if (control) {
        // 返回 & 取消的操作，表名称不变化
        editUrl += `&node=${node}`;
        navigate(`${url}${node ? editUrl : ''}`);
        return;
      }
      editUrl += `&node=${editTableName}`;
      navigate(`${url}${editUrl}`);
    },
    [workspaceId, catalog, schema, node, editTableName, navigate]
  );

  const [transStatus, setTransStatus] = useState(ResultStatus.DEFAULT);
  const [transErrMsg, setTransErrMsg] = useState('转换失败，请检查内容格式');

  const [ddlSql, setDdlSql] = useState('');

  const handleSlider = useCallback(
    (position) => {
      const {handleLeftSlider, handleRightSilder} = resizableGridRefHandle.current;
      position === sliderPostion.left ? handleLeftSlider?.(true) : handleRightSilder?.(true);
    },
    [resizableGridRefHandle.current]
  );

  // 表单 「确认按钮」是否禁用
  const [isDisableConfim, setDisableConfim] = useState(!node);

  // 表单转ddl
  const formToDDL = useCallback(
    debounce((val) => {
      setDisableConfim(false);
      setTransStatus(ResultStatus.LOADING);
      const params: ITableParamsNew = {
        ...val,
        ddl: ddlSql,
        catalogName: catalog,
        schemaName: schema
      };
      tabelSchemaToDDL(workspaceId, params)
        .then((res: any) => {
          if (res?.success) {
            if (res?.result?.success) {
              setTransStatus(ResultStatus.SUCCESS);
              setDdlSql(res?.result?.ddl || '');
              return;
            }
            setTransErrMsg(res?.result?.msg || '');
          } else {
            setTransErrMsg(res?.message?.global || '');
          }

          setTransStatus(ResultStatus.ERROR);
          setDisableConfim(true);
        })
        .catch((e) => {
          setTransStatus(ResultStatus.ERROR);
          setDisableConfim(true);
        });
    }, 500),
    [ddlSql]
  );

  // ddl 转表单
  const ddlToForm = debounce((val) => {
    if (isDataLake) {
      toast.error({
        message: 'Datalake 暂不支持 DDL 转表单功能～',
        duration: 5
      });
      return;
    }
    setDisableConfim(false);
    setTransStatus(ResultStatus.LOADING);
    const params: IDDLToSchemaParams = {
      catalogName: catalog,
      schemaName: schema,
      dataSourceFormat: isDataLake ? TableSource.ICEBERG : TableSource.DORIS,
      ddl: val
    };
    ddlToTableSchema(workspaceId, params)
      .then((res: any) => {
        if (res?.success) {
          if (res?.result?.success) {
            setTransStatus(ResultStatus.SUCCESS);
            setDdlSql(val);
            // 填充表单
            const newData = detailDataFormate(res?.result?.table);
            form.setFieldsValue(newData);
            form
              .validateFields()
              .then(() => {
                setDisableConfim(false);
                setTransStatus(ResultStatus.SUCCESS);
              })
              .catch((e) => {
                if (e?.errorFields?.length) {
                  console.error('ddlToTableSchema表单校验失败', e);
                  setDisableConfim(true);
                  setTransStatus(ResultStatus.ERROR);
                }
              });
            return;
          }

          setTransErrMsg(res?.result?.msg || '');
        } else {
          setTransErrMsg(res?.message?.global || '');
        }
        setDisableConfim(true);
        setTransStatus(ResultStatus.ERROR);
      })
      .catch((e) => {
        setDisableConfim(true);
        setTransStatus(ResultStatus.ERROR);
      });
  }, 500);

  // 表单操作
  const formHandleConfirm = useCallback(
    (tag: any) => {
      // 表单校验
      form
        .validateFields()
        .then((values) => {
          const submitValues = submitDataFormate(values);
          setDisableConfim(false);
          if (tag === fromHanle.TOCONFIRM) {
            setDisableConfim(true);
            const storageLocationParam = isPrivate
              ? `hdfs://${submitValues.storageLocation}`
              : `bos://${submitValues.storageLocation}`;
            const params: ITableParamsNew = {
              ...submitValues,
              ddl: ddlSql,
              catalogName: catalog,
              schemaName: schema,
              ...(submitValues.storageLocation ? {storageLocation: storageLocationParam} : {})
            };
            setLoading(true);
            if (baseInfoData.isCreate) {
              // 新建table
              createTable(workspaceId, params)
                .then((res) => {
                  if (res?.success) {
                    toast.success({
                      message: '新建成功',
                      duration: 5
                    });
                    goToPath();
                  }
                })
                .finally(() => {
                  setDisableConfim(false);
                  setLoading(false);
                });
              return;
            } else if (!baseInfoData.isCreate) {
              // 编辑 table
              patchTable(workspaceId, fullName, params)
                .then((res) => {
                  if (res?.success) {
                    toast.success({
                      message: '编辑数据表成功',
                      duration: 5
                    });
                    goToPath();
                  }
                })
                .finally(() => {
                  setDisableConfim(false);
                  setLoading(false);
                });
              return;
            }
            setDisableConfim(false);
            setLoading(false);
          } else if (tag === fromHanle.TODDL) {
            baseInfoData.isCreate && formToDDL(submitValues);
          }
        })
        .catch((e) => {
          // FIXME: 在未有表单项校验失败的情况下也会， validateFields 也会触发 catch， 故暂时这样处理
          if (e?.errorFields?.length) {
            console.error('表单校验失败', e);
            setDisableConfim(true);
            setTransStatus(ResultStatus.ERROR);
          }
        });
    },
    [form, workspaceId, catalog, schema, ddlSql, goToPath, submitDataFormate, formToDDL]
  );

  useEffect(() => {
    form.setFieldsValue({
      dataSourceFormat: isDataLake ? TableSource.ICEBERG : TableSource.DORIS
    });
  }, [form, isDataLake]);

  const getCataLogDetail = async () => {
    setLoading(true);
    try {
      // 获取 catalog 类型，根据类型获取详情展示形式，system 不需要请求
      if (catalog !== CatalogType.SYSTEM) {
        const catalogDetail = await http.getCatalogDetail(workspaceId, catalog);
        setCatalogType(catalogDetail?.result?.catalog?.type);
      }
    } catch {
      console.error('获取 catalog 详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 左侧内容
  const leftContentRender = useMemo(() => {
    // 判断使用 iceberg 还是 doris
    const WhichComponent = isDataLake ? MetaIcebergTable : MetaDorisTable;
    return (
      <WhichComponent
        form={form}
        editSyncDDL={(ddl) => {
          setDdlSql(ddl);
        }}
        status={transStatus}
        errMsg={transErrMsg}
        tableHanleWay={baseInfoData.tableHanleWay}
        onChange={() => formHandleConfirm(fromHanle.TODDL)}
        onSilderHandle={() => handleSlider(sliderPostion.left)}
      />
    );
  }, [isDataLake, form, transStatus, transErrMsg, baseInfoData, formHandleConfirm, handleSlider]);

  useEffect(() => {
    getCataLogDetail();
  }, []);

  return (
    <div className={styles[kclass]}>
      {loading && <Loading></Loading>}
      <div className={styles['create-content-container']}>
        {/* 头部 */}
        <div className={styles['create-content-title']}>
          <div className={styles['create_title_container']}>
            <IconSvg
              type="left"
              size={20}
              color="#5C5F66"
              onClick={() => {
                goToPath(true);
              }}
            />
            <span className={styles['title_content']}>{baseInfoData.title}</span>
          </div>

          {baseInfoData.isCreate && (
            <div className={styles['create_title_alert']}>
              <Alert
                icon={<IconSvg type="warning" size={18} />}
                message={isDataLake ? baseInfoData.alertContIce : baseInfoData.alertContDoris}
                type="info"
                showIcon
              />
            </div>
          )}
        </div>

        <Form
          style={{flex: 1, display: 'flex', flexDirection: 'column'}}
          labelAlign="left"
          colon={false}
          labelWidth={80}
          form={form}
          preserve={false}
          inputMaxWidth={500}
        >
          {/* 内容区分： 新建 or 编辑 */}
          {baseInfoData.isCreate ? (
            <div className={styles['create_content_info']}>
              <ResizableGrid status={transStatus} errMsg={transErrMsg} ref={resizableGridRefHandle}>
                {/* 左侧内容配置 */}
                {leftContentRender}
                {/* 右侧内容配置 */}
                <DdlModule
                  ddlSql={ddlSql}
                  status={transStatus}
                  errMsg={transErrMsg}
                  onChange={(val: string) => ddlToForm(val)}
                  onSilderHandle={() => handleSlider(sliderPostion.right)}
                  tableSource={isDataLake ? TableSource.ICEBERG : TableSource.DORIS}
                />
              </ResizableGrid>
            </div>
          ) : (
            <div className={styles['edit_content_info']}>{leftContentRender}</div>
          )}

          {/* 尾部 */}
          <div className={styles['create-content-tail']}>
            <Space>
              <Button
                disabled={isDisableConfim}
                type="primary"
                onClick={() => formHandleConfirm(fromHanle.TOCONFIRM)}
              >
                确定
              </Button>
              <Button
                onClick={() => {
                  goToPath(true);
                }}
              >
                取消
              </Button>
            </Space>
          </div>
        </Form>
      </div>
    </div>
  );
};
export default MetaTable;
