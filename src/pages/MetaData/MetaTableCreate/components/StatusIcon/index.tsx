/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-07-16 10:41:54
 * @LastEditTime: 2025-08-12 10:37:03
 */
import {MultiToneSuccess, OutlinedButtonRefresh} from 'acud-icon';
import IconSvg from '@components/IconSvg';
import {Tooltip} from 'acud';

export enum ResultStatus {
  DEFAULT = 'default',
  SUCCESS = 'success',
  ERROR = 'error',
  LOADING = 'loading'
}

const StatusIcon = ({status, errMsg = ''}) => {
  switch (status) {
    case ResultStatus.SUCCESS:
      return <MultiToneSuccess width={18} />;
    case ResultStatus.ERROR:
      return (
        <Tooltip title={errMsg}>
          <IconSvg type="warning" size={18} color="#FF9326" />
        </Tooltip>
      );
    case ResultStatus.LOADING:
      return <OutlinedButtonRefresh width={18} style={{color: '#2468F2'}} />;
    default:
      return null;
  }
};

export default StatusIcon;
