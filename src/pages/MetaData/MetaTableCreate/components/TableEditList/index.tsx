/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-07-17 11:40:32
 * @LastEditTime: 2025-07-30 19:06:47
 */
import {Button, Form, Table} from 'acud';
import {FormInstance} from 'acud/lib/form';
import React from 'react';
import './index.less';
import {OutlinedPlusNew} from 'acud-icon';

export interface IColumn {
  title: string;
  dataIndex: string;
  width?: number;
  valuePropName?: string;
  rules?: any[];
  renderComponent?: React.ReactNode;
  render?: (text: string, record: any, index: number) => React.ReactNode;
}
const TableEditList: React.FC<{
  showIndex?: boolean;
  showControl?: boolean;
  isReadOnly?: boolean;
  name: string;
  form?: FormInstance;
  columnList: IColumn[];
  defaultValue?: any;
  addButtonText?: string;
  initRowData?: boolean;
  onRemove?: (index: number) => void;
  removeTag?: (index: number) => boolean;
}> = ({
  showIndex = false,
  showControl = true,
  initRowData = false,
  name,
  isReadOnly = true,
  form,
  columnList,
  defaultValue = {},
  addButtonText = '添加字段',
  onRemove,
  removeTag
}) => {
  return (
    <div className="table-edit-list">
      <Form.List name={name}>
        {(fields, {add, remove}) => {
          const columns = [
            ...columnList.map((item) => {
              return {
                title: item.title,
                dataIndex: item.dataIndex,
                width: item.width ? item.width : 100,
                render: item?.render
                  ? item.render
                  : (_text, record, index) => (
                      <Form.Item
                        name={[index, item.dataIndex]}
                        rules={item.rules}
                        fieldKey={[index, item.dataIndex]}
                        valuePropName={item.valuePropName ? item.valuePropName : 'value'}
                      >
                        {item.renderComponent}
                      </Form.Item>
                    )
              };
            })
          ];

          showControl &&
            columns.push({
              title: '操作',
              width: 100,
              dataIndex: 'operation',
              render: (text, record, index) => {
                const editTag = removeTag?.(index) || false;
                const initFirstTag = initRowData && fields.length === 1;
                return (
                  <Button
                    disabled={initFirstTag || editTag}
                    className="delete-button"
                    type="actiontext"
                    onClick={() => {
                      remove(index);
                      onRemove && onRemove(index);
                    }}
                  >
                    移除
                  </Button>
                );
              }
            });

          showIndex &&
            columns.unshift({
              title: '序号',
              dataIndex: 'index',
              width: 50,
              render: (_, record, index) => <span>{index + 1}</span>
            });

          return (
            <>
              <Table dataSource={fields} columns={columns} pagination={false} rowKey="id" />
              {!isReadOnly && (
                <Button
                  className="add-button"
                  type="actiontext"
                  icon={<OutlinedPlusNew width={16} height={16} />}
                  onClick={() => add({...defaultValue})}
                >
                  {addButtonText}
                </Button>
              )}
            </>
          );
        }}
      </Form.List>
    </div>
  );
};

export default TableEditList;
