/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-07-16 14:34:51
 * @LastEditTime: 2025-07-28 15:56:22
 */
import {FC, useCallback, useContext, useState} from 'react';
import IconSvg from '@components/IconSvg';
import styles from './index.module.less';
import StatusIcon from '../StatusIcon';

interface LeftContentHeaderProps {
  status: string;
  errMsg: string;
  onSilderHandle: () => void;
}
const LeftContentHeader: FC<LeftContentHeaderProps> = ({
  status,
  errMsg = '',
  onSilderHandle,
}) => {
  return (
    <div className={styles['content_header']}>
      <div className={styles['content_title']}>
        <div className={styles['content_title_desc']}>可视化配置</div>
        <StatusIcon status={status} errMsg={errMsg}/>
      </div>
      <div style={{cursor: 'pointer'}}>
        <IconSvg
          type="left"
          size={20}
          color="#5C5F66"
          onClick={onSilderHandle}
        />
      </div>
    </div>
  );
};

export default LeftContentHeader;