/*
 * @Author: hong<PERSON><EMAIL>
 * @Date: 2025-07-15 11:51:50
 * @LastEditTime: 2025-08-14 11:12:55
 */

import React, {useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle} from 'react';
import styles from './index.module.less';
import classNames from 'classnames';
import {useEventListener} from 'ahooks';
import IconSvg from '@components/IconSvg';
import StatusIcon from '../components/StatusIcon';

interface ResizableGridProps {
  children?: React.ReactNode[];
  draggable?: boolean;
  rightMaxWidth?: number;
  rightMinWidth?: number;
  status?: string;
  errMsg?: string;
}

const sliderWidth = 40;

// 定义通过 ref 暴露的方法类型
export interface ResizableGridRefHandle {
  handleLeftSlider: (tag: boolean) => void;
  handleRightSilder: (tag: boolean) => void;
}

const ResizableGrid = (props: ResizableGridProps, ref: React.Ref<ResizableGridRefHandle>) => {
  const {
    children,
    draggable = true,
    rightMaxWidth = 600,
    rightMinWidth = 400,
    status = 'success',
    errMsg = ''
  } = props;
  const [dragging, setDragging] = useState(0);
  const [drawerWidth, setDrawerWidth] = useState(rightMinWidth);

  const [leftVisible, setLeftVisible] = useState(false);
  const [rightVisible, setRightVisible] = useState(false);

  const dragRef = useRef<HTMLDivElement>(null);

  useImperativeHandle(ref, () => ({
    handleLeftSlider: (tag) => {
      handleLeftSlider(tag);
    },
    handleRightSilder: (tag) => {
      handleRightSilder(tag);
    }
  }));

  const handleMouseMove = (e: MouseEvent) => {
    if (dragging - e.clientX < rightMinWidth || dragging - e.clientX > rightMaxWidth) {
      return;
    }
    setDrawerWidth(dragging - e.clientX);
  };

  useEventListener(
    ['mousemove', 'mouseup'],
    (e) => (e.type === 'mouseup' ? setDragging(0) : handleMouseMove(e as MouseEvent)),
    {
      enable: !!dragging
    }
  );

  useEventListener(
    ['mousedown'],
    (e) => {
      setDragging(e.clientX + drawerWidth);
    },
    {
      target: dragRef
    }
  );

  const monitorPageWidth = () => {
    setDrawerWidth(rightMinWidth);
    setLeftVisible(false);
    setRightVisible(false);
  };

  useEffect(() => {
    window.addEventListener('resize', monitorPageWidth);
    return () => {
      window.removeEventListener('resize', monitorPageWidth);
    };
  }, []);

  const handleRightSilder = useCallback(
    (tag) => {
      if (leftVisible) {
        handleLeftSlider(false);
        return;
      }
      let width = sliderWidth;
      if (!tag) {
        width = rightMinWidth;
      }
      setRightVisible(tag);
      setDrawerWidth(width);
    },
    [leftVisible]
  );

  const handleLeftSlider = useCallback(
    (tag) => {
      if (rightVisible) {
        handleRightSilder(false);
        return;
      }
      let width = rightMinWidth;
      if (tag) {
        width = document.querySelector('#drawer-container').clientWidth - sliderWidth;
      }
      setDrawerWidth(width);
      setLeftVisible(tag);
    },
    [rightVisible]
  );

  return (
    <div id="drawer-container" className={styles['drawer-container']}>
      <div className={styles['drawer-content-left']}>
        {leftVisible && (
          <div
            className={styles['drawer-content-slide']}
            style={{
              borderRight: '1px solid #e1e2e4'
            }}
          >
            <div className={styles['tag-icon']} style={{background: '#070C140F'}}>
              <IconSvg
                type="meta-table"
                size={16}
                color="#5C5F66"
                onClick={() => {
                  handleLeftSlider(false);
                }}
              />
            </div>
            <div className={styles['status-icon']}>
              <StatusIcon status={status} errMsg={errMsg} />
            </div>
          </div>
        )}
        <div
          className={styles['drawer-content-left-info']}
          style={{
            display: !leftVisible ? 'flex' : 'none'
          }}
        >
          {children[0]}
        </div>
      </div>

      <div
        className={styles['drawer-content-right']}
        style={{
          flex: `0 0 ${drawerWidth}px`,
          overflow: 'hidden'
        }}
      >
        {/* 拖拽线 避免影响其他组件 */}
        <div
          ref={draggable ? dragRef : null}
          className={classNames(styles['drawer-drag-container'], {
            [styles['drawer-draggable']]: draggable === true
          })}
          style={{display: !rightVisible && !leftVisible ? 'block' : 'none'}}
        ></div>
        <div
          className={styles['drawer-content-right-info']}
          style={{
            display: !rightVisible ? 'flex' : 'none'
          }}
        >
          {children[1]}
        </div>
        {rightVisible && (
          <div
            className={styles['drawer-content-slide']}
            style={{
              borderLeft: '1px solid #e1e2e4'
            }}
          >
            <div className={styles['tag-icon']} onClick={() => handleRightSilder(false)}>
              DDL
            </div>
            <div className={styles['status-icon']}>
              <StatusIcon status={status} errMsg={errMsg} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default forwardRef(ResizableGrid);
