.drawer-drag-container {
  width: 1px;
  background-color: #e1e2e4;
  height: 100%;
  position: absolute;
}

.drawer-draggable {
  cursor: col-resize;
  -webkit-user-drag: none;
  z-index: 999;

  &:hover {
    background-color: #2468f2;
    width: 4px;
  }
}

.drawer-container {
  flex: 1;
  height: 100%;
  display: flex;

  .drawer-content-left {
    flex: 1;
    display: flex;
    overflow-x: auto;

    .drawer-content-left-info {
      flex: 1;
      padding: 0 16px 0 32px;
    }
  }

  .drawer-content-slide {
    width: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 12px;

    .tag-icon {
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #3064F2;
      color: #FFF;
      font-size: 9px;
      font-style: normal;
      font-weight: 500;

      &:active, &:hover {
        border: 1px solid #C1D1FF
      }
    }

    .status-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 8px;
    }
  }

  .drawer-content-right {
    position: relative;
    display: flex;

    .drawer-content-right-info {
      width: 100%;
      flex: 1;
    }
  }
}
