/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-07-22 19:43:22
 * @LastEditTime: 2025-08-19 14:54:38
 */

import {ITableColumnNew, ITableParamsNew, ITableDetailRes, ETableDoris, TableSource} from '@api/metaRequest';

import {cloneDeep} from 'lodash';

interface IDataPartProps {
  index?: number;
  columnName: string;
  function: string;
  functionArgument: string;
}
interface ISubmitDataProps extends ITableParamsNew {
  columns: ITableColumnNew[];
  properties: any;
  partitioning: IDataPartProps[];
  distribution: any;
}
/**
 * 将表单数据格式化为接口数据格式
 * @param data json数据
 */
export const submitDataFormate = (data: ISubmitDataProps): ITableParamsNew => {
  const newData = cloneDeep(data);
  // 处理属性配置
  const properties = newData?.properties || [];
  delete newData.properties;
  const tempProp = {};
  properties?.forEach((item) => {
    const {name, value} = item;
    tempProp[name] = value;
  });
  newData['properties'] = tempProp;

  const filterCol = newData?.columns?.filter((item) => item.name && item.typeName) || [];
  if (newData?.dataSourceFormat === TableSource.DORIS && newData?.tableModel !== ETableDoris.AGGREGATE) {
    filterCol.forEach((item) => {
      delete item.aggregateType;
    });
  }
  newData.columns = [...filterCol];

  filterCol.forEach((item, index) => {
    item.nullable = !item.nullable;
    item.position = index;
    item.keyIndex !== undefined && item.keyIndex === -1 && delete item.keyIndex;
  });

  if (newData.partitioning) {
    newData.partitioning = [
      ...newData?.partitioning?.filter((item) => Object.keys(item).every((key) => item[key] !== undefined))
    ];
  }
  return {...newData};
};

/**
 * 将接口数据格式化为表单数据格式
 * @param data
 */
export const detailDataFormate = (data: ITableDetailRes): ISubmitDataProps => {
  const newData: any = {
    ...data,
    columns: [],
    properties: []
  };
  if (newData.storageLocation) {
    newData.storageLocation = newData.storageLocation.replace('bos://', '');
  }
  data?.columns?.forEach((item: any) => {
    item.nullable = !item.nullable;
    const {name} = item;
    item.oldName = name;
  });
  newData.columns = [...(data?.columns || [])];
  data?.partitioning?.forEach((item, index) => (item.index = index));
  const newProps =
    (data?.properties &&
      Object.keys(data.properties).map((key) => {
        return {name: key, value: data.properties[key]};
      })) ||
    [];
  newData.properties = [...newProps];
  return newData;
};
