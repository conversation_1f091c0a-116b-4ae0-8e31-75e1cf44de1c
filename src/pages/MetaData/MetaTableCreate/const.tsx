/*
 * @Author: ho<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2025-07-16 11:50:15
 * @LastEditTime: 2025-08-20 20:29:52
 */
export enum TableSource {
  ICEBERG = 'ICEBERG',
  DORIS = 'DORIS'
}

export enum ETableIceberg {
  MANAGED = 'MANAGED',
  EXTERNAL = 'EXTERNAL'
}

export const IcebergTableTypeMap = {
  [ETableIceberg.MANAGED]: '内部表',
  [ETableIceberg.EXTERNAL]: '外部表'
};

export enum ETableDoris {
  DUPLICATE = 'DUPLICATE_KEY',
  UNIQUE = 'UNIQUE_KEY',
  AGGREGATE = 'AGGREGATE_KEY'
}

export const DorisTableTypeMap = {
  [ETableDoris.DUPLICATE]: '明细模型',
  [ETableDoris.UNIQUE]: '唯一模型',
  [ETableDoris.AGGREGATE]: '聚合模型'
};

export enum ETableDorisBucket {
  HASH = 'HASH',
  RANDOM = 'RANDOM'
}

export const DorisTableBucketMap = {
  [ETableDorisBucket.HASH]: 'Hash分桶',
  [ETableDorisBucket.RANDOM]: 'Random分桶'
};

export const sqlFormatContent = {
  [TableSource.ICEBERG]: `CREATE TABLE <table_name> (
    <column_name_1> <data_type> [NOT NULL] [COMMENT <column_comment>],
    <column_name_2> <data_type> [NOT NULL] [COMMENT <column_comment>],
    ...
)
USING iceberg
PARTITIONED BY (column_name_1, <transform_function>(column_name_2))
COMMENT <table_comment>
TBLPROPERTIES (
    <additional_properties>
);`,
  [TableSource.DORIS]: `CREATE TABLE <table_name> (
    <column_name_1> <data_type> [NOT NULL] [<aggregation_type>] [DEFAULT <default_value>],
    <column_name_2> <data_type> [NOT NULL] [<aggregation_type>] [DEFAULT <default_value>],
    ...
    <partition_column> DATE NOT NULL  -- 分区列必须指定 NOT NULL
)
<MODEL_TYPE> KEY(<key_columns>)
PARTITION BY RANGE(<partition_column>) (
    <partition_range_definitions>
)
DISTRIBUTED BY HASH(<bucket_column>) BUCKETS <bucket_number>
PROPERTIES (
    <additional_properties>
);`
};
