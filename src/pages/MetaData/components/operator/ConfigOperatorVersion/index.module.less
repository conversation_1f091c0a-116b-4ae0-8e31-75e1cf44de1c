.operator-version-config {
  background-color: #fff;
  height: calc(100% - 20px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 8px;
  border-radius: 6px;
  border: 1px solid #e8e9eb;
  .form-item-title {
    /* 网络及可用区 */

    padding-left: 0px;
    padding-bottom: 20px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #151b26;
    /* 垂直居中 */
    display: inline-flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      margin-right: 8px;
      width: 4px;
      height: 14px;
      background: #2468f2;
      border-radius: 0px 2px 2px 0px;
      transform: rotate(180deg);
    }
  }
  .form-item-table {
    width: 100%;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0 16px 16px 16px;
    .form-item-table-title {
      padding-left: 0px;
      padding-bottom: 6px;
      font-weight: 500;
      font-size: 14px;
      color: #151b26;
      /* 垂直居中 */
      display: inline-flex;
      align-items: center;
    }
  }
  .config-title {
    padding: 16px 16px 0;
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    color: #151b26;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .config-container {
    overflow: auto;
    flex: 1;
    padding: 16px;
  }
  .config-form {
    width: 100%;
    .config-form-item {
      width: 100%;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0 16px 16px 16px;
    }
  }

  .config-footer {
    height: 80px;
    display: flex;
    align-items: center;
    border-top: 1px solid #e8e9eb;

    :global(.acud-btn) {
      margin-left: 20px;
      width: 114px;
    }
  }

  :global {
    .acud-select {
      width: 100%;
    }
    .form-item-title {
      /* 网络及可用区 */

      padding-left: 0px;
      padding-bottom: 20px;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      color: #151b26;
      /* 垂直居中 */
      display: inline-flex;
      align-items: center;

      &::before {
        content: '';
        display: inline-block;
        margin-right: 8px;
        width: 4px;
        height: 14px;
        background: #2468f2;
        border-radius: 0px 2px 2px 0px;
        transform: rotate(180deg);
      }
    }
  }
}
