.workarea-upload {
  position: relative;
  height: 200px !important;
  margin-bottom: 24px;

  // div.acud-upload-select {
  //   border: 1px dashed #d9d9d9;
  //   border-radius: 6px;
  //   width: 100%;
  //   height: 100%;
  //   cursor: pointer;

  //   &:hover {
  //     border-color: #528eff;
  //   }
  // }

  .upload-control {
    position: absolute;
    bottom: 12px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    .upload-btn {
      color: #2468f2;
      cursor: pointer;
    }
  }

  .upload-dragger {
    position: relative;
    height: 100%;
  }

  .isDrag {
    z-index: 1000;
  }

  :global {
    .acud-upload-list {
      position: absolute;
      top: 14px;
      left: 12px;
      width: calc(100% - 24px);
      max-height: calc(100% - 58px);
      overflow-y: auto;

      .acud-upload-list-item {
        border: none;
        border-radius: 6px;
        background: #f6f4f4;
        &:hover {
          background: #f6f4f4;
        }
      }

      .acud-upload-list-item-info {
        height: 32px;
      }
      .acud-upload-span {
        display: flex;
        align-items: center;
        padding: 10px;
      }
    }
  }
}
