import {uploadFileOperatorVersion} from '@api/meta/operate';
import FilePathSelectWorkarea, {FileNodeTypeEnum} from '@components/FilePathSelectWorkarea';
import IconSvg from '@components/IconSvg';
import ShowUploadFile, {ShowUploadFileRefHandle} from '@components/ShowUploadFile';
import {WorkspaceContext} from '@pages/index';
import {Button, Form, Input, Modal, Radio, Tag, toast, Upload} from 'acud';
import {FormInstance} from 'acud/lib/form';
import type {UploadProps} from 'acud/lib/upload';
import {useDrop, useMemoizedFn} from 'ahooks';
import classNames from 'classnames/bind';
import React, {useContext, useRef, useState} from 'react';
import styles from './index.module.less';
import SelectFile from './SelectFile';

const cx = classNames.bind(styles);

// 单个文件最大体积 MB
const MAX_FILE_SIZE_SINGLE = 100;

// 上传类型
const UploadType = {
  STORAGE: 'STORAGE',
  WORKSPACE: 'WORKSPACE'
};
/**
 * 算子版本配置
 */
const StorageLocation: React.FC<{form: FormInstance}> = ({form}) => {
  const catalogName = Form.useWatch('catalogName', form);
  const schemaName = Form.useWatch('schemaName', form);
  const name = Form.useWatch('operatorName', form);
  const version = Form.useWatch('version', form);

  const [uploadForm] = Form.useForm();
  const {workspaceId} = useContext(WorkspaceContext);
  const [visible, setVisible] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadType, setUploadType] = useState<string>(UploadType.STORAGE);

  const uploadRef = useRef<ShowUploadFileRefHandle>();

  useDrop(document.querySelector('.workarea-upload-box .acud-upload-list'), {
    onDrop: (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.dataTransfer) {
        const newEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          clientX: e.clientX,
          clientY: e.clientY,
          screenX: e.screenX,
          screenY: e.screenY,
          dataTransfer: e.dataTransfer
        });
        document.querySelector('.workarea-upload-box .acud-upload-drag-container')?.dispatchEvent(newEvent);
      }
    }
  });

  const [uploadRequestInfo, setUploadRequestInfo] = useState<any>([]);

  const handleUpload = useMemoizedFn(async () => {
    let mes = '';
    if (uploadType === UploadType.STORAGE) {
      if (fileList.length === 0) {
        mes = '请选择文件';
        return;
      }
    } else {
      try {
        await uploadForm.validateFields();
      } catch (error) {
        console.log(error);
        mes = '请选择工作区文件';
      }
    }

    if (mes) {
      toast.error({
        message: mes,
        duration: 3
      });
      return;
    }
    const value = uploadForm.getFieldsValue();

    if (uploadType === UploadType.STORAGE) {
      uploadRequest(fileList[0]);
      uploadRef.current?.expand();
    } else {
      const res = await uploadFileOperatorVersion({
        workspaceId,
        fullName: `${catalogName}.${schemaName}.${name}`,
        versionName: version,
        workspacePath: value.workspacePath,
        fileUploadType: UploadType.WORKSPACE
      });

      console.log('上传成功:', res?.result?.storageLocation);
      handleClose();

      form.setFieldsValue({
        storageLocation: res?.result?.storageLocation
      });
    }
  });
  function updateUploadRequestInfo(file: any, obj: any) {
    setUploadRequestInfo((pre) => {
      return pre.map((item) => {
        if (file.uid === item.file.uid) {
          return {...item, ...obj};
        }
        return item;
      });
    });
  }

  // 单文件上传逻辑
  const uploadRequest = useMemoizedFn(async (file: any) => {
    const formData = new FormData();
    formData.append('file', file);
    setUploadRequestInfo([{file, status: 'loading'}]);
    try {
      const res = await uploadFileOperatorVersion({
        workspaceId,
        fullName: `${catalogName}.${schemaName}.${name}`,
        versionName: version,
        workspacePath: ``,
        fileUploadType: UploadType.STORAGE,
        formData,
        onProgress: (event) => {
          updateUploadRequestInfo(file, {percent: Math.round((event.loaded / event.total) * 100)});
        }
      });
      if (res?.success != true) {
        throw new Error('文件上传失败');
      }
      handleClose();

      console.log('上传成功:', res?.result?.storageLocation);
      form.setFieldsValue({
        storageLocation: res?.result?.storageLocation
      });
      updateUploadRequestInfo(file, {status: 'success'});
    } catch (error) {
      console.error('文件上传失败:', error);
      updateUploadRequestInfo(file, {status: 'failed'});
    }
  });

  const handleClose = useMemoizedFn(() => {
    setVisible(false);
  });

  const uploadProps: UploadProps = {
    multiple: true,
    accept: '.whl',
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      setFileList(() => {
        if (file.size > MAX_FILE_SIZE_SINGLE * 1024 * 1024) {
          toast.error({
            message: `单个文件大小不能超过${MAX_FILE_SIZE_SINGLE}MB`,
            duration: 3
          });
          return [];
        }
        if (!file.name.endsWith('.whl')) {
          toast.error({
            message: `文件类型错误`,
            duration: 3
          });
          return [];
        }
        return [file];
      });
      return false; // 阻止自动上传
    },
    fileList
  };

  const storageLocation = Form.useWatch('storageLocation', form);

  // 移除文件
  const removeFile = useMemoizedFn(() => {
    form.setFieldsValue({
      storageLocation: ''
    });
    setFileList([]);
  });

  return (
    <>
      <Form.Item
        label="执行代码"
        name="storageLocation"
        rules={[{required: true, message: '请选择执行代码'}]}
      >
        <Button onClick={() => setVisible(true)} icon={<IconSvg type="add" />}>
          添加文件
        </Button>
        <SelectFile file={fileList[0]} path={storageLocation} onRemove={() => removeFile()} />
      </Form.Item>

      <Modal
        title="导入文件"
        visible={visible}
        onOk={handleUpload}
        onCancel={() => handleClose()}
        okText="上传"
        cancelText="取消"
        confirmLoading={uploading}
        width={800}
      >
        <Radio.Group className="mb-4" value={uploadType} onChange={(e: any) => setUploadType(e.target.value)}>
          <Radio.Button value={UploadType.STORAGE}>本地上传</Radio.Button>
          <Radio.Button value={UploadType.WORKSPACE}>工作区文件</Radio.Button>
        </Radio.Group>

        {uploadType === UploadType.STORAGE && (
          <div className={cx('workarea-upload', 'workarea-upload-box')}>
            <Upload.Dragger {...uploadProps}>
              <div className={cx('upload-dragger')}>
                {fileList.length === 0 ? (
                  <div className="h-full flex items-center justify-center flex-col">
                    <p className="text-gray-600 mb-2">点击或者拖拽文件到这里上传</p>
                    <p className="text-gray-400 text-sm">文件不超过{MAX_FILE_SIZE_SINGLE}MB</p>
                  </div>
                ) : (
                  <div className={cx('upload-control')}>
                    <span className={cx('upload-btn')}>点击此处</span>
                    选择文件
                  </div>
                )}
              </div>
            </Upload.Dragger>
          </div>
        )}
        <Form form={uploadForm} layout="vertical" inputMaxWidth="100%">
          {uploadType === UploadType.STORAGE && (
            <Form.Item label="上传目录">
              <Input disabled value={`${catalogName}.${schemaName}.${name}`} />
            </Form.Item>
          )}

          {uploadType === UploadType.WORKSPACE && (
            <Form.Item
              label="选择文件"
              name="workspacePath"
              rules={[
                {
                  validator: (rule, value) => {
                    if (!value) {
                      return Promise.reject(new Error('请选择文件'));
                    }
                    if (!value.endsWith('.whl')) {
                      return Promise.reject(new Error('请选择whl文件'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <FilePathSelectWorkarea
                selectNodeType={FileNodeTypeEnum.FILE}
                selectFileSuffix={['whl']}
                inputReadOnly
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
      <ShowUploadFile
        ref={uploadRef}
        uploadList={uploadRequestInfo}
        setUploadListFun={setUploadRequestInfo}
        reUploadRequest={(file) => uploadRequest(file.originFileObj)}
      />
    </>
  );
};

export default StorageLocation;
