import {Ellipsis} from '@baidu/bce-react-toolkit';
import React from 'react';
import styles from './index.module.less';
import IconSvg from '@components/IconSvg';
import {Button} from 'acud';
const SelectFile: React.FC<{file: File; path: string; onRemove: () => void}> = ({file, path, onRemove}) => {
  if (!path) {
    return null;
  }
  return (
    <>
      {file && (
        <div className={styles['text-name']}>
          <Ellipsis tooltip={file?.name}>{file?.name}</Ellipsis>

          <Button type="text" className={styles['delete-icon']} onClick={() => onRemove()}>
            <IconSvg type="delete" />
          </Button>
        </div>
      )}
      <div className={styles['text-path']}>
        <Ellipsis tooltip={path}>{path}</Ellipsis>
      </div>
    </>
  );
};

export default SelectFile;
