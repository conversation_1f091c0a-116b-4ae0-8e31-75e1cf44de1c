import React, {useState, useContext, useEffect, useRef, forwardRef, useImperativeHandle} from 'react';
import {Button, Table, Space, Tag, Tooltip, Modal, Form, Input, toast, Loading} from 'acud';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import useFrontendTable from '@hooks/useFrontendTable';
import {queryDatasetVersionData} from '@api/metaRequest';
import {WorkspaceContext} from '@pages/index';
import MonacoSqlEditor from '@components/MonacoSqlEditor';
import SaveIcon from '@assets/originSvg/save.svg';
import LoadIcon from '@assets/originSvg/load.svg';
import urls from '@utils/urls';
import {useLocation, useNavigate} from 'react-router-dom';
import useUrlState from '@ahooksjs/use-url-state';
import WorkareaPathSelect from '@components/WorkareaPathSelect';
import {FILE_NAME_ERROR_MESSAGE, validatePath} from '@pages/WorkArea/config';
import {loadFile, saveFile, getWorkspaceFileList, updateFile} from '@api/WorkArea';
import FilePreview, {IFilePreviewRef, IPreviewType} from '@components/FilePreview';
import DatasetSearchIcon from '@assets/originSvg/dataset-search.svg';
import SearchIcon from '@assets/originSvg/search.svg';
import ResizablePanels from '@components/ResizablePanels';
import OffScreenIcon from '@assets/originSvg/Off-screen.svg';
import IconSvg from '@components/IconSvg';

const cx = classNames.bind(styles);

function validateSqlName(name: string) {
  if (!name?.endsWith('.sql')) {
    return false;
  }
  if (name?.trim() === '.sql') {
    return false;
  }
  return validatePath(name);
}

const SQL_NAME_ERROR_MESSAGE = FILE_NAME_ERROR_MESSAGE + '，必须以.sql结尾';

interface DatasetSqlSearchProps {
  onExit?: () => void;
}
export default function DatasetSqlSearch({onExit}: DatasetSqlSearchProps) {
  const placeholder = `-- 使用 SQL 控制台对数据集进行查询遵循 Doris SQL 语法\nselect * from dataset limit 10;`;
  const [sqlQuery, setSqlQuery] = useState(placeholder);
  const [executionTime, setExecutionTime] = useState(0);
  const [total, setTotal] = useState(0);
  const [columns, setColumns] = useState([]);
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState();
  const {catalog = '', schema = '', node = '', version = 'v1'} = urlState;
  const sqlModalRef = useRef<SqlModalRef>(null);
  const sqlModalRef2 = useRef<SqlModalRef>(null);
  const [errorMessage, setErrorMessage] = useState('');
  const defaultSql = `select * from dataset;`;
  const [isSqlPanelExpanded, setIsSqlPanelExpanded] = useState(true);
  const isInitData = useRef(true);

  const [imageList, setImageList] = useState([]);
  const filePreviewRef = useRef<IFilePreviewRef>(null);
  const columnsRef = useRef([]);
  const [resetDisabled, setResetDisabled] = useState(true);

  const fullName = `${catalog}.${schema}.${node}`;
  // 使用前端分页表格钩子
  const {loading, loadTableData, tableProps, resetAll} = useFrontendTable<any>({
    getTableApi: async ({workspaceId, fullName, version, sql}) => {
      setErrorMessage('');
      const queryToExecute = isInitData.current ? defaultSql : sql;
      const res = await queryDatasetVersionData(workspaceId, fullName, version, {
        sql: queryToExecute
      });
      return res;
    },
    extraParams: {
      workspaceId,
      fullName,
      version
    },
    listKey: 'rows',
    enableSort: true,
    enableFilter: true,
    disablePagination: true,
    customDataHandler: (data) => {
      const result = data.result || {};
      columnsRef.current = result?.features;
      if (!isInitData.current) {
        setExecutionTime(result.queryTimeMs || 0);
        setTotal(result.rows?.length || 0);
      }
      if (isInitData.current) {
        isInitData.current = false;
      }
      if (result.errorMessage) {
        setErrorMessage(result.errorMessage);
        return;
      }

      // 组装表头
      const columnDatas =
        result?.features?.map((item) => {
          const baseColumn = {
            title: (
              <div className={cx('table-header')}>
                {item.name}
                <br />
                <span className={cx('table-header-type')}>{item.dtype}</span>
              </div>
            ),
            dataIndex: item.name,
            key: item.name,
            width: '200px',
            render(text: string) {
              return (
                <div className={cx('table-cell')} title={text}>
                  {text}
                </div>
              );
            }
          };

          if (item.dtype === 'image') {
            return {
              ...baseColumn,
              render: (imageBase64: string) => (
                <div className={cx('table-cell')}>
                  <img className={cx('table-cell-image')} src={imageBase64} alt={item.name} />
                </div>
              )
            };
          }

          return baseColumn;
        }) || [];

      setColumns(columnDatas);
    },
    customDataMapper: (data) => {
      const result = data.result || {};
      // 组装数据，添加 key
      const mappedRows =
        result?.rows?.map((item, index) => ({
          ...item.row,
          key: index
        })) || [];
      return {
        result: {
          ...result,
          rows: mappedRows
        }
      };
    }
  });

  // 执行SQL查询
  const handleExecuteQuery = async () => {
    await loadTableData({sql: sqlQuery});
    setResetDisabled(false);
  };

  // 重置查询
  const handleReset = async () => {
    isInitData.current = true;
    setSqlQuery(placeholder);
    setExecutionTime(0);
    setTotal(0);
    resetAll();
    await loadTableData({sql: ''});
    setResetDisabled(true);
  };

  // 初始化时执行默认查询
  useEffect(() => {
    loadTableData({sql: sqlQuery});
  }, []);

  const navigate = useNavigate();
  const location = useLocation();
  const handleExit = () => {
    navigate(`${urls.metaData}${location.search}`);
  };

  // 切换SQL面板展开/收起状态
  const toggleSqlPanel = () => {
    setIsSqlPanelExpanded(!isSqlPanelExpanded);
  };

  const onSave = () => {
    sqlModalRef.current?.open();
  };

  // moddal.confirm 的getContainer方法存在问题，会将container给清空掉，所以需要一个额外的container
  const popContainerRef = useRef<HTMLDivElement>(null);
  const handleCreateSql = async (values: {name?: string; parentId?: string}) => {
    const {name, parentId} = values;
    if (!name || !parentId) {
      return;
    }

    // 检查文件是否存在
    const fileRes = await getWorkspaceFileList({
      workspaceId,
      parentId
    });
    if (fileRes.success) {
      const {result} = fileRes;
      const existFile = result.find((item) => item.name === name);
      // 文件存在，提示是否覆盖
      if (existFile) {
        Modal.confirm({
          title: '提示',
          content: '文件已存在，是否覆盖？',
          getContainer: () => popContainerRef.current,
          onOk: async () => {
            const res = await updateFile({
              workspaceId,
              fileId: existFile.id,
              content: sqlQuery
            });
            if (res.success) {
              // TODO: toast目前不生效, getContainer方法存在问题
              toast.success({message: '保存成功', duration: 3});
            }
          }
        });
        return;
      }
    }

    // 不存在，新建文件
    const res = await saveFile({
      workspaceId,
      parentId,
      name,
      content: sqlQuery
    });
    if (res.success) {
      toast.success({message: '保存成功', duration: 3});
    }
  };

  const onLoad = () => {
    sqlModalRef2.current?.open();
  };

  const handleUpdateSql = async (values) => {
    const {fileId} = values;
    if (!fileId) {
      return;
    }
    const res = await loadFile({
      workspaceId,
      fileId
    });
    if (res.success) {
      setSqlQuery(res.result.content);
    }
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        const imageInfo = columnsRef.current
          ?.filter((item) => item.dtype !== 'image')
          .map((item) => ({
            label: item.name,
            value: record[item.name]
          }));
        const imageList = columnsRef.current
          ?.filter((item) => item.dtype === 'image')
          .map((item) => {
            return {
              src: record[item.name],
              thumbnailSrc: record[item.name],
              name: item.name,
              info: imageInfo
            };
          });

        setImageList(imageList);
        filePreviewRef.current?.open();
      }
    };
  };

  const containerRef = useRef<HTMLDivElement>(null);

  const renderTable = () => {
    return (
      <Table {...tableProps} columns={columns} scroll={{x: 'max-content'}} onRow={onRow} pagination={false} />
    );
  };

  return (
    <div className={cx('dataset-search')} ref={containerRef}>
      <div ref={popContainerRef}></div>
      <div className={cx('dataset-search-content')}>
        {/* 页面头部 */}
        <div className={cx('header')}>
          <div className={cx('title')}>
            <span className={cx('icon')}>
              <IconSvg type="meta-dataset" size={20} fill="none" color="#fff" />
            </span>
            {schema} {node}_{version}
          </div>
          <div className={cx('button-group')}>
            <Button disabled={resetDisabled} onClick={handleReset}>
              恢复到原始表
            </Button>
            <Button onClick={toggleSqlPanel}>{isSqlPanelExpanded ? '收起SQL查询' : '展开SQL查询'}</Button>
            <Button icon={<OffScreenIcon />} onClick={onExit}>
              退出数据检索
            </Button>
          </div>
        </div>

        {/* 主体内容区域 */}
        <div className={cx('main-content')}>
          {isSqlPanelExpanded ? (
            <ResizablePanels
              leftPanel={
                <div className={cx('table-section')}>
                  {errorMessage ? <div className={cx('error-message')}>{errorMessage}</div> : renderTable()}
                </div>
              }
              rightPanel={
                <div className={cx('sql-panel')}>
                  <div className={cx('sql-panel-header')}>
                    <span className={cx('panel-title')}>SQL 查询</span>
                    <div className={cx('action-btn-group')}>
                      <div className={cx('action-btn')} onClick={onSave}>
                        <Tooltip
                          title="保存SQL语句"
                          placement="bottom"
                          getPopupContainer={() => containerRef.current}
                        >
                          <SaveIcon />
                        </Tooltip>
                      </div>
                      <div className={cx('action-btn')} onClick={onLoad}>
                        <Tooltip
                          title="导入SQL语句"
                          placement="bottom"
                          getPopupContainer={() => containerRef.current}
                        >
                          <LoadIcon />
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                  <div className={cx('sql-editor-section')}>
                    <div className={cx('editor-container')}>
                      <MonacoSqlEditor
                        className={cx('editor')}
                        value={sqlQuery}
                        onChange={setSqlQuery}
                        height="100%"
                        language="sql"
                        onExecute={handleExecuteQuery}
                      />
                    </div>
                    <div className={cx('action-group')}>
                      <div className={cx('search-summary')}>
                        <DatasetSearchIcon />
                        共查询到{total}条数据
                        <span className={cx('divider')}></span>
                        耗时{executionTime}ms
                      </div>
                      <Button
                        disabled={!sqlQuery}
                        className={cx('w-full')}
                        type="primary"
                        loading={loading}
                        onClick={handleExecuteQuery}
                        icon={<SearchIcon />}
                      >
                        {loading ? '查询中...' : '开始查询'}
                      </Button>
                    </div>
                  </div>
                </div>
              }
              initialConfig={{
                rightWidth: '500px',
                fixedSide: 'right'
              }}
              constraints={{
                rightMinWidth: '400px',
                rightMaxWidth: '800px'
              }}
              dividerConfig={{
                width: 12,
                visible: true
              }}
            />
          ) : (
            <div className={cx('table-section')}>
              {errorMessage ? <div className={cx('error-message')}>{errorMessage}</div> : renderTable()}
            </div>
          )}
          <SqlModal
            title="保存SQL语句"
            mode="export"
            workspaceId={workspaceId}
            ref={sqlModalRef}
            onSuccess={handleCreateSql}
            getContainer={() => containerRef.current}
          />
          <SqlModal
            title="导入SQL语句"
            mode="import"
            workspaceId={workspaceId}
            ref={sqlModalRef2}
            onSuccess={handleUpdateSql}
            getContainer={() => containerRef.current}
          />
          {imageList.length > 0 && (
            <FilePreview
              ref={filePreviewRef}
              files={imageList}
              type={IPreviewType.MULTIPLE}
              getContainer={() => containerRef.current}
            />
          )}
        </div>
      </div>
    </div>
  );
}

interface SqlModalProps {
  workspaceId: string;
  title?: string;
  mode?: 'export' | 'import';
  onSuccess?: (values: {name?: string; parentId?: string; fileId?: string}) => void;
  getContainer?: () => HTMLElement;
}

interface SqlModalRef {
  open: () => void;
  close: () => void;
}

const SqlModal = forwardRef<SqlModalRef, SqlModalProps>((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    open: () => {
      form.resetFields();
      setVisible(true);
    },
    close: () => {
      setVisible(false);
    }
  }));

  async function handleOk() {
    const values = await form.validateFields();
    props.onSuccess?.(values);
    setVisible(false);
  }
  const fileNameRules = [
    {
      required: true,
      message: '请输入名称'
    },
    {
      validator: (_, value) => {
        if (!validateSqlName(value)) {
          return Promise.reject(new Error(SQL_NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];

  return (
    <Modal visible={visible} onCancel={() => setVisible(false)} onOk={handleOk} {...props}>
      <Form form={form} layout="vertical">
        {props.mode === 'import' ? (
          <Form.Item
            inputMaxWidth="100%"
            label="SQL文件"
            name="fileId"
            rules={[{required: true, message: '请选择SQL文件'}]}
          >
            <WorkareaPathSelect
              mode="file"
              canSelectFileExtensions={['.sql']}
              getContainer={props.getContainer}
            />
          </Form.Item>
        ) : (
          <>
            <Form.Item
              inputMaxWidth="100%"
              label="SQL名称"
              name="name"
              rules={fileNameRules}
              extra={SQL_NAME_ERROR_MESSAGE}
            >
              <Input />
            </Form.Item>
            <Form.Item
              inputMaxWidth="100%"
              label="保存位置"
              name="parentId"
              rules={[{required: true, message: '请选择保存位置'}]}
            >
              <WorkareaPathSelect mode="folder" getContainer={props.getContainer} />
            </Form.Item>
          </>
        )}
      </Form>
    </Modal>
  );
});

SqlModal.displayName = 'SqlModal';
