.dataset-search {
  padding: 0 8px 8px;
  height: 100%;
  box-sizing: border-box;

  :global {
    .acud-tooltip {
      .acud-tooltip-content {
        .acud-tooltip-arrow {
          .acud-tooltip-arrow-content {
            background-color: rgba(7, 12, 20, 0.85);
          }
        }

        .acud-tooltip-inner {
          color: #fff;
          background-color: rgba(7, 12, 20, 0.85);

          button:not(:hover) {
            color: #fff;
          }
        }
      }
    }
  }

  .dataset-search-content {
    height: 100%;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    border: 1px solid #d4d6d9;
    border-radius: 6px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      gap: 16px;
      flex-shrink: 0;

      .title {
        font-family: 'PingFang SC';
        font-weight: 500;
        font-size: 20px;
        line-height: 1.4em;
        color: #151b26;
        margin: 0;
        display: flex;
        align-items: center;

        .icon {
          background-image: linear-gradient(180deg, #151b26 0%, #454343 100%);
          border-radius: 8px;
          width: 32px;
          height: 32px;
          display: inline-flex;
          margin-right: 12px;
          justify-content: center;
          align-items: center;
        }
      }

      .button-group {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    // 主体内容区域
    .main-content {
      flex: auto;
      min-height: 0;

      // 表格区域样式
      .table-section {
        height: 100%;
        display: flex;
        flex-direction: column;
        min-height: 0;
        min-width: 0;

        .error-message {
          padding: 20px;
          text-align: center;
          color: #ff4d4f;
          background: #fff2f0;
          border: 1px solid #ffccc7;
          border-radius: 6px;
        }

        .table-header {
          .table-header-type {
            font-size: 12px;
            color: #84868c;
            font-family: 'PingFang SC';
          }
        }

        .table-cell {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 200px;
        }

        .table-cell-image {
          display: inline-block;
          width: 48px;
          height: 48px;
          object-fit: cover;
          border-radius: 6px;
          border: 0.5px solid rgba(0, 0, 0, 0.08);
        }

        :global {
          .acud-table-wrapper {
            height: 100%;
            .acud-loading-loading-wrapper {
              height: 100%;
              & > div {
                height: 100%;
                display: flex;
                flex-direction: column;
                .acud-table {
                  flex: auto;
                  min-height: 0;
                  .acud-table-container {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    .acud-table-thead {
                      flex: none;
                      th {
                        background-color: #f8fafc !important;
                        box-sizing: border-box;
                        height: 60px;
                      }
                    }
                    .acud-table-body {
                      flex: auto;
                      min-height: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }

      // SQL查询面板样式
      .sql-panel {
        height: 100%;
        background: #fff;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 0.5px solid #f6f7fa;

        .sql-panel-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: #f8fafc;
          border-radius: 6px 6px 0 0;
          flex-shrink: 0;
          height: 60px;
          box-sizing: border-box;

          .panel-title {
            font-weight: 500;
            color: #333;
            font-size: 14px;
          }

          .action-btn-group {
            display: flex;
            align-items: center;
          }
          .action-btn {
            cursor: pointer;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            &:hover {
              background-color: #edf0f7;
            }
          }
        }

        .sql-editor-section {
          flex: 1;
          display: flex;
          flex-direction: column;
          // padding: 16px;
          gap: 12px;
          background-color: #fbfdfd;
          min-height: 0;

          .editor-header {
            display: flex;
            justify-content: flex-start;
            align-items: center;
          }

          .editor-container {
            padding: 16px 16px 0 0;
            flex: 1;
            min-height: 0;
            :global {
              .monaco-editor-background {
                background-color: #fbfdfd;
              }
              .monaco-editor .margin {
                background-color: #fbfdfd;
              }
              .monaco-editor .line-numbers {
                color: #495366;
              }

              .monaco-editor .line-numbers.active-line-number {
                color: #000;
                font-weight: 600;
              }
            }
          }
          .action-group {
            padding: 0 16px 16px 16px;
            .search-summary {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;
              color: #666;
              margin-bottom: 12px;
              .divider {
                width: 1px;
                height: 12px;
                background-color: #e8e8e8;
              }
            }
          }
        }
      }
    }
  }
}
