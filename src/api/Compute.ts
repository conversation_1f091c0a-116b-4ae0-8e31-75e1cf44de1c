import {request, BaseResponseType, urlPrefix} from './apiFunction';

// 实际 API 路径前缀，用于真实 API 调用
const vpcUrlPrefix = '/api/network/v1';

// 计算实例类型
export enum Engine {
  // 常驻实例
  Ray = 'Ray',
  // 查询检索实例
  Doris = 'Doris',
  // 源连接与集成实例
  Seatunnel = 'Seatunnel',
  // Spark
  Spark = 'Spark'
}

export type ComputeStatus = 'DEPLOY' | 'RUNNING' | 'INVALID' | 'CREATED_FAIL';

interface GetComputeResourceListParams {
  workspaceId: string;
  pageNo?: number;
  pageSize?: number;
  keyword?: string;
  status?: string;
  order?: 'asc' | 'desc';
  orderBy?: string;
  // 计算实例类型
  engine?: Engine;
}

export interface ComputeResourceItem {
  workspaceId: string; // 工作空间id
  name: string; // 计算实例名字
  status: ComputeStatus;
  creator: string; // 创建者名字
  createdAt: string; // 创建时间
  computeId?: string; // 计算实例 id
  region?: string; // 地域
  runtime?: string; // 运行环境
  clusterType?: string; // 集群资源规格
  engine?: string; // 集群计算引擎
  accountId?: string; // 账户id
  updatedAt?: string; // 更新时间
  nodeCount?: number; // 节点数量
  mirrorVersion?: string; // 镜像版本
}

export interface GetComputeResourceListResult {
  computes: ComputeResourceItem[];
  total: number;
}

export function getComputeResourceList(
  params: GetComputeResourceListParams
): BaseResponseType<GetComputeResourceListResult> {
  // 实际 API 调用
  return request({
    url: `${urlPrefix}/workspaces/${params.workspaceId}/instances`,
    method: 'GET',
    params
  });
}

interface CreateComputeResourceParams {
  name: string;
  workspaceId: string;
  region: string;
  chargingType: string;
  type: string;
  vpcId: string;
  availableZone: string;
  subnetId: string;
  engine: string;
  mirrorVersion: string;
  clusterType: string;
  nodeCnt: number;
}

// 计算实例详情
export function getComputeResourceDetail({
  workspaceId,
  computeId
}: {
  workspaceId: string;
  computeId: string;
}): BaseResponseType<ComputeResourceItem> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instances/${computeId}`,
    method: 'GET'
  });
}

export function createComputeResource(params: CreateComputeResourceParams): BaseResponseType<any> {
  const {workspaceId} = params;

  // 实际 API 调用
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instances`,
    method: 'POST',
    data: params
  });
}

export function deleteComputeResource({
  workspaceId,
  computeId
}: {
  workspaceId: string;
  computeId: string;
}): BaseResponseType<any> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instances/${computeId}`,
    method: 'DELETE'
  });
}

export interface Vpc {
  vpcId: string;
  name: string;
  cidr: string;
  shortId: string;
  defaultVpc: boolean;
}

type GetVpcListResult = Vpc[];

export function getVpcList(): BaseResponseType<GetVpcListResult> {
  return request({
    url: `${vpcUrlPrefix}/vpcs`,
    method: 'POST'
  });
}

export interface Zone {
  zoneName: string;
  specs: {
    dataBuilderClusterType: string;
    spec: string;
    inventoryQuantity: number;
    type: 'CPU' | 'GPU';
  }[];
}

interface GetZoneListResult {
  zones: Zone[];
}

export function getZoneList({engine}: {engine?: string}): BaseResponseType<GetZoneListResult> {
  return request({
    url: `${urlPrefix}/inventory`,
    method: 'GET',
    params: {
      engine
    }
  });
}

export interface Subnet {
  subnetId: string;
  shortId: string;
  name: string;
  az: string;
}
interface GetSubnetListResult {
  success: boolean;
  page: {
    result: Subnet[];
  };
}

export function getSubnetList({vpcId}: {vpcId: string}): Promise<GetSubnetListResult> {
  return request({
    url: `${vpcUrlPrefix}/subnet/pageList`,
    method: 'POST',
    data: {
      vpcId
    }
  });
}

/* 任务实例相关 -- start */
interface createTaskInstanceTemplateParams {
  workspaceId: string;
  chargeType: string; // 付费方式
  region?: string;
  vpcId: string;
  subnetId: string;
  name: string;
  engine: string; // 引擎类型
  mirrorVersion?: string; // 镜像版本
  clusterType?: string; // 节点规格
  nodeCnt: number; // 节点数量
  autoRenewal?: boolean;
  availableZone: string;
  resourcePoolId?: string; //资源池id
  resourcePoolName: string; // 资源池名称
  unit?: string;
  unitMount?: string;
}
interface updateTaskInstanceTemplateParams extends createTaskInstanceTemplateParams {
  templateId?: string; // 模板id
}
// 创建任务实例模版
export function createTaskInstanceTemplate(params: createTaskInstanceTemplateParams): BaseResponseType<any> {
  const {workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instancejobs/templates`,
    method: 'POST',
    data: params
  });
}

// 更新任务实例模版
export function updateTaskInstanceTemplate(params: updateTaskInstanceTemplateParams): BaseResponseType<any> {
  const {workspaceId, templateId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instancejobs/templates/${templateId}`,
    method: 'PUT',
    data: params
  });
}
interface taskInstanceTemplate {
  id: string;
  name: string;
  status: string;
  engine: string;
  mirrorVersion: string;
  chargeType: string;
  unit: string;
  unitMount: string;
  autoRenewal: true;
  region: string;
  availableZone: string; // 可用区
  vpcId: string;
  subnetId: string;
  workflowId: string;
  workflowName: string;
  taskId: string;
  taskName: string;
  config: string;
  env: string;
  resourcePoolId: string;
  resourcePoolName: string;
  clusterType: string;
  nodeCnt: string;
  processor: string;
  createdAt: string;
  updatedAt: string;
}
// 任务实例模版详情
export function getTaskInstanceTemplateDetail({
  workspaceId,
  templateId
}: {
  workspaceId: string;
  templateId: string;
}): BaseResponseType<taskInstanceTemplate> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instancejobs/templates/${templateId}`,
    method: 'GET'
  });
}
// 删除任务实例模版
export function deleteTaskInstanceTemplate({
  workspaceId,
  templateId
}: {
  workspaceId: string;
  templateId: string;
}) {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instancejobs/templates/${templateId}`,
    method: 'DELETE'
  });
}
export interface TaskInstanceTemplate {
  engine: string; // 引擎类型
  id: string; // 模板 id
  mirrorVersion: string; // 镜像版本
  name: string; // 模板名称
  nodeCnt: string; // 节点数量
  clusterType: string; // 节点规格
  chargeType: string; // 付费方式
}
interface getTaskInstanceTemplateListParams {
  workspaceId: string;
  order?: string;
  orderBy?: string; // 默认按createAt倒序排序
  pageNo?: number;
  pageSize?: number;
}
interface TaskInstanceTemplateResult {
  total: number; // 总条数
  templates: TaskInstanceTemplate[];
}
// 任务实例模版列表
export function getTaskInstanceTemplateList(
  params: getTaskInstanceTemplateListParams
): BaseResponseType<TaskInstanceTemplateResult> {
  const {workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instancejobs/templates`,
    method: 'GET',
    params
  });
}
/**
 * DEPLOY 生效中
 * RUNNING 运行中
 * INVALID 失效
 * CREATED_FAIL 创建失败
 */
export enum TASK_INSTANCE_STATUS {
  DEPLOY = 'DEPLOY',
  RUNNING = 'RUNNING',
  INVALID = 'INVALID',
  CREATED_FAIL = 'CREATED_FAIL'
}
interface getTaskInstanceListParams {
  workspaceId: string;
  order?: string;
  orderBy?: string; // 默认按createAt倒序排序
  pageNo?: number;
  pageSize?: number;
  status?: string; // 多选筛选
}
export interface TaskInstanceResultItem {
  chargeType?: string;
  createdAt: string; // 创建时间
  updatedAt?: string; // 更新时间
  engine?: string;
  id: string;
  name: string;
  mirrorVersion: string;
  nodeCnt: string;
  clusterType?: string;
  region?: string;
  runtime?: string;
  status: TASK_INSTANCE_STATUS;
  taskId: string;
  taskName: string;
  workflowId: string;
  workflowName: string;
}
interface TaskInstanceResult {
  total: number; // 总条数
  instances: TaskInstanceResultItem[];
}
// 任务实例列表
export function getTaskInstanceList(params: getTaskInstanceListParams): BaseResponseType<TaskInstanceResult> {
  const {workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instancejobs`,
    method: 'GET',
    params
  });
}
export interface TaskInstanceDetailResult {
  autoRenewal: boolean; // 自动续费
  availableZone: string; // 可用区
  chargeType: string; // 付费方式
  config: string; // spark config
  env: string; // spark 环境变量
  createdAt: string; // 创建时间
  updatedAt: string; // 更新时间
  engine: string;
  id: string;
  name: string;
  region: string;
  mirrorVersion: string;
  nodeCnt: string;
  clusterType: string;
  processor: string;
  resourcePoolId: string; // 资源池id
  resourcePoolName: string; // 资源池名称
  status: TASK_INSTANCE_STATUS;
  vpcId: string; // 私有网络Id
  subnetId: string; // 子网id
  taskId: string; // 任务id
  taskName: string; // 任务名称
  unit: string; // 包年包月单位
  unitMount: string; // 包年或包月数量
  workflowId: string; // 工作流id
  workflowName: string; // 工作流名称
}
// 任务实例详情
export function getTaskInstanceDetail({
  workspaceId,
  batchJobId
}: {
  workspaceId: string;
  batchJobId: string;
}): BaseResponseType<TaskInstanceDetailResult> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instancejobs/${batchJobId}`,
    method: 'GET'
  });
}
/* 任务实例相关 -- end */

interface GetComputeResourcePoolListParams {
  workspaceId: string;
  pageNo?: number;
  pageSize?: number;
  // keyword?: string;
  status?: string;
  order?: 'asc' | 'desc';
  orderBy?: string;
}
export interface ComputeResourcePoolItem {
  /**
   * 账户id，主账户id
   */
  accountId?: string;
  /**
   * 可用区
   */
  availableZone: string;
  /**
   * 付费方式 Prepaid预付费 Postpaid后付费
   */
  chargeType?: string;
  /**
   * 集群资源规格
   */
  clusterType?: string;
  /**
   * 创建时间
   */
  createdAt: string;
  /**
   * 创建者，创建者名字
   */
  creator: string;
  /**
   * 正在运行的实例个数
   */
  instanceCount: string;
  /**
   * 资源池名字
   */
  name: string;
  /**
   * 资源总量
   */
  nodeCount: string;
  /**
   * 地域
   */
  region?: string;
  /**
   * 计算资源池id
   */
  resourcePoolId?: string;
  /**
   * 运行环境
   */
  runtime?: string;
  /**
   * 状态，DEPLOY 生效中 RUNNING 运行中 INVALID 失效 CREATED_FAIL 创建失败
   */
  status: ComputeStatus;
  /**
   * 子网id
   */
  subnetId: string;
  /**
   * 闲置资源数量
   */
  unusedNodeCount: string;
  /**
   * 更新时间
   */
  updatedAt?: string;
  /**
   * 已使用资源数量
   */
  usedNodeCount: string;
  /**
   * 工作空间id
   */
  workspaceId: string;
}

export interface GetComputeResourcePoolListResult {
  pools: ComputeResourcePoolItem[];
  total: number;
}

export function getComputeResourcePoolList(
  params: GetComputeResourcePoolListParams
): BaseResponseType<GetComputeResourcePoolListResult> {
  // console.log(params);
  // // 定义数据模板
  // const poolTemplate = {
  //   resourcePoolId: (i: number) => generateId('pool-'),
  //   name: (i: number) => `资源池-${i + 1}`,
  //   workspaceId: 'workspace-123',
  //   status: () => randomPick(['DEPLOY', 'RUNNING', 'INVALID']),
  //   createdAt: () => generateDate(),
  //   creator: 'zhuwei11',
  //   availableZone: () => randomPick(['zoneA', 'zoneB', 'zoneC']),
  //   chargeType: 'Postpaid',
  //   clusterType: () => randomPick(['small', 'medium', 'large']),
  //   instanceCount: () => String(Math.floor(Math.random() * 5)),
  //   nodeCount: () => String(Math.floor(Math.random() * 10) + 1),
  //   region: 'bj',
  //   runtime: 'python3.8',
  //   subnetId: 'subnet-123',
  //   unusedNodeCount: '0',
  //   usedNodeCount: '0',
  //   updatedAt: () => generateDate()
  // };

  // const mockPools = generateMockList<ComputeResourcePoolItem>(35, poolTemplate);
  // return mockListResponse<ComputeResourcePoolItem>(mockPools, params, 'pools');

  return request({
    url: `${urlPrefix}/workspaces/${params.workspaceId}/pools`,
    method: 'GET',
    params
  });
}

export interface CreateComputeResourcePoolParams {
  /**
   * 自动续费，预付费参数
   */
  autoRenewal?: boolean;
  /**
   * 可用区，zoneA zoneB等
   */
  availableZone: string;
  /**
   * 付费方式，Postpaid 后付费；Prepaid 预付费 MVP仅后付费
   */
  chargingType: string;
  /**
   * 节点规格
   */
  clusterType: string;
  /**
   * 集群名称，由大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-64
   */
  name: string;
  /**
   * 节点数量
   */
  nodeCnt: number;
  /**
   * 预安装镜像版本，本期只支持1个版本
   */
  // preInstalledImage?: string;
  /**
   * 地域
   */
  region?: string;
  /**
   * 子网Id
   */
  subnetId: string;
  /**
   * 包年包月单位，month/year，预付费参数
   */
  unit?: string;
  /**
   * 包年或包月数量，预付费参数
   */
  unitMount?: string;
  /**
   * 私有网络Id
   */
  vpcId: string;
  /**
   * 工作空间id
   */
  workspaceId: string;
  engine?: string;
  mirrorVersion?: string;
  [property: string]: any;
}

export function createComputeResourcePool(params: CreateComputeResourcePoolParams): BaseResponseType<any> {
  const {workspaceId} = params;

  // 实际 API 调用
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/pools`,
    method: 'POST',
    data: params
  });
}
interface GetImageVersionsParams {
  engines: string;
}
interface GetImageVersionsResult {
  total: number;
  images: {
    mirrorVersion: string;
    rayVersion: string;
    pythonVersion: string;
    description: string;
    imageName: string;
    engine: string;
  }[];
}
export function getImageVersions(params: GetImageVersionsParams): BaseResponseType<GetImageVersionsResult> {
  // return Promise.resolve({
  //   success: true,
  //   status: 200,
  //   result: {
  //     total: 1,
  //     images: [
  //       {
  //         mirrorVersion: '1.0.0',
  //         rayVersion: '1.0.0',
  //         pythonVersion: '3.8',
  //         description: 'test',
  //         imageName: 'test'
  //       }
  //     ]
  //   }
  // });
  return request({
    url: `${urlPrefix}/compute/images`,
    method: 'GET',
    params
  });
}

export interface GetComputeResourcePoolDetailResult {
  chargeType: string;
  accountId: string;
  availableZone: string;
  clusterType: string;
  createdAt: string;
  creator: string;
  instanceCount: string;
  name: string;
  nodeCount: string;
  region: string;
  resourcePoolId: string;
  runtime: string;
  status: ComputeStatus;
  vpcId: string;
  subnetId: string;
  unusedNodeCount: string;
  updatedAt: string;
  usedNodeCount: string;
  mirrorVersion: string;
  workspaceId: string;
  privileges: string[];
}

export function getComputeResourcePoolDetail(params: {
  workspaceId: string;
  resourcePoolId: string;
}): BaseResponseType<GetComputeResourcePoolDetailResult> {
  // return Promise.resolve({
  //   success: true,
  //   status: 200,
  //   result: {
  //     chargeType: 'Postpaid',
  //     accountId: 'test',
  //     availableZone: 'zoneA',
  //     clusterType: 'small',
  //     createdAt: '2025-06-04T09:21:24.000Z',
  //     creator: 'zhuwei11',
  //     instanceCount: '0',
  //     name: 'test',
  //     nodeCount: '1',
  //     region: 'bj',
  //     resourcePoolId: 'test',
  //     runtime: 'python3.8',
  //     status: 'DEPLOY',
  //     subnetId: 'acc1a862-822b-4add-a57e-94d057e5b780',
  //     unusedNodeCount: '0',
  //     usedNodeCount: '0',
  //     mirrorVersion: '1.0.0',
  //     vpcId: 'a7405028-d313-4659-b035-a0eeaf20ee64',
  //     updatedAt: '2025-06-04T09:21:24.000Z',
  //     workspaceId: 'test'
  //   }
  // });
  const {workspaceId, resourcePoolId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/pools/${resourcePoolId}`,
    method: 'GET'
  });
}

export interface GetResourcePoolAssociatedComputesParams {
  computeType?: string;
  engine?: string;
  order?: 'asc' | 'desc';
  orderBy?: string;
  pageNo?: number;
  pageSize?: number;
  status?: ComputeStatus;
  [property: string]: any;
}
export interface GetResourcePoolAssociatedComputesResult {
  instances?: Compute[];
  total?: number;
  [property: string]: any;
}

export interface Compute {
  accountId?: string;
  chargeType?: string;
  clusterType?: string;
  computeId?: string;
  computeType: string;
  createdAt: string;
  creator: string;
  engine?: string;
  mirrorVersion: string;
  name: string;
  nodeCount: string;
  region?: string;
  runtime?: string;
  status: ComputeStatus;
  updatedAt?: string;
  workspaceId: string;
  [property: string]: any;
}
export function getResourcePoolAssociatedComputes(
  params: GetResourcePoolAssociatedComputesParams
): BaseResponseType<GetResourcePoolAssociatedComputesResult> {
  // console.log(params);
  // const computeTemplate = {
  //   computeId: (i: number) => generateId('compute-'),
  //   name: (i: number) => `compute-${i + 1}`,
  //   workspaceId: 'workspace-123',
  //   status: () => randomPick(['DEPLOY', 'RUNNING', 'INVALID']),
  //   createdAt: () => generateDate(),
  //   creator: 'zhuwei11',
  //   availableZone: () => randomPick(['zoneA', 'zoneB', 'zoneC']),
  //   chargeType: 'Postpaid',
  //   clusterType: () => randomPick(['small', 'medium', 'large']),
  //   computeType: () => randomPick(['RAY', 'SPARK']),
  //   engine: () => randomPick(['ray', 'spark']),
  //   mirrorVersion: () => randomPick(['1.0.0', '2.0.0']),
  //   nodeCount: () => String(Math.floor(Math.random() * 5)),
  //   region: 'bj',
  //   runtime: 'python3.8',
  //   subnetId: 'subnet-123',
  //   unusedNodeCount: '0',
  //   usedNodeCount: '0',
  //   updatedAt: () => generateDate()
  // };
  // const mockComputes = generateMockList<Compute>(35, computeTemplate);
  // return mockListResponse<Compute>(mockComputes, params, 'instances');

  const {workspaceId, resourcePoolId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/associatedComputes/${resourcePoolId}`,
    method: 'GET',
    params
  });
}

export function computeScaleUp(params: {workspaceId: string; computeId: string; totalCount: number}) {
  const {workspaceId, computeId, totalCount} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instances/${computeId}/scaleUp`,
    method: 'POST',
    data: {
      totalCount
    }
  });
}
