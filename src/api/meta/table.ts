/*
 * @Author: hong<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2025-07-17 14:18:27
 * @LastEditTime: 2025-08-18 16:44:16
 */

export enum TableHanleModel {
  CREATE = 'create',
  EDIT = 'edit'
}

export enum IcebergFieldTypes {
  STRING = 'STRING',
  FIXED = 'FIXED',
  INT = 'INT',
  LONG = 'LONG',
  FLOAT = 'FLOAT',
  DOUBLE = 'DOUBLE',
  DECIMAL = 'DECIMAL',
  BOOLEAN = 'BOOLEAN',
  BINARY = 'BINARY',
  DATE = 'DATE',
  TIME = 'TIME',
  TIMESTAMP = 'TIMESTAMP',
  TIMESTAMPTZ = 'TIMESTAMPTZ',
  LIST = 'LIST',
  MAP = 'MAP',
  STRUCT = 'STRUCT'
}

// 数据表字段类型
export const IcebergTableFieldTypeMap = {
  [IcebergFieldTypes.STRING]: 'STRING',
  [IcebergFieldTypes.FIXED]: 'FIXED',
  [IcebergFieldTypes.INT]: 'INT',
  [IcebergFieldTypes.LONG]: 'LONG',
  [IcebergFieldTypes.FLOAT]: 'FLOAT',
  [IcebergFieldTypes.DOUBLE]: 'DOUBLE',
  [IcebergFieldTypes.DECIMAL]: 'DECIMAL',
  [IcebergFieldTypes.BOOLEAN]: 'BOOLEAN',
  [IcebergFieldTypes.BINARY]: 'BINARY',
  [IcebergFieldTypes.DATE]: 'DATE',
  [IcebergFieldTypes.TIMESTAMP]: 'TIMESTAMP',
  [IcebergFieldTypes.TIMESTAMPTZ]: 'TIMESTAMPTZ',
  [IcebergFieldTypes.LIST]: 'LIST',
  [IcebergFieldTypes.MAP]: 'MAP',
  [IcebergFieldTypes.STRUCT]: 'STRUCT',
};

export enum IcebergPartFuncTypes {
  identity = 'identity',
  bucket = 'bucket',
  truncate = 'truncate',
  year = 'year',
  month = 'month',
  day = 'day',
  hour = 'hour'
}

export const IcebergTablePartitionFunc = {
  [IcebergPartFuncTypes.identity]: [...Object.keys(IcebergFieldTypes)],
  [IcebergPartFuncTypes.bucket]: [
    IcebergFieldTypes.INT,
    IcebergFieldTypes.LONG,
    IcebergFieldTypes.DECIMAL,
    IcebergFieldTypes.DATE,
    IcebergFieldTypes.TIME,
    IcebergFieldTypes.TIMESTAMP,
    IcebergFieldTypes.TIMESTAMPTZ,
    IcebergFieldTypes.STRING,
    IcebergFieldTypes.FIXED,
    IcebergFieldTypes.BINARY
  ],
  [IcebergPartFuncTypes.truncate]: [
    IcebergFieldTypes.INT,
    IcebergFieldTypes.LONG,
    IcebergFieldTypes.DECIMAL,
    IcebergFieldTypes.STRING
  ],
  [IcebergPartFuncTypes.year]: [
    IcebergFieldTypes.DATE,
    IcebergFieldTypes.TIMESTAMP,
    IcebergFieldTypes.TIMESTAMPTZ
  ],
  [IcebergPartFuncTypes.month]: [
    IcebergFieldTypes.DATE,
    IcebergFieldTypes.TIMESTAMP,
    IcebergFieldTypes.TIMESTAMPTZ
  ],
  [IcebergPartFuncTypes.day]: [
    IcebergFieldTypes.DATE,
    IcebergFieldTypes.TIMESTAMP,
    IcebergFieldTypes.TIMESTAMPTZ
  ],
  [IcebergPartFuncTypes.hour]: [
    IcebergFieldTypes.DATE,
    IcebergFieldTypes.TIMESTAMP,
    IcebergFieldTypes.TIMESTAMPTZ
  ]
}

// Doris 字段类型
export enum DorisFieldTypes {
  CHAR = 'CHAR',
  VARCHAR = 'VARCHAR',
  STRING = 'STRING',
  TINYINT = 'TINYINT',
  SMALLINT = 'SMALLINT',
  INT = 'INT',
  BIGINT = 'BIGINT',
  LARGEINT = 'LARGEINT',
  FLOAT = 'FLOAT',
  DOUBLE = 'DOUBLE',
  DECIMAL = 'DECIMAL',
  BOOLEAN = 'BOOLEAN',
  DATE = 'DATE',
  DATETIME = 'DATETIME',
  ARRAY = 'ARRAY',
  MAP = 'MAP',
  STRUCT = 'STRUCT',
  JSON = 'JSON',
  VARIANT = 'VARIANT',
  HLL = 'HLL',
  BITMAP = 'BITMAP',
  QUANTILE_STATE = 'QUANTILE_STATE',
  AGG_STATE = 'AGG_STATE',
  IPv4 = 'IPv4',
  IPv6 = 'IPv6',
}

// Doris 数据表字段类型
export const DorisTableFieldTypeMap = {
  [DorisFieldTypes.CHAR]: 'CHAR',
  [DorisFieldTypes.VARCHAR]: 'VARCHAR',
  [DorisFieldTypes.STRING]: 'STRING',
  [DorisFieldTypes.TINYINT]: 'TINYINT',
  [DorisFieldTypes.SMALLINT]: 'SMALLINT',
  [DorisFieldTypes.INT]: 'INT',
  [DorisFieldTypes.BIGINT]: 'BIGINT',
  [DorisFieldTypes.LARGEINT]: 'LARGEINT',
  [DorisFieldTypes.FLOAT]: 'FLOAT',
  [DorisFieldTypes.DOUBLE]: 'DOUBLE',
  [DorisFieldTypes.DECIMAL]: 'DECIMAL',
  [DorisFieldTypes.BOOLEAN]: 'BOOLEAN',
  [DorisFieldTypes.DATE]: 'DATE',
  [DorisFieldTypes.DATETIME]: 'DATETIME',
  [DorisFieldTypes.ARRAY]: 'ARRAY',
  [DorisFieldTypes.MAP]: 'MAP',
  [DorisFieldTypes.STRUCT]: 'STRUCT',
  [DorisFieldTypes.JSON]: 'JSON',
  [DorisFieldTypes.VARIANT]: 'VARIANT',
  [DorisFieldTypes.HLL]: 'HLL',
  [DorisFieldTypes.BITMAP]: 'BITMAP',
  [DorisFieldTypes.QUANTILE_STATE]: 'QUANTILE_STATE',
  [DorisFieldTypes.AGG_STATE]: 'AGG_STATE',
  [DorisFieldTypes.IPv4]: 'IPv4',
  [DorisFieldTypes.IPv6]: 'IPv6'
};

// Doris 聚合函数
export const DorisAggFuncMap = {
  SUM: 'SUM',
  REPLACE: 'REPLACE',
  MAX: 'MAX',
  MIN: 'MIN',
  REPLACE_IF_NOT_NULL: 'REPLACE_IF_NOT_NULL',
  HLL_UNION: 'HLL_UNION',
  BITMAP_UNION: 'BITMAP_UNION',
  QUANTILE_UNION: 'QUANTILE_UNION',
  GENERIC: 'GENERIC',
}


export enum ETableDoris {
  DUPLICATE = 'DUPLICATE_KEY',
  UNIQUE = 'UNIQUE_KEY',
  AGGREGATE = 'AGGREGATE_KEY',
}

export const DorisTableTypeMap = {
  [ETableDoris.DUPLICATE]: 'DUPLICATE 明细模型',
  [ETableDoris.UNIQUE]: 'UNIQUE 唯一模型',
  [ETableDoris.AGGREGATE]: 'AGGREGATE 聚合模型'
};
