export enum RoleType {
  User = 'USER',
  System = 'SYSTEM'
}

// 资源类型
export enum ResourceType {
  // 空间管理
  Workspace = 'WORKSPACE',
  // 元数据-元存储
  Metastore = 'METASTORE',
  // 资源池
  ResourcePool = 'RESOURCE_POOL',
  // 工作流
  Workflow = 'WORKFLOW',
  // 工作区-文件夹
  Directory = 'DIRECTORY',
  // 工作区-文件
  File = 'FILE',
  // 工作区-notebook
  Notebook = 'NOTEBOOK',
  // 数据源
  Connection = 'CONNECTION',
  // 目录
  Catalog = 'CATALOG',
  // 模式
  Schema = 'SCHEMA',
  // 表
  Table = 'TABLE',
  // 卷
  Volume = 'VOLUME',
  // 数据集
  Dataset = 'DATASET',
  // 模型
  Model = 'MODEL',
  // 算子
  Operator = 'OPERATOR',
  // 非结构化集成
  UnstructuredIntegration = 'UNSTRUCTURED_INTEGRATION',
  // 结构化集成
  StructuredIntegration = 'STRUCTURED_INTEGRATION',
  // 集成计算资源
  IntegrationCompute = 'INTEGRATION_COMPUTE',
  // 常驻实例计算资源
  EtlCompute = 'ETL_COMPUTE',
  // 任务实例模板
  EtlJobTemplate = 'ETL_JOB_TEMPLATE',
  // 分析计算资源
  AnalysisCompute = 'ANALYSIS_COMPUTE'
}

// 权限点
export enum Privilege {
  All = 'ALL',
  // 管理
  Manage = 'MANAGE',
  // 执行
  Execute = 'EXECUTE',
  // 查看
  View = 'VIEW',
  // 修改
  Modify = 'MODIFY',
  // 使用
  Use = 'USE',
  // 管理
  FullControl = 'FULL_CONTROL',
  // 功能相关权限点
  // 空间管理-菜单权限
  WorkspacesMenu = 'WORKSPACES_MENU',
  // 按钮-新建
  WorkspaceCreate = 'WORKSPACE_CREATE',
  // 元数据-菜单权限 (空间外)
  MetastoreMenu = 'METASTORE_MENU',
  // 按钮-创建
  MetastoreCreate = 'METASTORE_CREATE',
  // 工作区-菜单权限
  WorkspaceMenu = 'WORKSPACE_MENU',
  // 按钮-新建文件夹
  DirCreate = 'DIRECTORY_CREATE',
  // 按钮-导入文件
  FileImport = 'FILE_IMPORT',
  // 按钮-新建Notebook
  NotebookCreate = 'NOTEBOOK_CREATE',
  // 元数据-菜单权限（空间内）
  CatalogMenu = 'CATALOG_MENU',
  // 计算资源-菜单权限
  ComputeMenu = 'COMPUTE_MENU',
  // 源连接与集成 - 新建
  IntegrationComputeCreate = 'INTEGRATION_COMPUTE_CREATE',
  // 数据处理 - 新建常驻实例
  EtlComputeCreate = 'ETL_COMPUTE_CREATE',
  // 数据处理 - 新建任务实例模版
  EtlJobTemplateCreate = 'ETL_JOB_TEMPLATE_CREATE',
  // 分析与 AI 搜索 - 新建
  AnalysisComputeCreate = 'ANALYSIS_COMPUTE_CREATE',
  // 按钮-新建（资源池）
  ResourcePoolCreate = 'RESOURCE_POOL_CREATE',
  // 数据集成-菜单权限
  IntegrationMenu = 'INTEGRATION_MENU',
  // 按钮-新建（非结构化集成）
  UnstructuredIntegrationCreate = 'UNSTRUCTURED_INTEGRATION_CREATE',
  // 按钮-批量停止
  UnstructuredIntegrationStop = 'UNSTRUCTURED_INTEGRATION_STOP',
  // 按钮-批量删除
  UnstructuredIntegrationDelete = 'UNSTRUCTURED_INTEGRATION_DELETE',
  // 按钮-批量运行
  UnstructuredIntegrationExecute = 'UNSTRUCTURED_INTEGRATION_EXECUTE',
  // 按钮-新建（结构化集成）
  StructuredIntegrationCreate = 'STRUCTURED_INTEGRATION_CREATE',
  // 按钮-批量运行
  StructuredIntegrationExecute = 'STRUCTURED_INTEGRATION_EXECUTE',
  // 按钮-批量发布
  StructuredIntegrationPublish = 'STRUCTURED_INTEGRATION_PUBLISH',
  // 按钮-批量删除
  StructuredIntegrationDelete = 'STRUCTURED_INTEGRATION_DELETE',
  // 按钮-批量编辑
  StructuredIntegrationModify = 'STRUCTURED_INTEGRATION_MODIFY',
  // 工作流菜单权限
  WorkflowMenu = 'WORKFLOW_MENU',
  // 按钮-新建工作流
  WorkflowCreate = 'WORKFLOW_CREATE',
  // 按钮-导入工作流
  WorkflowImport = 'WORKFLOW_IMPORT',
  // 运行记录-菜单权限
  WorkflowInstanceMenu = 'WORKFLOW_INSTANCE_MENU',
  // 资源权限
  // -----元数据目录-------
  // 创建目录
  CreateCatalog = 'CREATE_CATALOG',
  // 创建数据源
  CreateConnection = 'CREATE_CONNECTION',
  // 查看元数据
  Browse = 'BROWSE',
  // 读卷
  ReadVolume = 'READ_VOLUME',
  // 读表
  ReadTable = 'READ_TABLE',
  // 读数据集
  ReadDataset = 'READ_DATASET',

  // 写卷
  WriteVolume = 'WRITE_VOLUME',
  // 修改表数据
  WriteTable = 'WRITE_TABLE',
  // 写数据集
  WriteDataset = 'WRITE_DATASET',

  // 创建模式
  CreateSchema = 'CREATE_SCHEMA',
  // 创建卷
  CreateVolume = 'CREATE_VOLUME',
  // 创建表
  CreateTable = 'CREATE_TABLE',
  // 创建数据集
  CreateDataset = 'CREATE_DATASET',
  // 创建数据集新版本
  CreateDatasetVersion = 'CREATE_DATASET_VERSION',
  // 创建模型
  CreateModel = 'CREATE_MODEL',
  // 创建模型新版本
  CreateModelVersion = 'CREATE_MODEL_VERSION',
  // 创建算子
  CreateOperator = 'CREATE_OPERATOR',
  // 创建算子新版本
  CreateOperatorVersion = 'CREATE_OPERATOR_VERSION',
  // 使用数据源
  UseConnection = 'USE_CONNECTION'
}

export interface ResourcePrivilege {
  /**
   * 权限点列表
   */
  privileges: Privilege[];
  /**
   * 资源类型
   */
  resourceType: ResourceType;
}

export interface RoleItem {
  /**
   * 创建时间
   */
  createdAt: string;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 角色描述
   */
  description: string;
  /**
   * 角色名
   */
  name: string;
  /**
   * 资源权限点
   */
  privileges: Privilege[];
  /**
   * 角色类型
   */
  type: RoleType;
  id?: string;
}

export interface RoleDetail {
  createdAt: string;
  createdBy: string;
  description: string;
  name: string;
  privileges: Privilege[];
  type: RoleType;
}

export interface CreateRoleReq {
  description: string;
  name: string;
  id?: string;
  privileges: Privilege[];
}

// 主体类型
export enum PrincipalType {
  User = 'USER',
  Group = 'GROUP',
  Role = 'ROLE'
}

export interface Principal {
  id?: string;
  name?: string;
  type: PrincipalType;
}

export interface Role {
  // 授予者
  grantor?: string;
  // 角色名
  name?: string;
}

export interface WorkspaceUserList {
  principalInfos: WorkspaceUser[];
  totalCount: number;
}

export interface WorkspaceUser {
  // 主体
  principal?: Principal;
  // 主体所属角色列表
  roles?: Role[];
  // 授予时间
  createdAt?: string;
}

export interface UpdateWorkspaceUser {
  // 角色名列表
  addRoleNames?: string[];
  removeRoleNames?: string[];
  // 主体
  principal: Principal;
}

/**
 * 继承自哪个资源
 */
export interface InheritedFrom {
  /**
   * 资源ID
   */
  id: string;
  /**
   * 资源名称
   */
  name: string;
  /**
   * 资源类型
   */
  type: ResourceType;
}

export interface ResourcePermissionInfo {
  /*
   * 授予者
   */
  grantor?: string;
  /**
   * 继承自哪个资源
   */
  inheritedFrom?: InheritedFrom;
  /**
   * 主体
   */
  principal?: Principal;
  /**
   * 权限
   */
  privilege?: {
    type: string;
    privilege: Privilege;
  };
}

export enum PrivilegeType {
  Group = 'GROUP',
  // 一般传 SINGLE，全部权限"ALL"传 GROUP
  Single = 'SINGLE'
}

export interface UpdateResourcePermission {
  /**
   * 主体
   */
  principal: Principal;
  /**
   * 待添加的权限
   */
  addPrivileges?: PrivilegeItem[];
  /**
   * 待移除的权限
   */
  removePrivileges?: PrivilegeItem[];
}

export interface PrivilegeItem {
  /**
   * 权限点，全部权限传"ALL"
   */
  privilege: Privilege;
  /**
   * 权限类型，一般传 SINGLE，全部权限"ALL"传 GROUP
   */
  type: PrivilegeType;
}

/**
 * 资源
 */
export interface ResourceItem {
  /**
   * 资源ID
   */
  id: string;
  /**
   * 资源类型
   */
  type: ResourceType;
}

export interface VerifyAuthParams {
  /**
   * 权限列表，多个权限为"或"关系，只要满足一个即返回true
   */
  privileges?: PrivilegeItem[];
  /**
   * 资源
   */
  resource?: ResourceItem;
}
