/** File 类型任务数据源类型 */
export enum FileSourceType {
  FTP = 'FTP',
  SFTP = 'SFTP',
  HDFS = 'HDFS'
}

export enum FtpConnectionMode {
  ActiveLocal = 'ACTIVE_LOCAL',
  PassiveLocal = 'PASSIVE_LOCAL'
}

/** 目的端类型 */
export enum FileSinkType {
  Catalog = 'Catalog'
}

/** 同名文件处理策略 */
export enum DuplicateFileStrategy {
  Overwrite = 'OVERWRITE', // 覆盖同名文件
  Skip = 'SKIP', // 跳过同名文件
  Rename = 'RENAME' // 重命名同名文件
}

/**
 * file的来源配置
 */
export interface FileSourceConfig {
  /** 数据源类型，支持 FTP 或 SFTP */
  sourceType: FileSourceType;

  /** 数据源连接 ID，用于绑定具体 FTP/SFTP 连接信息 */
  sourceConnectionId: string;

  /** 文件路径，例如 /data/inputs/ */
  sourceFilePath: string;

  /** 正则路径过滤，用于过滤匹配的文件路径（如 .*\.csv） */
  sourceFileFilterPattern: string;

  /** 文件扩展名过滤（如 .csv、.txt），用于按格式筛选 */
  sourceFileNameExtension: string;

  /** 文件更新时间过滤的起始时间（ISO 格式字符串） */
  sourceFileFilterModMin: string;

  /** 文件更新时间过滤的结束时间（ISO 格式字符串） */
  sourceFileFilterModMax: string;

  /** FTP 连接模式：ACTIVE_LOCAL（主动）或 PASSIVE_LOCAL（被动） */
  sourceConnectionMode?: FtpConnectionMode;

  /**
   * 并行度，表示同时处理的线程/任务数，默认值为 1，最大值为 6。
   * 超出并发度可能对性能或服务器连接数产生影响。
   */
  parallelism: number;
}

/**
 * 文件类任务的目的端配置
 */
export interface FileSinkConfig {
  /** 目的端类型，目前仅支持 Catalog */
  sinkType: FileSinkType;

  /** 文件写入路径，例如 /output/data/ */
  sinkVolume: string;

  /**
   * 同名文件处理策略：
   * - OVERWRITE：覆盖已有文件
   * - SKIP：跳过已有文件
   * - RENAME：自动重命名新文件（避免冲突）
   */
  duplicateFileStrategy?: DuplicateFileStrategy;
}
