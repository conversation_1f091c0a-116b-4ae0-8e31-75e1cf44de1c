import {QueryJobListRes} from './type';
import {
  CdcJobType,
  CdcJobStatus,
  CdcSubStatus,
  QueryCdcListExecutionRes,
  CdcBaseStatus,
  CdcIncrementStatus,
  DmlDeleteStrategy
} from './cdc-type';
// Mock 实时列表接口返回
export const mockCDCJobList = Array.from({length: 15}, (_, i) => ({
  cdcType: i % 2 === 0 ? CdcJobType.BaseIncrement : CdcJobType.Increment,
  createTime: '2025-07-16 07:05:22',
  creatorName: `creator_${i + 1}`,
  jobId: `job_${i + 1}`,
  latency: Math.floor(Math.random() * 1000),
  name: `Mock_Job_Mock_Job_Mock_${i + 1}`,
  runtime: Math.floor(Math.random() * 10000),
  sinkDatabase: `sink_db_${i + 1}`,
  sourceConnectionId: `source_conn_${i + 1}`,
  sourceDatabase: `source_db_${i + 1}`,
  sourceType: `source_type_${i + 1}`,
  status: Object.values(CdcJobStatus)[i % Object.values(CdcJobStatus).length],
  subStatus: {
    base: i % 2 === 0 ? CdcSubStatus.Closed : undefined,
    increment: Object.values(CdcSubStatus)[i % Object.values(CdcSubStatus).length]
  },
  updaterName: `updater_${i + 1}`,
  updateTime: '2025-07-16 07:05:22'
}));
export const mockCDCJobListRes: QueryJobListRes = {
  pageNo: 1,
  pageSize: 15,
  totalCount: 15,
  jobs: mockCDCJobList
};
// Mock 实时详情接口返回
export const mockCdcJobDetail = {
  cdcType: 'BASE_INCREMENT',
  commonConfig: {
    addedSinkFields: [
      {
        comment: '新增字段说明',
        defaultValue: 'default',
        name: 'new_field',
        precision: 0,
        scale: 0,
        type: 'string'
      }
    ]
  },
  compute: {
    name: 'compute_name',
    computeId: 'compute_id'
  },
  createTime: '2025-07-21T14:30:00Z',
  creatorId: 'user-001',
  creatorName: '张三',
  description: '这是一条测试任务描述',
  mappingConfig: {
    tableConfigs: [
      {
        createTableStatement: 'CREATE TABLE test(id INT PRIMARY KEY, name VARCHAR(50))',
        dmlConfig: {
          delete: 'DELETE',
          insert: 'INSERT',
          logicalDeleteField: 'logical_delete_tag',
          update: 'UPDATE'
        },
        isAutoCreated: true,
        isTableCustomConfig: false,
        mapping: [
          {
            sinkColumn: 'name',
            sourceColumn: 'username'
          }
        ],
        sinkFields: [
          {
            comment: '主键ID',
            defaultValue: '0',
            function: 'none',
            functionParameter: '',
            isPrimaryKey: true,
            name: 'id',
            precision: 0,
            scale: 0,
            type: 'int'
          }
        ],
        sinkPartitions: [],
        sinkTableComment: '示例目标表',
        sinkTableName: 'catalog.schema.test',
        sourceSchema: 'public',
        sourceTable: 'user'
      }
    ]
  },
  name: '测试同步任务',
  sinkConfig: {
    dirtyDataStrategy: {
      dirtyDataVolume: '100MB',
      enableDirtyDataWrite: true,
      maxDirtyRatio: '0.01',
      maxDirtyRow: 1000,
      strategy: 'IGNORE'
    },
    dmlConfig: {
      delete: DmlDeleteStrategy.Delete,
      insert: DmlDeleteStrategy.LogicalDelete,
      logicalDeleteField: 'logical_delete_tag',
      update: DmlDeleteStrategy.Skip
    },
    isAutoCreated: true,
    prefix: 'pre_',
    sinkMode: 'upsert',
    sinkNameRule: 'ADD_PREFIX',
    sinkPath: 'catalog.schema',
    sinkTableType: 'MANAGED',
    sinkType: 'Doris',
    suffix: '_suf',
    target: 'Catalog'
  },
  sourceConfig: {
    enableRateLimit: true,
    incrementPosition: {
      position: '2025-07-20T00:00:00Z',
      type: 'timestamp'
    },
    parallelism: 2,
    rateLimit: {
      flow: 10,
      records: 1000
    },
    sourceChange: {
      onAddColumn: 'SKIP',
      onChangeColumn: 'SYNC',
      onChangeColumnComment: 'SYNC',
      onChangeTableComment: 'SYNC',
      onDeleteColumn: 'SKIP',
      onDeleteSource: 'PAUSE',
      onRenameColumn: 'SYNC',
      onRenameTable: 'PAUSE',
      onTruncateTable: 'SKIP'
    },
    sourceConnectionId: 'conn-123',
    sourceDatabase: 'test_db',
    sourceType: 'MySQL'
  },
  status: 'DRAFT',
  type: 'cdc',
  workspaceId: 'workspace-123'
};
export const mockExecutionDetail: QueryCdcListExecutionRes = {
  base: Array.from({length: 8}, (_, index) => ({
    finishTime: `2024-07-22T10:0${index}:00Z`,
    readCount: `${(index + 1) * 1000}`,
    sinkTable: `sink_table_${index}`,
    sinkTablePath: `catalog.schema.sink_table_${index}`,
    sourceSchema: `public`,
    sourceTable: `source_table_${index}`,
    startTime: `2024-07-22T09:0${index}:00Z`,
    status: index === 2 ? CdcBaseStatus.Closed : index === 1 ? CdcBaseStatus.Failed : CdcBaseStatus.Running,
    syncProgress: `${((index + 1) * 30) % 101}`,
    writeCount: `${(index + 1) * 950}`
  })),
  increment: Array.from({length: 10}, (_, index) => ({
    dirtyBytes: (index + 1) * 1024,
    dirtyCount: (index + 1) * 5,
    readBytes: (index + 1) * 20480,
    readCount: (index + 1) * 5000,
    sourceSchema: `public`,
    sourceTable: `increment_table_${index}`,
    sinkTable: `sink_table_${index}`,
    sinkTablePath: `catalog.schema.sink_table_${index}`,
    status: index === 1 ? CdcIncrementStatus.Running : CdcIncrementStatus.Stopped,
    stopMessage: 'Manual stop',
    stopTime: `2024-07-22T11:0${index}:00Z`,
    writeBytes: (index + 1) * 18000,
    writeCount: (index + 1) * 4800
  }))
};
