import {API_PREFIX, Request_Method} from '@api/common';
import {BaseResponseType, request} from '@api/apiFunction';
import {
  SinkNameRuleEnum,
  SinkTableTypeEnum,
  SinkTypeEnum,
  SourceChangeHandleEnum,
  TargetTypeEnum
} from '@pages/DataIntegration/SqlCollect/constants';
import {JobType} from './type';
import {Privilege} from '@api/permission/type';
const DATASOURCE_API_PREFIX = '/connection';

// 获取数据库
export interface BatchReqDatabases {
  environment: {
    workspaceId: string;
    computeId: string;
  };
  datasourceInfo: {
    connectionId: string;
  };
}

// 获取表
export interface BatchReqTables extends BatchReqDatabases {
  datasourceInfo: {
    connectionId: string;
    database: string;
    schema?: string;
  };
}

// 获取表字段
export interface BatchReqTableColumns extends BatchReqDatabases {
  datasourceInfo: {
    connectionId: string;
    database: string;
    table: string;
    schema?: string;
  };
}

// 表字段
export interface BatchTableColumns {
  // 前端使用 id 字段标识数据
  id?: string;
  name: string;
  type: string;
  defaultValue: string;
  nullable: boolean;
  description: string;
  precision: number;
  dbType: string;
  scale: number;
  isPrimaryKey: boolean;
  primaryKey?: boolean;
  function: string;
  functionParameter: string;
  comment: string;
}

export interface RateLimit {
  records: number;
  flow: number;
}

/** 源端变更 */
export interface SourceChange {
  onDeleteSource: SourceChangeHandleEnum.PAUSE;
  onDeleteColumn: SourceChangeHandleEnum;
  onAddColumn: SourceChangeHandleEnum;
}

/** 脏数据策略 */
export interface DirtyDataStrategy {
  strategy: string;
  maxDirtyRatio: number;
  maxDirtyRow: number;
  enableDirtyDataWrite: boolean;
  dirtyDataVolume: string;
}

/** 源端配置 */
export interface BatchSourceConfig {
  sourceType: string;
  sourceConnectionId: string;
  sourceDatabase: string;
  sourceTable: string;
  sourceSchema?: string;
  enableRateLimit: boolean;
  rateLimit: RateLimit;
  parallelism: number;
  splitField: string;
  sourceChange: SourceChange;
}

/** 目标端配置 */
export interface BatchSinkConfig {
  target: TargetTypeEnum;
  sinkType: SinkTypeEnum;
  isAutoCreated: boolean;
  sinkTableType: SinkTableTypeEnum;
  sinkNameRule: SinkNameRuleEnum;
  prefix?: string;
  suffix?: string;
  sinkPath: string;
  comment: string;
  sinkMode: string;
  dirtyDataStrategy: DirtyDataStrategy;
}

export interface BatchMappingConfig {
  mapping: SourceMapping[];
  filter: string;
  sinkFields: BatchTableColumns[];
  sinkPartitions: BatchTableColumns[];
  sinkTableComment: string;
  createTableStatement: string;
}

// 集成任务对象
export interface BatchObj {
  jobId?: string;
  workspaceId: string;
  name: string;
  type: JobType;
  description: string;
  sinkFullName?: string;
  compute: {
    computeId: string;
    name: string;
  };
  // 源端表配置
  sourceTable?: BatchTableColumns[];
  sourceConfig: BatchSourceConfig;
  sinkConfig: BatchSinkConfig;
  mappingConfig?: BatchMappingConfig;
}

// 映射配置
export interface SourceMapping {
  sourceColumn: string;
  sinkColumn: string;
}

// 分区配置
export interface Partitioning {
  name: string;
  function: string;
  functionParameter: string;
  id?: string;
}

/**
 * 获取所有数据库
 */
export function getDatasourceDatabases(
  params: BatchReqDatabases,
  privileges?: Privilege
): BaseResponseType<{databases: string[]}> {
  return request({
    url: `${API_PREFIX}${DATASOURCE_API_PREFIX}/databases`,
    method: Request_Method.Post,
    data: {...params, privileges}
  });
}

/**
 * 获取所有表
 */

export function getDatasourceTables(params: BatchReqTables): BaseResponseType<{tables: string[]}> {
  return request({
    url: `${API_PREFIX}${DATASOURCE_API_PREFIX}/tables`,
    method: Request_Method.Post,
    data: params
  });
}

// 表字段
export interface BatchTableConfig {
  columns: BatchTableColumns[];
  description: string;
  relations: {
    primaryKeyTableName: string;
    primaryKeyColumnName: string;
    foreignKeyTableName: string;
    foreignKeyColumnName: string;
  }[];
  primaryKey: string[];
  uniqueKeys: {
    name: string;
    fields: string[];
  }[];
  tableName: string;
  tableType: string;
  tableProperty: any;
}

/**
 * 获取所有表
 */

export function getDatasourceTableColumns(params: BatchReqTableColumns): BaseResponseType<BatchTableConfig> {
  return request({
    url: `${API_PREFIX}${DATASOURCE_API_PREFIX}/table/schema`,
    method: Request_Method.Post,
    data: params
  });
}
