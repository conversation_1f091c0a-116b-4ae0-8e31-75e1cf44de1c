import {FieldType} from './type';

// CDC 子类型
export enum CdcJobType {
  /** 全量+增量 */
  BaseIncrement = 'BASE_INCREMENT',
  /** 增量 */
  Increment = 'INCREMENT'
}

// DML 删除策略
export enum DmlDeleteStrategy {
  /** 按 DELETE 同步 */
  Delete = 'DELETE',
  /** 忽略删除操作 */
  Skip = 'SKIP',
  /** 逻辑删除 */
  LogicalDelete = 'LOGICAL_DELETE',
  /** 忽略该操作 */
  Ignore = 'IGNORE'
}

// DML 更新策略
export enum DmlUpdateStrategy {
  /** 按 UPDATE 同步 */
  Update = 'UPDATE',
  /** 忽略该操作 */
  Skip = 'SKIP',
  /** 忽略该操作 */
  Ignore = 'IGNORE'
}

// DML 插入策略
export enum DmlInsertStrategy {
  /** 按 INSERT 同步 */
  Insert = 'INSERT',
  /** 忽略该操作 */
  Skip = 'SKIP',
  /** 忽略该操作 */
  Ignore = 'IGNORE'
}

// 表名生成规则
export enum SinkNameRule {
  /** 和源端名称保持一致 */
  Same = 'SAME',
  /** 增加前缀 */
  AddPrefix = 'ADD_PREFIX',
  /** 增加后缀 */
  AddSuffix = 'ADD_SUFFIX',
  /** 增加前缀和后缀 */
  AddPrefixAndSuffix = 'ADD_PREFIX_AND_SUFFIX'
}

// 目标端写入模式
export enum SinkWriteMode {
  /** 覆盖写入 */
  Overwrite = 'overwrite',
  /** 追加写入 */
  Append = 'append',
  /** 根据设置主键字段进行数据更新写入 */
  Upsert = 'upsert'
}

// 目标端表类型
export enum SinkTableType {
  /** 内部管理表 */
  Managed = 'MANAGED',
  /** 外部表 */
  External = 'EXTERNAL'
}

// 目标端类型
export enum SinkType {
  /** Iceberg 数据湖 */
  Iceberg = 'iceberg',
  /** Doris 数据库 */
  Doris = 'Doris'
}

// 位点类型
export enum IncrementPositionType {
  /** 时间戳类型位点 */
  Timestamp = 'timestamp'
}

// 源表结构变更策略
export enum SourceChangeStrategy {
  /** 跳过该变更 */
  Skip = 'SKIP',
  /** 暂停任务 */
  Pause = 'PAUSE',
  /** 同步该变更 */
  Sync = 'SYNC'
}

export enum SinkConfigTarget {
  Catalog = 'CATALOG'
}

// 脏数据策略
export enum DirtyDataStrategyEnum {
  // 不容忍
  Strict = 'STRICT',
  // 容忍部分
  Tolerant = 'TOLERANT',
  // 忽略
  Ignore = 'IGNORE'
}

export enum SinkModeEnum {
  Overwrite = 'overwrite',
  Append = 'append',
  Upsert = 'upsert'
}

export enum SourceTypeEnum {
  /** MySQL 数据库 */
  MySQL = 'MySQL',
  /** Oracle 数据库 */
  Oracle = 'Oracle',
  /** SQL Server 数据库 */
  SQLServer = 'SQLServer',
  /** PostgreSQL 数据库 */
  PostgreSQL = 'PostgreSQL'
}

/**
 * 资源配置
 *
 * Compute
 */
export interface Compute {
  computeId: string;
  name: string;
  [property: string]: any;
}

/**
 * 任务映射配置
 *
 * CdcMappingConfig
 *
 * CdcTableConfig
 */
export interface CdcTableConfig {
  /**
   * 自动建表语句，仅目标端为Doris时，且isAutoCreated为true时，值有效
   */
  createTableStatement: string;
  /**
   * DML语句处理策略
   */
  dmlConfig: DmlConfig;
  /**
   * 是否自动建表
   */
  isAutoCreated: boolean | number;
  /**
   * 指定表是否开启个性化配置
   */
  isTableCustomConfig: boolean;
  /**
   * 字段映射
   */
  mapping?: SourceMapping[];
  /**
   * 目的端普通字段
   */
  sinkFields: SinkField[];
  /**
   * 目的端分区字段
   */
  sinkPartitions: SinkField[];
  /**
   * 目的表注释
   */
  sinkTableComment: string;
  /**
   * 目标端表名，1. 创建任务阶段含义：已有表表名，自动建表时为空 2. 详情页配置中含义：如果表已创建，则返回catalog.schema.table；如果表未创建，则返回空
   */
  sinkTableName: string;
  /**
   * 源端模式名，仅源端为SQLServer、PostgreSQL、HANA 时有值
   */
  sourceSchema?: string;
  /**
   * 源端表名
   */
  sourceTable: string;
  [property: string]: any;
}

/**
 * DML语句处理策略
 *
 * DmlConfig
 */
export interface DmlConfig {
  /**
   * 源端表删除数据的处理策略，{DELETE, SKIP, LOGICAL_DELETE}，IGNORE：忽略
   * DELETE：按DELETE同步
   * LOGICAL_DELETE：逻辑删除
   */
  delete: DmlDeleteStrategy;
  /**
   * 源端表插入数据的处理策略，{INSERT, SKIP}，IGNORE：忽略
   * INSERT：按INSERT同步
   */
  insert: DmlInsertStrategy;
  /**
   * 逻辑删除时使用的列名，固定传logical_delete_tag
   */
  logicalDeleteField: string;
  /**
   * 源端表更新数据的处理策略，{UPDATE, SKIP}，IGNORE：忽略
   * UPDATE：按UPDATE同步
   */
  update: DmlUpdateStrategy;
  [property: string]: any;
}

/**
 * SourceMapping
 */
export interface SourceMapping {
  sinkColumn: string;
  sourceColumn: string;
  [property: string]: any;
}

export enum PartitionFunction {
  Year = 'year',
  Month = 'month',
  Day = 'day',
  Hour = 'hour',
  Bucket = 'bucket',
  Truncate = 'truncate',
  Identity = 'identity'
}

/**
 * SinkField
 */
export interface SinkField {
  comment: string;
  defaultValue: string;
  function: PartitionFunction;
  functionParameter: string;
  isPrimaryKey: boolean;
  name: string;
  precision: number;
  scale: number;
  type: FieldType;
  [property: string]: any;
}

/**
 * 任务目标端配置
 *
 * CdcSinkConfig
 */
export interface CdcSinkConfig {
  /**
   * 脏数据处理
   */
  dirtyDataStrategy?: DirtyDataStrategy;
  /**
   * DML语句处理策略
   */
  dmlConfig?: DmlConfig;
  /**
   * 是否自动建表
   */
  isAutoCreated?: boolean | number;
  /**
   * 表名前缀, sinkNameRule为 ADD_PREFIX 或 ADD_PREFIX_AND_SUFFIX时存在
   */
  prefix?: string;
  /**
   * 目的端写入模式 overwrite：覆盖写入, append：追加写入, upsert：根据设置主键字段进行数据更新写入
   */
  sinkMode?: SinkModeEnum;
  // TODO: 待确定
  sinkName: string;
  /**
   * 表名设置, 根据此规则创建目标表，取值为以下几种之一：SAME: 和源端名称保持一致；ADD_PREFIX:增加前缀，ADD_SUFFIX:
   * 增加后缀；ADD_PREFIX_AND_SUFFIX: 增加前缀和后缀。若此字段不为空，表示自动创建目标表，此字段和sinkName为互斥关系
   */
  sinkNameRule?: SinkNameRule;
  /**
   * 目的端表路径：catalog.schema
   */
  sinkPath?: string;
  /**
   * 表类型, 取值为 {MANAGED, EXTERNAL}
   */
  sinkTableType?: SinkTableType;
  /**
   * 目的端类型，{iceberg, Doris}
   */
  sinkType: SinkType;
  /**
   * 表名后缀, sinkNameRule为 ADD_SUFFIX 或 ADD_PREFIX_AND_SUFFIX时存在
   */
  suffix?: string;
  /**
   * 目的端: {Catalog}
   */
  target: SinkConfigTarget;
  [property: string]: any;
}

/**
 * 脏数据处理
 *
 * DirtyDataStrategy
 */
export interface DirtyDataStrategy {
  // 脏数据存储路径
  dirtyDataVolume: string;
  // 是否写入脏数据，默认否
  enableDirtyDataWrite: boolean;
  // 最大容忍脏数据比例
  maxDirtyRatio: string;
  // 最大容忍脏数据条数
  maxDirtyRow: number;
  // 脏数据策略，{STRICT，TOLERANT，IGNORE}
  strategy: DirtyDataStrategyEnum;
  [property: string]: any;
}

/**
 * 任务源端配置
 *
 * CdcSourceConfig
 */
export interface CdcSourceConfig {
  /**
   * 是否开启限速
   */
  enableRateLimit: boolean;
  /**
   * cdc任务增量同步起始点
   */
  incrementPosition: IncrementPosition;
  /**
   * 最大并发数，默认1
   */
  parallelism: number;
  /**
   * 限流流量
   */
  rateLimit: RateLimit;
  /**
   * 源表变化时处理策略
   */
  sourceChange: SourceChange;
  /**
   * 数据源名称
   */
  sourceConnectionId: string;
  /**
   * 源端数据库名称
   */
  sourceDatabase: string;
  /**
   * 源端模式名，仅源端为SQLServer、PostgreSQL、HANA 时有值
   */
  sourceSchema?: string;
  /**
   * 数据源类型 {MySQL、Oracle、SQLServer、PostgreSQL、HANA}
   */
  sourceType: SourceTypeEnum;
}

/**
 * cdc任务增量同步起始点
 *
 * IncrementPosition
 */
export interface IncrementPosition {
  /**
   * 位点数值，2023-01-20T06:50:56Z
   */
  position: string;
  /**
   * 位点类型，{timestamp}，timestamp表示position格式为UTC时区的时间字符串，例如：2023-01-20T06:50:56Z
   */
  type: IncrementPositionType;
}

/**
 * 限流流量
 *
 * RateLimit
 */
export interface RateLimit {
  /**
   * 按流量限速，单位 MB/s
   */
  flow: number;
  /**
   * 按行限速
   */
  records: number;
}

/**
 * 源表变化时处理策略
 *
 * SourceChange
 */
export interface SourceChange {
  /**
   * 源表字段新增时操作，{SKIP，PAUSE}
   */
  onAddColumn: SourceChangeStrategy;
  /**
   * 源端表修改字段类型，{SKIP，PAUSE, SYNC}
   */
  onChangeColumn: SourceChangeStrategy;
  /**
   * 源端表修改字段描述信息，{SKIP，PAUSE, SYNC}
   */
  onChangeColumnComment: SourceChangeStrategy;
  /**
   * 源端表修改表描述信息，{SKIP，PAUSE, SYNC}
   */
  onChangeTableComment: SourceChangeStrategy;
  /**
   * 源表字段被删除时操作，{SKIP，PAUSE}
   */
  onDeleteColumn: SourceChangeStrategy;
  /**
   * 源表删除时操作，{PAUSE}
   */
  onDeleteSource: SourceChangeStrategy;
  /**
   * 源表重命名字段时的操作，{SKIP，PAUSE, SYNC}
   */
  onRenameColumn: SourceChangeStrategy;
  /**
   * 源端表名称重命名时的操作，{SKIP，PAUSE}
   */
  onRenameTable: SourceChangeStrategy;
  /**
   * 源端表清空数据时的操作，{SKIP，PAUSE}
   */
  onTruncateTable: SourceChangeStrategy;
}

/** 实时作业任务状态 */
export enum CdcJobStatus {
  // 草稿
  Draft = 'DRAFT',
  // 等待资源
  Ready = 'READY',
  // 前置检查中
  PreCheck = 'PRECHECK',
  // 前置检查通过
  Checkpass = 'CHECKPASS',
  // 前置检查失败
  Checkfailed = 'CHECKFAILED',
  // 运行中
  Running = 'RUNNING',
  // 暂停中
  Suspending = 'SUSPENDING',
  // 已暂停
  Suspend = 'SUSPEND',
  // 运行失败
  Failed = 'FAILED'
}
/**
 * 实时作业 单个 Job
 */

export interface CdcJob {
  /* 任务类型：BASE_INCREMENT（全量+增量），INCREMENT（增量）*/
  cdcType: CdcJobType;
  /* 创建时间 */
  createTime: string;
  /* 创建人名称 */
  creatorName: string;
  /* 任务id */
  jobId: string;
  /* 数据延迟时间，单位秒，全量迁移阶段返回-1，表示延迟未知 */
  latency: number;
  /* 任务名称 */
  name: string;
  /* 持续运行时长，单位秒 */
  runtime: number;
  /* 目标端数据库 */
  sinkPath: string;
  /* 源链接id */
  sourceConnectionId: string;
  /* 源端数据库 */
  sourceDatabase: string;
  /* 数据源类型 */
  sourceType: string;
  /* 任务状态 */
  status: CdcJobStatus;
  /* 执行概况 */
  subStatus: SubStatus;
  /* 更新人名称 */
  updaterName: string;
  /* 更新时间 */
  updateTime: string;
  /** 权限 */
  privileges?: string[];
}
export enum CdcSubStatus {
  // 未开始
  Queue = 'QUEUE',
  // 运行中
  Running = 'RUNNING',
  // 运行失败
  Failed = 'FAILED',
  // 已完成
  Closed = 'CLOSED',
  // 已暂停
  Suspend = 'SUSPEND',
  // 已终止
  Stopped = 'STOPPED'
}
/* 实时作业 - 执行概况 */
export interface SubStatus {
  /**
   * 全量阶段任务状态，queue 未开始,
   * running 运行中,
   * failed 运行失败,
   * closed 已完成,
   * suspend 已暂停,
   * stopped 已终止（因ddl变化选择终止、任务已失效）
   */
  base?: CdcSubStatus;
  increment: CdcSubStatus;
}
export enum CdcJobStage {
  Base = 'base',
  Increment = 'increment'
}
// 实时作业 - 运行记录接口请求参数
export interface QueryCdcListExecutionsReq {
  /**
   * 排序方式，
   * 1、全量进展：支持降序desc和升序asc两种排序方式
   * 2、实时同步：不支持
   */
  order?: string;
  /**
   * 排序字段，
   * 1、全量进展：支持startTime、finishTime两种
   * 2、实时同步：不支持
   */
  orderBy?: string;
  /**
   * 页码，默认值1
   */
  pageNo: number;
  /**
   * 每页任务数，默认值10
   */
  pageSize: number;
  /**
   * 源端表名称搜索
   */
  sourceTablePattern?: string;
  /**
   * 状态筛选
   */
  status: string;
  /**
   * 任务阶段，取值：
   * 1、全量进展：base
   * 2、实时同步：increment
   */
  stage: CdcJobStage;
}
/** 实时运行记录 - 状态  */
export enum CdcRecordStatus {
  // 未开始
  Queue = 'QUEUE',
  // 运行中
  Running = 'RUNNING',
  // 运行失败
  Failed = 'FAILED',
  // 已完成
  Closed = 'CLOSED',
  // 已终止
  Stopped = 'STOPPED',
  // 已暂停
  Suspend = 'SUSPEND'
}

/**
 * 全量阶段
 */
export interface CdcBaseExecutionDetail {
  /**
   * 结束时间
   */
  finishTime: string;
  /**
   * 读取行数，单位行
   */
  readCount: number;
  /**
   * 目标端表名
   */
  sinkTable: string;
  /**
   * 源端模式名
   */
  sourceSchema: string;
  /**
   * 源端表名
   */
  sourceTable: string;
  /**
   * 开始时间
   */
  startTime: string;

  status: CdcRecordStatus;
  /**
   * 同步进度，百分比数据，单位%
   */
  syncProgress: string;
  /**
   * 写入行数，单位行
   */
  writeCount: number;
}

/**
 * 增量阶段
 */
export interface CdcIncrementExecutionDetail {
  /**
   * 脏数据大小，单位字节
   */
  dirtyBytes: number;
  /**
   * 脏数据行数，单位行
   */
  dirtyCount: number;
  /**
   * 读取大小，单位字节
   */
  readBytes: number;
  /**
   * 读取行数，单位行
   */
  readCount: number;
  /**
   * 源端模式名
   */
  sourceSchema: string;
  /**
   * 源端表名
   */
  sourceTable: string;

  status: CdcRecordStatus;
  /**
   * 终止原因
   */
  stopMessage: string;
  /**
   * 终止时间
   */
  stopTime: string;
  /**
   * 写入大小，单位字节
   */
  writeBytes: number;
  /**
   * 写入行数，单位行
   */
  writeCount: number;
}
// 实时作业 - 运行记录接口返回参数
export interface QueryCdcListExecutionRes {
  [CdcJobStage.Base]?: CdcBaseExecutionDetail[];
  [CdcJobStage.Increment]: CdcIncrementExecutionDetail[];
  runId: string;
  totalCount: number;
}
export interface CdcStartJobReq {
  /**
   * 增量同步起始点，resetIncrementPosition为true时有效
   */
  incrementPosition: IncrementPosition;
  /**
   * 是否重置增量同步位点
   */
  resetIncrementPosition: boolean;
}
