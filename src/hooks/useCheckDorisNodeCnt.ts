import {useContext, useState, useEffect} from 'react';
import {WorkspaceContext} from '@pages/index';
import {getComputeResourceList, Engine} from '@api/Compute';
import {STATUS} from '@pages/Compute/config';
import useUrlState from '@ahooksjs/use-url-state';

// 检查 Doris 节点数量
export default function useCheckDorisNodeCnt() {
  const [isDorisNodeCountLimit, setIsDorisNodeCountLimit] = useState(false);
  const [urlState] = useUrlState();
  async function checkDorisNodeCount() {
    if (!urlState.workspaceId) return;
    const res = await getComputeResourceList({
      workspaceId: urlState.workspaceId,
      engine: Engine.Doris,
      status: `${STATUS.RUNNING},${STATUS.DEPLOY},${STATUS.INVALID}`
    });
    if (res.success) {
      setIsDorisNodeCountLimit(res.result?.computes?.length >= 1);
    }
  }
  useEffect(() => {
    checkDorisNodeCount();
  }, [urlState.workspaceId]);
  return {
    isDorisNodeCountLimit,
    checkDorisNodeCount
  };
}
