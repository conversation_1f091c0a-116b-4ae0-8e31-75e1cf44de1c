import {useState, useEffect, useMemo} from 'react';
import {useRequest} from 'ahooks';

interface UseFrontendTableOptions<T> {
  getTableApi: (params?: any) => Promise<any>;
  listKey?: string;
  handleTableChange?: (pagination: any, filters: any, sorter: any, extra: any) => void;
  sortOrderMap?: {[key: string]: string};
  refreshDeps?: any[];
  extraParams?: any;
  customDataHandler?: (data: any) => void;
  customDataMapper?: (data: any) => any;
  defaultPageSize?: number;
  enableSort?: boolean;
  enableFilter?: boolean;
  disablePagination?: boolean;
}

export default function useFrontendTable<T>({
  getTableApi,
  listKey = 'items',
  handleTableChange: customHandleTableChange = (pagination, filters, sorter, extra) => {},
  sortOrderMap = {ascend: 'asc', descend: 'desc'},
  refreshDeps = [],
  extraParams = {},
  customDataHandler = (data: any) => data,
  customDataMapper = (data: any) => data,
  defaultPageSize = 10,
  enableSort = true,
  enableFilter = true,
  disablePagination = false
}: UseFrontendTableOptions<T>) {
  // 原始数据（从API获取的完整数据）
  const [originalData, setOriginalData] = useState<T[]>([]);
  // 分页状态
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  // 排序状态
  const [sortOrder, setSortOrder] = useState<string | undefined>(undefined);
  const [sortField, setSortField] = useState<string | undefined>(undefined);
  // 筛选状态
  const [filters, setFilters] = useState<{[key: string]: any}>({});
  // 总数
  const [total, setTotal] = useState(0);

  // 获取数据的API调用
  const {loading, runAsync: loadTableData} = useRequest(
    async (params) => {
      return getTableApi({
        ...extraParams,
        ...params
      });
    },
    {
      refreshDeps: [...refreshDeps],
      onSuccess: (res: any) => {
        const data = customDataMapper?.(res) || res;
        const items = data.result?.[listKey] || [];
        setOriginalData(items);
        setTotal(items.length);
        customDataHandler?.(data);
      }
    }
  );

  // 前端数据处理：筛选、排序、分页
  const processedData = useMemo(() => {
    let filteredData = [...originalData];

    // 1. 筛选
    if (enableFilter && Object.keys(filters).length > 0) {
      filteredData = filteredData.filter((item: any) => {
        return Object.entries(filters).every(([key, filterValue]) => {
          if (!filterValue || (Array.isArray(filterValue) && filterValue.length === 0)) {
            return true;
          }

          const itemValue = item[key];
          if (Array.isArray(filterValue)) {
            return filterValue.includes(itemValue);
          }

          // 支持字符串模糊匹配
          if (typeof filterValue === 'string' && typeof itemValue === 'string') {
            return itemValue.toLowerCase().includes(filterValue.toLowerCase());
          }

          return itemValue === filterValue;
        });
      });
    }

    // 2. 排序
    if (enableSort && sortField && sortOrder) {
      filteredData.sort((a: any, b: any) => {
        const aValue = a[sortField];
        const bValue = b[sortField];

        let result = 0;
        if (aValue < bValue) {
          result = -1;
        } else if (aValue > bValue) {
          result = 1;
        }

        return sortOrder === 'desc' ? -result : result;
      });
    }

    // 更新总数（筛选后的）
    const filteredTotal = filteredData.length;

    // 3. 分页（如果启用）
    let finalData = filteredData;
    if (!disablePagination) {
      const startIndex = (pageNo - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      finalData = filteredData.slice(startIndex, endIndex);
    }

    return {
      dataSource: finalData,
      filteredTotal
    };
  }, [
    originalData,
    pageNo,
    pageSize,
    sortField,
    sortOrder,
    filters,
    enableSort,
    enableFilter,
    disablePagination
  ]);

  // 处理表格变化事件
  function handleTableChange(pagination: any, tableFilters: any, sorter: any, extra: any) {
    customHandleTableChange?.(pagination, tableFilters, sorter, extra);

    if (extra?.action === 'paginate' && !disablePagination) {
      setPageNo(pagination.current);
      setPageSize(pagination.pageSize);
    }

    if (extra?.action === 'sort') {
      setSortOrder(sorter.order ? sortOrderMap[sorter.order] : undefined);
      setSortField(sorter.order ? sorter.field : undefined);
    }

    if (extra?.action === 'filter') {
      setFilters(tableFilters);
      // 筛选后重置到第一页
      setPageNo(1);
    }
  }

  // 重置筛选
  const resetFilters = () => {
    setFilters({});
    setPageNo(1);
  };

  // 重置排序
  const resetSort = () => {
    setSortOrder(undefined);
    setSortField(undefined);
  };

  // 重置所有状态
  const resetAll = () => {
    setPageNo(1);
    setPageSize(defaultPageSize);
    resetFilters();
    resetSort();
  };

  // 手动设置筛选条件
  const setFilter = (key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value
    }));
    setPageNo(1);
  };

  // 手动设置排序
  const setSort = (field: string, order: 'asc' | 'desc' | undefined) => {
    setSortField(field);
    setSortOrder(order);
  };

  const paginationProps = {
    current: pageNo,
    pageSize,
    total: processedData.filteredTotal,
    showTotal: (total: number) => `共 ${total} 条`,
    showSizeChanger: true,
    showQuickJumper: true
  };

  return {
    loading,
    loadTableData,
    pageNo,
    pageSize,
    sortOrder,
    sortField,
    filters,
    total: processedData.filteredTotal,
    originalTotal: total,
    setPageNo,
    setPageSize,
    setSortOrder,
    setSortField,
    setFilters,
    setFilter,
    setSort,
    resetFilters,
    resetSort,
    resetAll,
    dataSource: processedData.dataSource,
    originalData,
    handleTableChange,
    tableProps: {
      dataSource: processedData.dataSource,
      loading,
      onChange: handleTableChange,
      ...(disablePagination ? {} : {pagination: paginationProps})
    },
    paginationProps
  };
}
