import React, {useMemo, useState, useEffect} from 'react';
import Main, {workspaceFlattenedMenuList, flattenedMenuList, menus, workspaceMenus} from '../pages';
import {Suspense} from 'react';
import {createHashRouter, Navigate, RouterProvider} from 'react-router-dom';
import urls from '../utils/urls';
import {useAppContext} from '@baidu/bce-react-toolkit';
import {Loading} from 'acud';
import flags from '../flags';
import {withPermissionHoc} from './WithPermissionHoc';
import {useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import useEnv from '../hooks/useEnv';
import {playgroundInfo} from '@helpers/palyground-info';
import {checkFullControl} from '@api/auth';

const isPrivate = flags.DatabuilderPrivateSwitch;
// 仅在非私有化模式下加载激活页面组件
const Activation = !isPrivate
  ? React.lazy(() => import(/* webpackChunkName: "Activation" */ '@pages/Activation'))
  : null;

// 仅在非私有化模式下加载成都无权限页面组件
const ForbiddenChengdu = !isPrivate
  ? React.lazy(
      () =>
        import(
          /* webpackChunkName: "ForbiddenChengdu" */ '@components/WithoutPermissionPage/ForbiddenChengdu'
        )
    )
  : null;

// 主路由
const mainRouter = flattenedMenuList.map((menu) => ({
  path: menu.key,
  element: <Main menus={menus} component={menu.Component} />,
  privilege: menu.privilege
}));

// playground 用户的主路由 重定向
const playgroundMainRouter = flattenedMenuList.map((menu) => ({
  path: menu.key,
  element: (
    <Navigate
      to={`/workspace/workarea?folderId=${playgroundInfo.folderId}&workspaceId=${playgroundInfo.workspaceId}`}
      replace
    />
  ),
  privilege: menu.privilege
}));

// 创建一个包装器组件来处理懒加载
const LazyComponent = ({component: Component}: {component?: React.ComponentType<any>}) => {
  return (
    Component && (
      <Suspense
        fallback={
          <div className="db-workspace-wrapper">
            <Loading />
          </div>
        }
      >
        <Component />
      </Suspense>
    )
  );
};

// 工作空间路由 使用二级路由
const workspaceRouter = [
  {
    path: '/workspace',
    id: 'workspace',
    element: <Main menus={workspaceMenus} />,
    children: workspaceFlattenedMenuList.map((menu) => ({
      path: menu.key,
      element: withPermissionHoc(<LazyComponent component={menu.Component} />, menu.privilege),
      privilege: menu.privilege
    }))
  }
];

const RouterComponent = () => {
  const {appState, appDispatch} = useAppContext();
  const availableInChengdu = useSelector((state: IAppState) => state.globalAuthSlice.availableInChengdu);
  const {isPlayGroundUser} = useEnv();
  const hidderChengdu = !isPrivate && !availableInChengdu;

  // playground 用户管理员权限检查状态
  const [isPlaygroundAdmin, setIsPlaygroundAdmin] = useState<boolean | null>(null);
  const [adminCheckLoading, setAdminCheckLoading] = useState(false);

  // 当 isPlayGroundUser 为 true 时，检查是否为管理员
  useEffect(() => {
    if (isPlayGroundUser && isPlaygroundAdmin === null && !adminCheckLoading) {
      setAdminCheckLoading(true);
      checkFullControl(true) // silent 为 true，不展示错误提示
        .then(() => {
          // 接口正常返回代表当前登录是管理员
          setIsPlaygroundAdmin(true);
        })
        .catch(() => {
          // 接口异常代表不是管理员
          setIsPlaygroundAdmin(false);
        })
        .finally(() => {
          setAdminCheckLoading(false);
        });
    }
  }, [isPlayGroundUser, isPlaygroundAdmin, adminCheckLoading]);

  const router = useMemo(() => {
    // 如果是 playground 用户且正在检查管理员权限，显示加载状态
    if (isPlayGroundUser && adminCheckLoading) {
      return createHashRouter([
        {
          path: '*',
          element: (
            <div className="db-workspace-wrapper">
              <Loading />
            </div>
          )
        }
      ]);
    }

    const list = hidderChengdu
      ? [
          {
            path: urls.regionInvalid,
            element: <LazyComponent component={ForbiddenChengdu} />
          },
          {
            path: '*',
            element: <Navigate to={urls.regionInvalid} replace />
          }
        ]
      : [
          // 路由选择逻辑：
          // 1.如果不是 playground 用户，使用正常的主路由
          // 2.如果是 playground 用户且是管理员，使用正常的主路由
          // 3.如果是 playground 用户但不是管理员，使用重定向路由（禁止普通用户访问到空间外）
          ...(isPlayGroundUser && !isPlaygroundAdmin ? playgroundMainRouter : mainRouter),
          ...workspaceRouter,
          // TODO: 需要修改
          {
            path: '*',
            element: <Navigate to={urls.manageWorkspace} replace />
          }
        ];
    return createHashRouter(list);
  }, [appState.isActivated, hidderChengdu, isPlayGroundUser, isPlaygroundAdmin, adminCheckLoading]);

  return <RouterProvider router={router} />;
};

export default RouterComponent;
