/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

import { ReadonlyPartialJSONObject } from '@lumino/coreutils';
import { CommandRegistry } from '@lumino/commands';
import { DisposableSet } from '@lumino/disposable';
import { SessionContextDialogs } from '@jupyterlab/apputils';
import { CompletionHandler } from '@jupyterlab/completer';
import {
  NotebookActions,
  NotebookPanel,
  NotebookSearchProvider,
  NotebookTracker,
  Notebook,
} from '@jupyterlab/notebook';
import {
  SearchDocumentModel,
  SearchDocumentView,
} from '@jupyterlab/documentsearch';
import { Widget } from '@lumino/widgets';
import { nullTranslator } from '@jupyterlab/translation';
import { IYText } from '@jupyter/ydoc';
import { EditorView } from '@codemirror/view';
import { toggleComment } from '@codemirror/commands';

/**
 * The map of command ids used by the notebook.
 */
export const NotebookCommandIds = {
  changeCellType: 'notebook-cell:change-cell-type',
  changeCellTypeToCode: 'notebook-cell:change-cell-to-code',
  changeCellTypeToMarkdown: 'notebook-cell:change-cell-to-markdown',
  changeCellTypeToRaw: 'notebook-cell:change-cell-to-raw',
  commandMode: 'notebook:command-mode',
  deleteCells: 'notebook-cells:delete',
  editMode: 'notebook:edit-mode',
  extendAbove: 'notebook-cells:extend-above',
  extendBelow: 'notebook-cells:extend-below',
  extendBottom: 'notebook-cells:extend-bottom',
  extendTop: 'notebook-cells:extend-top',
  findNext: 'documentsearch:find-next',
  findPrevious: 'documentsearch:find-previous',
  insertAbove: 'notebook-cells:insert-above',
  insertBelow: 'notebook-cells:insert-below',
  interrupt: 'notebook:interrupt-kernel',
  invoke: 'completer:invoke',
  invokeNotebook: 'completer:invoke-notebook',
  merge: 'notebook-cells:merge',
  redo: 'notebook-cells:redo',
  restart: 'notebook:restart-kernel',
  run: 'notebook:run-cell',
  runAll: 'notebook:run-all',
  runAndAdvance: 'notebook-cells:run-and-advance',
  save: 'notebook:save',
  select: 'completer:select',
  selectAbove: 'notebook:move-cursor-up',
  selectBelow: 'notebook:move-cursor-down',
  selectNotebook: 'completer:select-notebook',
  split: 'notebook-cells:split',
  startSearch: 'documentsearch:start-search',
  switchKernel: 'notebook:switch-kernel',
  undo: 'notebook-cells:undo',
  clearOutputs: 'notebook:clear-outputs',
  clearAllOutputs: 'notebook:clear-all-outputs',
  copyCell: 'notebook-cells:copy',
  cutCell: 'notebook-cells:cut',
  pasteCell: 'notebook-cells:paste',
  insertBelowPro: 'notebook-cells:insert-below-pro',
  insertAbovePro: 'notebook-cells:insert-above-pro',
  runAllAbove: 'notebook:run-all-above',
  runAllBelow: 'notebook:run-all-below',
  toggleAllLineNumbers: 'notebook:toggle-all-line-numbers',
  deleteAllCells: 'notebook-cells:delete-all',
  activeCell: 'notebook:active-cell',
  toggleComment: 'notebook:toggle-comment',
  moveUp: 'notebook-cells:move-up',
  moveDown: 'notebook-cells:move-down',
};

/**
 * Register notebook commands.
 *
 * @param commands Command registry
 * @param completerHandler Completion handler
 * @param tracker Notebook tracker
 * @param path Notebook path
 * @returns Commands disposer
 */
export function addNotebookCommands(
  commands: CommandRegistry,
  completerHandler: CompletionHandler,
  tracker: NotebookTracker,
  path?: string
): DisposableSet {
  const allCommands = new DisposableSet();

  // Add commands.
  if (path) {
    allCommands.add(
      commands.addCommand(NotebookCommandIds.save, {
        label: 'Save',
        execute: () => {
          tracker.currentWidget?.context.save();
        },
      })
    );
    allCommands.add(
      commands.addKeyBinding({
        selector: '.jp-Notebook',
        keys: ['Accel S'],
        command: NotebookCommandIds.save,
      })
    );
  }

  allCommands.add(
    commands.addCommand(NotebookCommandIds.deleteAllCells, {
      label: 'Delete All Cells',
      execute: () => {
        const notebook = tracker.currentWidget?.content;
        if (notebook) {
          NotebookActions.selectAll(notebook);
          NotebookActions.deleteCells(notebook);
        }
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.insertAbovePro, {
      label: 'Insert Above',
      execute: args => {
        const notebook = tracker.currentWidget?.content;
        if (!notebook || !notebook.model) {
          return;
        }

        const state = getState(notebook);
        const model = notebook.model;

        const newIndex = notebook.activeCell ? notebook.activeCellIndex : 0;
        model.sharedModel.insertCell(newIndex, {
          cell_type:
            (args.cellType as string) || notebook.notebookConfig.defaultCell,
          metadata:
            notebook.notebookConfig.defaultCell === 'code'
              ? {
                  // This is an empty cell created by user, thus is trusted
                  trusted: true,
                  ...(args.metadata as any),
                }
              : {
                  ...(args.metadata as any),
                },
        });
        // Make the newly inserted cell active.
        notebook.activeCellIndex = newIndex;

        notebook.deselectAll();
        void handleState(notebook, state, true);
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.insertBelowPro, {
      label: 'Insert Below',
      execute: args => {
        const notebook = tracker.currentWidget?.content;
        if (!notebook || !notebook.model) {
          return;
        }

        const state = getState(notebook);
        const model = notebook.model;

        const newIndex = notebook.activeCell ? notebook.activeCellIndex + 1 : 0;
        model.sharedModel.insertCell(newIndex, {
          cell_type:
            (args.cellType as string) || notebook.notebookConfig.defaultCell,
          metadata:
            notebook.notebookConfig.defaultCell === 'code'
              ? {
                  // This is an empty cell created by user, thus is trusted
                  trusted: true,
                  ...(args.metadata as any),
                }
              : {
                  ...(args.metadata as any),
                },
        });
        // Make the newly inserted cell active.
        notebook.activeCellIndex = newIndex;

        notebook.deselectAll();
        void handleState(notebook, state, true);
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.copyCell, {
      label: 'Copy cell',
      execute: () => {
        const notebook = tracker.currentWidget?.content;
        if (notebook) {
          NotebookActions.copy(notebook);
        }
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.cutCell, {
      label: 'Cut cell',
      execute: () => {
        const notebook = tracker.currentWidget?.content;
        if (notebook) {
          NotebookActions.cut(notebook);
        }
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.pasteCell, {
      label: 'Paste cell',
      execute: () => {
        const notebook = tracker.currentWidget?.content;
        if (notebook) {
          NotebookActions.paste(notebook);
        }
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.clearOutputs, {
      label: 'Clear output',
      execute: () => {
        const notebook = tracker.currentWidget?.content;
        if (notebook) {
          NotebookActions.clearOutputs(notebook);
        }
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.clearAllOutputs, {
      label: 'Clear output',
      execute: () => {
        const notebook = tracker.currentWidget?.content;
        if (notebook) {
          NotebookActions.clearAllOutputs(notebook);
        }
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.invoke, {
      label: 'Completer: Invoke',
      execute: () => completerHandler.invoke(),
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.select, {
      label: 'Completer: Select',
      execute: () => completerHandler.completer.selectActive(),
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.invokeNotebook, {
      label: 'Invoke Notebook',
      execute: () => {
        if (tracker.currentWidget?.content.activeCell?.model.type === 'code') {
          return commands.execute(NotebookCommandIds.invoke);
        }
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.selectNotebook, {
      label: 'Select Notebook',
      execute: () => {
        if (tracker.currentWidget?.content.activeCell?.model.type === 'code') {
          return commands.execute(NotebookCommandIds.select);
        }
      },
    })
  );
  let searchInstance: SearchDocumentView | undefined;
  allCommands.add(
    commands.addCommand(NotebookCommandIds.startSearch, {
      label: 'Find…',
      execute: () => {
        if (!tracker.currentWidget) {
          searchInstance = undefined;
        } else if (!searchInstance) {
          const provider = new NotebookSearchProvider(
            tracker.currentWidget,
            nullTranslator
          );
          const searchModel = new SearchDocumentModel(provider, 500);
          searchInstance = new SearchDocumentView(searchModel);
          /**
           * Activate the target widget when the search panel is closing
           */
          searchInstance.closed.connect(() => {
            if (!tracker.currentWidget?.isDisposed) {
              tracker.currentWidget?.activate();
            }
          });
          searchInstance.disposed.connect(() => {
            if (!tracker.currentWidget?.isDisposed) {
              tracker.currentWidget?.activate();
            }
            // find next and previous are now disabled
            commands.notifyCommandChanged();
          });
          /**
           * Dispose resources when the widget is disposed.
           */
          tracker.currentWidget?.disposed.connect(() => {
            searchInstance?.dispose();
            searchModel.dispose();
            provider.dispose();
          });
        }
        if (
          searchInstance &&
          tracker.currentWidget &&
          !searchInstance.isAttached
        ) {
          Widget.attach(searchInstance, tracker.currentWidget?.node);
          searchInstance.node.style.top = `${
            tracker.currentWidget?.toolbar.node.getBoundingClientRect().height +
            tracker.currentWidget?.contentHeader.node.getBoundingClientRect()
              .height
          }px`;
          if (searchInstance.model.searchExpression) {
            searchInstance.model.refresh();
          }
        }
        searchInstance?.focusSearchInput();
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.findNext, {
      label: 'Find Next',
      isEnabled: () => !!searchInstance,
      execute: async () => {
        if (!searchInstance) {
          return;
        }
        await searchInstance.model.highlightNext();
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.findPrevious, {
      label: 'Find Previous',
      isEnabled: () => !!searchInstance,
      execute: async () => {
        if (!searchInstance) {
          return;
        }
        await searchInstance.model.highlightPrevious();
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.interrupt, {
      label: 'Interrupt',
      execute: async () =>
        tracker.currentWidget?.context.sessionContext.session?.kernel?.interrupt(),
    })
  );
  const sessionContextDialogs = new SessionContextDialogs();
  allCommands.add(
    commands.addCommand(NotebookCommandIds.restart, {
      label: 'Restart Kernel',
      execute: () => {
        if (tracker.currentWidget) {
          sessionContextDialogs.restart(
            tracker.currentWidget.context.sessionContext
          );
        }
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.switchKernel, {
      label: 'Switch Kernel',
      execute: () => {
        if (tracker.currentWidget) {
          sessionContextDialogs.selectKernel(
            tracker.currentWidget.context.sessionContext
          );
        }
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.runAndAdvance, {
      label: 'Run and Advance',
      execute: () => {
        return tracker.currentWidget
          ? NotebookActions.runAndAdvance(
              tracker.currentWidget.content,
              tracker.currentWidget.context.sessionContext
            )
          : undefined;
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.run, {
      label: 'Run',
      execute: () => {
        return tracker.currentWidget
          ? NotebookActions.run(
              tracker.currentWidget.content,
              tracker.currentWidget.context.sessionContext
            )
          : undefined;
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.runAllAbove, {
      label: 'Run All Above',
      execute: () => {
        return tracker.currentWidget
          ? NotebookActions.runAllAbove(
              tracker.currentWidget.content,
              tracker.currentWidget.context.sessionContext
            )
          : undefined;
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.runAllBelow, {
      label: 'Run All Below',
      execute: () => {
        return tracker.currentWidget
          ? NotebookActions.runAllBelow(
              tracker.currentWidget.content,
              tracker.currentWidget.context.sessionContext
            )
          : undefined;
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.runAll, {
      label: 'Run all',
      execute: () => {
        return tracker.currentWidget
          ? NotebookActions.runAll(
              tracker.currentWidget.content,
              tracker.currentWidget.context.sessionContext
            )
          : undefined;
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.deleteCells, {
      label: 'Delete Cells',
      execute: () => {
        return tracker.currentWidget
          ? NotebookActions.deleteCells(tracker.currentWidget.content)
          : undefined;
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.insertAbove, {
      label: 'Insert Above',
      execute: args => {
        const { cellType } = (args ?? {}) as any;
        const notebook = tracker.currentWidget?.content;
        if (notebook) {
          NotebookActions.insertAbove(notebook);
          if (cellType) {
            NotebookActions.changeCellType(notebook, cellType);
          }
        }
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.insertBelow, {
      label: 'Insert Below',
      execute: args => {
        const { cellType } = (args ?? {}) as any;
        const notebook = tracker.currentWidget?.content;
        if (notebook) {
          NotebookActions.insertBelow(notebook);
          if (cellType) {
            NotebookActions.changeCellType(notebook, cellType);
          }
        }
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.editMode, {
      label: 'Edit Mode',
      execute: () => {
        if (tracker.currentWidget) {
          tracker.currentWidget.content.mode = 'edit';
        }
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.commandMode, {
      label: 'Command Mode',
      execute: () => {
        if (tracker.currentWidget) {
          tracker.currentWidget.content.mode = 'command';
        }
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.selectBelow, {
      label: 'Select Below',
      execute: () =>
        tracker.currentWidget
          ? NotebookActions.selectBelow(tracker.currentWidget.content)
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.selectAbove, {
      label: 'Select Above',
      execute: () =>
        tracker.currentWidget
          ? NotebookActions.selectAbove(tracker.currentWidget.content)
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.extendAbove, {
      label: 'Extend Above',
      execute: () =>
        tracker.currentWidget
          ? NotebookActions.extendSelectionAbove(tracker.currentWidget.content)
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.extendTop, {
      label: 'Extend to Top',
      execute: () =>
        tracker.currentWidget
          ? NotebookActions.extendSelectionAbove(
              tracker.currentWidget.content,
              true
            )
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.extendBelow, {
      label: 'Extend Below',
      execute: () =>
        tracker.currentWidget
          ? NotebookActions.extendSelectionBelow(tracker.currentWidget.content)
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.extendBottom, {
      label: 'Extend to Bottom',
      execute: () =>
        tracker.currentWidget
          ? NotebookActions.extendSelectionBelow(
              tracker.currentWidget.content,
              true
            )
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.merge, {
      label: 'Merge Cells',
      execute: () =>
        tracker.currentWidget
          ? NotebookActions.mergeCells(tracker.currentWidget.content)
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.split, {
      label: 'Split Cell',
      execute: () =>
        tracker.currentWidget
          ? NotebookActions.splitCell(tracker.currentWidget.content)
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.undo, {
      label: 'Undo',
      execute: () => {
        const activeCell = tracker.currentWidget?.content.activeCell;
        if (activeCell) {
          const sharedModel = activeCell.model.sharedModel as any as IYText;
          if (sharedModel.undoManager) {
            sharedModel.undoManager.undo();
          } else {
            // Fallback to default undo if Yjs undo manager is not available
            NotebookActions.undo(tracker.currentWidget.content);
          }
        }
      },
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.redo, {
      label: 'Redo',
      execute: () => {
        const activeCell = tracker.currentWidget?.content.activeCell;
        if (activeCell) {
          const sharedModel = activeCell.model.sharedModel as any as IYText;
          if (sharedModel.undoManager) {
            sharedModel.undoManager.redo();
          } else {
            // Fallback to default redo if Yjs undo manager is not available
            NotebookActions.redo(tracker.currentWidget.content);
          }
        }
      },
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.changeCellTypeToCode, {
      label: 'Change Cell Type to Code',
      execute: args =>
        tracker.currentWidget
          ? NotebookActions.changeCellType(
              tracker.currentWidget.content,
              'code'
            )
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.changeCellTypeToMarkdown, {
      label: 'Change Cell Type to Markdown',
      execute: args =>
        tracker.currentWidget
          ? NotebookActions.changeCellType(
              tracker.currentWidget.content,
              'markdown'
            )
          : undefined,
    })
  );
  allCommands.add(
    commands.addCommand(NotebookCommandIds.changeCellTypeToRaw, {
      label: 'Change Cell Type to Raw',
      execute: args =>
        tracker.currentWidget
          ? NotebookActions.changeCellType(tracker.currentWidget.content, 'raw')
          : undefined,
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.toggleAllLineNumbers, {
      label: 'Toggle All Line Numbers',
      execute: args =>
        tracker.currentWidget
          ? NotebookActions.toggleAllLineNumbers(tracker.currentWidget.content)
          : undefined,
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.activeCell, {
      label: 'Active Cell',
      execute: (args: any) => {
        if (tracker.currentWidget) {
          const { content: notebook } = tracker.currentWidget;
          const state = getState(notebook);
          const index = notebook.widgets.findIndex(
            widget => widget.model === args.model
          );
          notebook.activeCellIndex = index;
          notebook.deselectAll();
          void handleState(notebook, state, true);
        }
      },
    })
  );

  function getCurrent(args: ReadonlyPartialJSONObject): NotebookPanel | null {
    return tracker.currentWidget;
  }
  function isEnabled(): boolean {
    return tracker.currentWidget !== null;
  }
  allCommands.add(
    commands.addCommand('run-selected-codecell', {
      label: 'Run Cell',
      execute: args => {
        const current = getCurrent(args);
        if (current) {
          const { context, content } = current;
          NotebookActions.run(content, context.sessionContext);
        }
      },
      isEnabled,
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.toggleComment, {
      label: 'Toggle Comment',
      execute: args => {
        const view = findEditorView();
        if (!view) {
          return;
        }
        toggleComment(view);
      },
      isEnabled,
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.moveUp, {
      label: 'Move Up',
      execute: args =>
        tracker.currentWidget
          ? NotebookActions.moveUp(tracker.currentWidget.content)
          : undefined,
    })
  );

  allCommands.add(
    commands.addCommand(NotebookCommandIds.moveDown, {
      label: 'Move Down',
      execute: args =>
        tracker.currentWidget
          ? NotebookActions.moveDown(tracker.currentWidget.content)
          : undefined,
    })
  );

  const bindings = [
    // {
    //   selector: '.jp-Notebook.jp-mod-editMode .jp-mod-completer-enabled',
    //   keys: ['Tab'],
    //   command: NotebookCommandIds.invokeNotebook,
    // },
    // {
    //   selector: '.jp-mod-completer-active',
    //   keys: ['Enter'],
    //   command: NotebookCommandIds.selectNotebook,
    // },
    {
      selector: '.jp-Notebook',
      keys: ['Ctrl Enter'],
      command: NotebookCommandIds.run,
    },
    {
      selector: '.jp-Notebook',
      keys: ['Shift Enter'],
      command: NotebookCommandIds.runAndAdvance,
    },
    // {
    //   selector: '.jp-Notebook',
    //   keys: ['Accel F'],
    //   command: NotebookCommandIds.startSearch,
    // },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['D', 'D'],
      command: NotebookCommandIds.deleteCells,
    },
    // {
    //   selector: '.jp-Notebook',
    //   keys: ['Accel G'],
    //   command: NotebookCommandIds.findNext,
    // },
    // {
    //   selector: '.jp-Notebook',
    //   keys: ['Accel Shift G'],
    //   command: NotebookCommandIds.findPrevious,
    // },
    // {
    //   selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
    //   keys: ['I', 'I'],
    //   command: NotebookCommandIds.interrupt,
    // },
    // {
    //   selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
    //   keys: ['0', '0'],
    //   command: NotebookCommandIds.restart,
    // },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['Enter'],
      command: NotebookCommandIds.editMode,
    },
    {
      selector: '.jp-Notebook.jp-mod-editMode',
      keys: ['Escape'],
      command: NotebookCommandIds.commandMode,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['Shift M'],
      command: NotebookCommandIds.merge,
    },
    {
      selector: '.jp-Notebook.jp-mod-editMode',
      keys: ['Ctrl Shift -'],
      command: NotebookCommandIds.split,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['J'],
      command: NotebookCommandIds.selectBelow,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['ArrowDown'],
      command: NotebookCommandIds.selectBelow,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['A'],
      command: NotebookCommandIds.insertAbove,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['B'],
      command: NotebookCommandIds.insertBelow,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['K'],
      command: NotebookCommandIds.selectAbove,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['ArrowUp'],
      command: NotebookCommandIds.selectAbove,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['Shift K'],
      command: NotebookCommandIds.extendAbove,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['Shift J'],
      command: NotebookCommandIds.extendBelow,
    },
    {
      selector: '.jp-Notebook',
      keys: ['Accel Z'],
      command: NotebookCommandIds.undo,
    },
    {
      selector: '.jp-Notebook',
      keys: ['Accel Y'],
      command: NotebookCommandIds.redo,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['C'],
      command: NotebookCommandIds.copyCell,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['X'],
      command: NotebookCommandIds.cutCell,
    },
    {
      selector: '.jp-Notebook.jp-mod-commandMode :focus:not(:read-write)',
      keys: ['V'],
      command: NotebookCommandIds.pasteCell,
    },
    // {
    //   selector: '.jp-Notebook.jp-mod-commandMode :focus',
    //   keys: ['M'],
    //   command: NotebookCommandIds.changeCellTypeToMarkdown,
    // },
    // {
    //   selector: '.jp-Notebook.jp-mod-commandMode :focus',
    //   keys: ['R'],
    //   command: NotebookCommandIds.changeCellTypeToRaw,
    // },
    // {
    //   selector: '.jp-Notebook.jp-mod-commandMode :focus',
    //   keys: ['Y'],
    //   command: NotebookCommandIds.changeCellTypeToCode,
    // },
    {
      selector: '.cm-content',
      keys: ['Accel /'],
      command: NotebookCommandIds.toggleComment,
    },
  ];
  bindings.forEach(binding => allCommands.add(commands.addKeyBinding(binding)));

  return allCommands;
}

async function handleState(
  notebook: Notebook,
  state: { wasFocused: boolean; activeCellId: string | null },
  scrollIfNeeded = false
): Promise<void> {
  const { activeCell, activeCellIndex } = notebook;
  if (scrollIfNeeded && activeCell) {
    await notebook.scrollToItem(activeCellIndex, 'auto', 0).catch(reason => {
      // no-op
    });
  }
  if (state.wasFocused || notebook.mode === 'edit') {
    notebook.activate();
  }
}

function getState(notebook: Notebook): {
  wasFocused: boolean;
  activeCellId: string | null;
} {
  return {
    wasFocused: notebook.node.contains(document.activeElement),
    activeCellId: notebook.activeCell?.model.id ?? null,
  };
}

/**
 * Selector for CodeMirror editor with `cmView` attribute.
 */
const CODE_MIRROR_SELECTOR = '.cm-content';
const findEditorView = () => {
  const node = document.activeElement?.closest(CODE_MIRROR_SELECTOR);
  if (!node) {
    return;
  }
  if (!('cmView' in node)) {
    return;
  }
  return (node.cmView as any).view as EditorView;
};

export default addNotebookCommands;
